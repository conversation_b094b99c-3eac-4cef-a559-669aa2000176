# Genesis Portal project

## Requirements
- PHP 8.3
- MySql 8 or 9

## Development environment setup

```bash
<NAME_EMAIL>:swebvn/genesis-portal.git
cd genesis-portal

cp .env.example .env

# Create database for logging
touch database/logging.sqlite

# Install dependencies
composer install

# Generate application key
php artisan key:generate

# Migrate the database
php artisan migrate --seed
```

## Run tests

```bash
php artisan test
```

## Dev

Create dummy suppliers shipment file

```bash
php artisan make:dummy-suppliers-shipment-file
```

Create dummy tracking shipment file

```bash
php artisan make:dummy-tracking-shipment-file
```

## Environemnt

We have 2 environment: dev and prod.

**Dev**

The branch `dev` is deployed to
https://genesis-dev.tdagroup.online/

**Prod**

The branch `main` is deployed to
https://genesis.tdagroup.co/

**Git workflow**
Never merge `dev` into `main` directly unless there is no choice. Working on your own branch, and merge it into `dev` first. Then merge that branch into `main`. Create pull if you not confident enough to merge it yourself.

