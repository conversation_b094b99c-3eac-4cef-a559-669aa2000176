<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qc_line_item_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('qc_order_check_id')->constrained('qc_order_checks')->cascadeOnDelete();
            $table->foreignId('line_item_id')->constrained('line_items');
            $table->string('status', 32)->nullable();
            $table->timestamp('checked_at')->nullable();
            $table->timestamps();

            $table->unique(['qc_order_check_id', 'line_item_id']);
            $table->index(['qc_order_check_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qc_line_item_checks');
    }
};
