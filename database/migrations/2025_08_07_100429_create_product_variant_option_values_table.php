<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variant_option_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_variant_id');
            $table->unsignedBigInteger('product_type_option_value_id');
            $table->timestamps();

            $table->foreign('product_variant_id', 'pv_opt_val_variant_fk')
                ->references('id')->on('product_variants')->cascadeOnDelete();
            $table->foreign('product_type_option_value_id', 'pv_opt_val_value_fk')
                ->references('id')->on('product_type_option_values')->cascadeOnDelete();

            $table->unique(['product_variant_id', 'product_type_option_value_id'], 'pv_option_values_unique');
            $table->index('product_variant_id', 'pv_opt_values_variant_idx');
            $table->index('product_type_option_value_id', 'pv_opt_values_value_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variant_option_values');
    }
};
