<?php

use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('number_portal', 32)->nullable()->unique();
            $table->foreignId('seller_id')->nullable()->constrained('users');
            $table->foreignId('backup_seller_id')->nullable()->constrained('users');
            $table->foreignId('orders_import_request_id')->nullable()->constrained('orders_import_requests');

            $table->string('order_number', 64)->index();
            $table->timestamp('order_date')->nullable()->comment('Date when the order was placed');
            $table->string('platform', 32)->nullable();
            $table->unsignedBigInteger('id_platform')->nullable()->index()->comment('ID from the platform system, e.g., Etsy, Amazon');
            $table->unsignedBigInteger('shop_id_platform')->nullable()->index()->comment('Shop ID from the platform system, e.g., Etsy shop ID');
            $table->string('status', 32)->default(OrderStatus::Draft->value);

            $table->boolean('is_gift')->default(false);
            $table->boolean('is_gift_wrapped')->default(false);
            $table->text('gift_message')->nullable();

            $table->string('currency_code', 6)->nullable();
            $table->unsignedBigInteger('total')->default(0);
            $table->unsignedBigInteger('subtotal')->default(0);
            $table->unsignedBigInteger('shipping_total')->default(0);
            $table->unsignedBigInteger('tax_total')->default(0);
            $table->unsignedBigInteger('discount_total')->default(0);
            $table->json('cost_breakdown')->nullable()->comment('JSON containing detailed cost breakdown');

            $table->dateTime('date_paid')->nullable()->index();
            $table->string('payment_method', 64)->nullable()->comment('Payment method used for the order');
            $table->dateTime('expected_ship_date')->nullable()->comment('Expected shipping date for the order');
            $table->timestamp('seller_confirmed_at')->nullable()->comment('Timestamp when the seller confirmed the order');

            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->text('customer_note')->nullable();
            $table->text('seller_note')->nullable();

            $table->json('meta_data')->nullable()->comment('Additional metadata related to the order');
            $table->text('private_note')->nullable();

            $table->timestamps();

            $table->unique(['id_platform', 'shop_id_platform']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
