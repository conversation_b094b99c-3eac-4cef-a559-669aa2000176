<?php

use App\Enums\TransportHandoverStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transport_handovers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('third_party_logistic_id')
                ->constrained('third_party_logistics')
                ->comment('3PL provider for this handover');
            $table->text('tracking_numbers')
                ->comment('Original tracking numbers input from user');
            $table->text('notes')->nullable()->comment('Optional notes for the handover');
            $table->string('status', 32)
                ->default(TransportHandoverStatus::Draft->value)
                ->comment('Handover status: draft, confirmed');
            $table->timestamp('confirmed_at')->nullable()->comment('When the handover was confirmed');
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transport_handovers');
    }
};
