<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('third_party_logistics', function (Blueprint $table) {
            $table->id();
            $table->string('name', 128)->unique()->comment('3PL provider name (e.g., "Hongkong Post")');
            $table->string('code', 16)->unique()->comment('Short code for 3PL provider (e.g., "HP")');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('third_party_logistics');
    }
};
