<?php

use App\Enums\DesignStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('designs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('idea_id')->constrained('ideas');
            $table->foreignId('product_type_id')->constrained('product_types');
            $table->text('source_file_urls')->nullable();
            $table->foreignId('user_id')->nullable()->constrained('users')->comment('Designer who created this design');
            $table->string('status', 32)->default(DesignStatus::Todo->value);
            $table->timestamp('assigned_at')->nullable()->comment('When the design was assigned to a designer');
            $table->timestamp('approved_at')->nullable()->comment('When the design was approved');
            $table->timestamps();

            $table->unique(['idea_id', 'product_type_id'], 'idea_product_type_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('designs');
    }
};
