<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('listing_urls', function (Blueprint $table) {
            $table->id();
            $table->string('link');
            $table->string('link_without_query_string')->nullable()->index();
            $table->string('domain')->nullable();
            $table->unsignedBigInteger('idea_id');
            $table->unsignedBigInteger('design_id')->nullable();
            $table->timestamps();

            $table->unique(['link', 'idea_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('listing_urls');
    }
};
