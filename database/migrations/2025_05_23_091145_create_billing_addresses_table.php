<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained();
            $table->string('id_platform', 64)->nullable();
            $table->string('name', 128)->nullable();
            $table->string('line_one', 512)->nullable();
            $table->string('line_two')->nullable();
            $table->string('city', 64)->nullable();
            $table->string('state', 32)->nullable();
            $table->string('zip', 16)->nullable();
            $table->string('country', 32)->nullable();
            $table->string('phone', 32)->nullable();
            $table->string('email', 64)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_addresses');
    }
};
