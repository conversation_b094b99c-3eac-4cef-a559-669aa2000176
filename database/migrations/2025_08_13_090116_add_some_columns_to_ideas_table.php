<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ideas', function (Blueprint $table) {
            $table->boolean('is_personalized')->default(false);
            $table->string('personalized_description')->nullable();
            $table->boolean('is_made_by_ai')->default(false);
            $table->string('made_by_ai_description')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ideas', function (Blueprint $table) {
            $table->dropColumn([
                'is_personalized',
                'personalized_description',
                'is_made_by_ai',
                'made_by_ai_description',
            ]);
        });
    }
};
