<?php

use App\Enums\LineItemStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('line_items', function (Blueprint $table) {
            $table->id();
            $table->string('number_portal', 32)->nullable()->unique();
            $table->unsignedBigInteger('id_platform')->nullable()->index();
            $table->foreignId('order_id')->constrained();
            $table->string('product_name', 1024);
            $table->string('product_image_url', 512)->nullable();
            $table->string('sku')->nullable()->index();
            $table->unsignedInteger('quantity')->default(1);
            $table->unsignedBigInteger('price')->default(0);
            $table->string('currency_code', 6)->nullable();
            $table->unsignedBigInteger('subtotal')->default(0);
            $table->boolean('is_download')->default(false);
            $table->boolean('is_personalizable')->default(false);
            $table->unsignedBigInteger('etsy_listing_id')->nullable();
            $table->json('variations')->nullable();
            $table->string('product_type', 128)->nullable();
            $table->json('meta_data')->nullable();
            $table->text('private_note')->nullable();
            $table->unsignedBigInteger('production_total')->default(0)->comment('Total production cost in cents');
            $table->unsignedBigInteger('packaging_total')->default(0);
            $table->unsignedBigInteger('shipping_total')->default(0);
            $table->foreignId('supplier_id')->nullable()->constrained('suppliers');
            $table->string('supplier_order_number', 64)->nullable();
            $table->string('design_file_url', 512)->nullable();
            $table->foreignId('shipment_id')->nullable()->constrained('shipments');
            $table->string('status', 32)->default(LineItemStatus::New->value);
            $table->text('seller_note')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('line_items');
    }
};
