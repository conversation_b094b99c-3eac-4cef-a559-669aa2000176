<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('platform', 32)->nullable();
            $table->string('id_platform', 64)->nullable()->index();
            $table->string('name');
            $table->string('email', 128)->index();
            $table->string('username', 64)->nullable();
            $table->string('phone', 32)->nullable()->index();
            $table->string('avatar_url')->nullable();
            $table->timestamps();

            $table->unique(['id_platform', 'platform']);
            $table->unique(['email', 'id_platform']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
