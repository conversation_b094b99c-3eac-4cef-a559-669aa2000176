<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ideas', function (Blueprint $table) {
            $table->id();
            $table->string('uid', 32)->unique()->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('idea_file_urls')->nullable();
            $table->string('thumbnail')->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('ideas');
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->string('status', 32)->default('draft');
            $table->text('listing_urls')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ideas');
    }
};
