<?php

use App\Enums\ShipmentStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('third_party_logistic_id')
                ->nullable()
                ->constrained('third_party_logistics');
            $table->foreignId('carrier_id')->constrained('carriers');

            // Tracking information
            $table->string('tracking_number', 128)->index();

            // Status and timeline
            $table->string('status', 32)->default(ShipmentStatus::Pending->value);
            $table->dateTime('shipped_at')->nullable()->comment('When the shipment was actually shipped');
            $table->date('expected_delivery_date')->nullable();
            $table->dateTime('delivered_at')->nullable()->comment('When the shipment was delivered');

            // Physical properties
            $table->unsignedInteger('weight')->nullable()->comment('Weight in grams');
            $table->unsignedInteger('length')->nullable()->comment('Length in cm');
            $table->unsignedInteger('width')->nullable()->comment('Width in cm');
            $table->unsignedInteger('height')->nullable()->comment('Height in cm');

            // Cost information
            $table->unsignedBigInteger('shipping_total')->default(0)->comment('Shipping cost in cents');
            $table->string('currency_code', 6)->nullable();

            // Platform integration
            $table->string('label_url', 512)->nullable()->comment('URL to shipping label');
            $table->string('packer', 16)->nullable()->comment('Packer code (TDA, SUP, etc.)');

            $table->timestamps();

            $table->unique(['carrier_id', 'tracking_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
