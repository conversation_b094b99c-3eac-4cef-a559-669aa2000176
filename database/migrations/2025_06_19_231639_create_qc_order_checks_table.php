<?php

use App\Enums\QcOrderCheckStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qc_order_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('qc_session_id')->constrained('qc_sessions')->cascadeOnDelete();
            $table->foreignId('order_id')->constrained('orders');
            $table->string('status', 32)->default(QcOrderCheckStatus::Processing->value);
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamps();

            $table->unique(['qc_session_id', 'order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qc_order_checks');
    }
};
