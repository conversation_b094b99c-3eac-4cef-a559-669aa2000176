<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_lines', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained();
            $table->string('type', 128);
            $table->string('code', 64)->index();
            $table->float('percentage', 5, 2)->default(0)->comment('Percentage discount applied by the coupon');
            $table->dateTime('end_date')->nullable()->comment('Expiration date of the coupon');

            $table->timestamps();

            $table->unique(['order_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_lines');
    }
};
