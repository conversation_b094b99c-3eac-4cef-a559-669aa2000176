<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedBigInteger('to_processing_by_id')->nullable();
            $table->unsignedBigInteger('to_partial_shipped_by_id')->nullable();
            $table->unsignedBigInteger('to_shipped_by_id')->nullable();
            $table->unsignedBigInteger('to_cancelled_by_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['to_processing_by_id', 'to_partial_shipped_by_id', 'to_shipped_by_id', 'to_cancelled_by_id']);
        });
    }
};
