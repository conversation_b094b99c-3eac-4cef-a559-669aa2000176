<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('etsy_shops', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_etsy');
            $table->string('username', 128);
            $table->timestamps();

            $table->unique(['id_etsy', 'username']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('etsy_shops');
    }
};
