<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained();
            $table->string('name', 128);
            $table->string('line_one', 512);
            $table->string('line_two')->nullable();
            $table->string('city', 64);
            $table->string('state', 32)->nullable();
            $table->string('zip', 16);
            $table->string('country', 32);
            $table->string('phone', 32)->nullable();
            $table->string('email', 64)->nullable();
            $table->boolean('is_usps_verified')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_addresses');
    }
};
