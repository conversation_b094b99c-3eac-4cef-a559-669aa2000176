<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('idea_id')->nullable()->constrained('ideas');
            $table->foreignId('product_type_id')->nullable()->constrained('product_types');
            $table->string('thumbnail')->nullable();
            $table->string('vid', 32)->nullable()->unique();
            $table->string('sku', '64')->nullable()->unique();
            $table->unsignedBigInteger('stock_quantity')->nullable();
            $table->boolean('is_out_of_stock')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
