<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('product_type_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_type_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->boolean('is_required')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique(['product_type_id', 'name'], 'pt_options_type_name_unique');
            $table->index(['product_type_id', 'sort_order'], 'pt_options_type_sort_index');
        });

        Schema::create('product_type_option_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_type_option_id')->constrained()->cascadeOnDelete();
            $table->string('value');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique(['product_type_option_id', 'value'], 'pto_values_option_value_unique');
            $table->index(['product_type_option_id', 'sort_order'], 'pto_values_option_sort_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_type_option_values');
        Schema::dropIfExists('product_type_options');
        Schema::dropIfExists('product_types');
    }
};
