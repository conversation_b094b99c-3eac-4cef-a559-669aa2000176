<?php

namespace Database\Seeders;

use App\Models\ThirdPartyLogistic;
use Illuminate\Database\Seeder;

class ThirdPartyLogisticSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'name' => 'Viettel Post',
                'code' => 'VTP',
            ],
            [
                'name' => 'Vietnam Post',
                'code' => 'VNP',
            ],
            [
                'name' => '<PERSON>ia<PERSON> (GHN)',
                'code' => 'GHN',
            ],
            [
                'name' => '<PERSON>ia<PERSON> (GHTK)',
                'code' => 'GHTK',
            ],
            [
                'name' => 'J&T Express Vietnam',
                'code' => 'JT',
            ],
            [
                'name' => 'Best Express Vietnam',
                'code' => 'BEST',
            ],
            [
                'name' => 'Ninja Van Vietnam',
                'code' => 'NINJA',
            ],
            [
                'name' => 'Kerry Express Vietnam',
                'code' => 'KERRY',
            ],
        ];

        // Add timestamps to all records
        $now = now();
        $providersWithTimestamps = array_map(function ($provider) use ($now) {
            return array_merge($provider, [
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }, $providers);

        ThirdPartyLogistic::upsert(
            $providersWithTimestamps,
            ['code'], // Unique key for conflict detection
            ['name', 'updated_at'] // Columns to update if conflict
        );
    }
}
