<?php

namespace Database\Seeders;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\Customer;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\ShippingAddress;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Order::factory()
            ->count(10)
            ->state(['status' => OrderStatus::New])
            ->has(Customer::factory())
            ->has(ShippingAddress::factory())
            ->has(LineItem::factory()->count(2)->withStatus(LineItemStatus::New))
            ->create([
                'seller_id' => 5, // test seller
            ]);
    }
}
