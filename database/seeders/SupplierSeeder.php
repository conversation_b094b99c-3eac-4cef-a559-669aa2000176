<?php

namespace Database\Seeders;

use App\Models\Supplier;
use Illuminate\Database\Seeder;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suppliers = [
            ['name' => 'Aliexpress', 'code' => 'ALIEXPRESS'],
            ['name' => 'Taobao', 'code' => 'TBO'],
            ['name' => 'Zhuxiang', 'code' => 'ZHUXIANG'],
            ['name' => 'BurgerPrint', 'code' => 'BURGER'],
            ['name' => 'Lenful', 'code' => 'LENFUL'],
            ['name' => 'Merchize', 'code' => 'MERCHIZE'],
            ['name' => 'Lionnix', 'code' => 'LIONNIX'],
            ['name' => 'Echo', 'code' => 'ECHO'],
            ['name' => 'Fairy Mai', 'code' => 'FAIRYMAI'],
            ['name' => 'Yiwang', 'code' => 'YIWANG'],
            ['name' => 'Printify', 'code' => 'PRINTIFY'],
            ['name' => 'Asendia', 'code' => 'ASENDIA'],
            ['name' => 'Flash', 'code' => 'FLASH'],
            ['name' => 'Keycap', 'code' => 'KEYCAP'],
            ['name' => 'Zootopbear', 'code' => 'ZOOTOPBEAR'],
            ['name' => 'Catkiss', 'code' => 'CATKISS'],
            ['name' => 'Otaku4Go', 'code' => 'OTAKU4GO'],
            ['name' => 'Other', 'code' => 'OTHER'],
            ['name' => 'OnePrint', 'code' => 'ONEPRINT'],
        ];

        // Add timestamps to all records
        $now = now();
        $suppliersWithTimestamps = array_map(function ($supplier) use ($now) {
            return array_merge($supplier, [
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }, $suppliers);

        Supplier::upsert(
            $suppliersWithTimestamps,
            ['code'], // Unique key for conflict detection
            ['name', 'updated_at'] // Columns to update if conflict
        );
    }
}
