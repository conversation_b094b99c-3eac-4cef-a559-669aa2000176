<?php

namespace Database\Seeders;

use App\Actions\AddOrderToQc;
use App\Actions\StartQcSession;
use App\Enums\QcLineItemStatus;
use App\Enums\QcSessionStatus;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Seeder;

class QcSessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get a user (operator)
        $user = User::where('role', 'operator')->first() ?? User::first();

        if (! $user) {
            $this->command->error('No users found. Please seed users first.');

            return;
        }

        // Create a QC session using the action
        $qcSession = StartQcSession::run($user);

        // Update to in progress manually for demo data
        $qcSession->update([
            'status' => QcSessionStatus::Processing->value,
            'started_at' => now()->subHours(2),
        ]);

        // Get some processing orders
        $processingOrders = Order::where('status', 'processing')
            ->with('lineItems')
            ->limit(3)
            ->get();

        foreach ($processingOrders as $order) {
            // Add order to QC using the action
            $orderCheck = AddOrderToQc::run($qcSession, $order);

            // Update line item statuses for demo data
            foreach ($orderCheck->lineItemChecks as $lineItemCheck) {
                $lineItemCheck->update([
                    'status' => fake()->randomElement([
                        QcLineItemStatus::Ready->value,
                        QcLineItemStatus::Missing->value,
                    ]),
                    'checked_at' => now()->subMinutes(rand(10, 120)),
                ]);
            }
        }

        $this->command->info("Created QC session with {$processingOrders->count()} orders for user: {$user->name}");
    }
}
