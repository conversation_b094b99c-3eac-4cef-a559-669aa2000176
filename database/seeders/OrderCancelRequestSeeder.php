<?php

namespace Database\Seeders;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Database\Seeder;

class OrderCancelRequestSeeder extends Seeder
{
    public function run(): void
    {
        // Get some existing orders and users
        $sellers = User::where('role', 'seller')->limit(3)->get();
        $operators = User::where('role', 'operator')->limit(2)->get();

        if ($sellers->isEmpty() || $operators->isEmpty()) {
            $this->command->warn('No sellers or operators found. Please seed users first.');

            return;
        }

        // Get some orders that can have cancel requests
        $orders = Order::whereIn('status', [OrderStatus::New, OrderStatus::Processing])
            ->whereIn('seller_id', $sellers->pluck('id'))
            ->limit(10)
            ->get();

        if ($orders->isEmpty()) {
            $this->command->warn('No suitable orders found for cancel requests.');

            return;
        }

        // Create pending cancel requests
        foreach ($orders->take(5) as $order) {
            OrderCancelRequest::factory()
                ->pending()
                ->create([
                    'order_id' => $order->id,
                    'seller_id' => $order->seller_id,
                ]);
        }

        // Create some processed cancel requests
        foreach ($orders->skip(5)->take(3) as $order) {
            OrderCancelRequest::factory()
                ->approved()
                ->create([
                    'order_id' => $order->id,
                    'seller_id' => $order->seller_id,
                    'processed_by' => $operators->random()->id,
                ]);
        }

        foreach ($orders->skip(8)->take(2) as $order) {
            OrderCancelRequest::factory()
                ->rejected()
                ->create([
                    'order_id' => $order->id,
                    'seller_id' => $order->seller_id,
                    'processed_by' => $operators->random()->id,
                ]);
        }

        $this->command->info('Created cancel requests for demonstration.');
    }
}
