<?php

namespace Database\Seeders;

use App\Models\Idea;
use App\Models\ProductType;
use Illuminate\Database\Seeder;

class IdeaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ideasData = [
            [
                'name' => 'Summer Vibes',
                'description' => 'Tropical summer idea with palm trees and sunset colors',
                'idea_file_urls' => "https://example.com/ideas/summer-vibes-main.svg\nhttps://example.com/ideas/summer-vibes-alt.png",
                'product_types' => ['T-Shirt', 'Mug'],
            ],
            [
                'name' => 'Vintage Logo Collection',
                'description' => 'Classic vintage-style logos and typography',
                'idea_file_urls' => "https://example.com/ideas/vintage-logo-1.svg\nhttps://example.com/ideas/vintage-logo-2.svg\nhttps://example.com/ideas/vintage-logo-3.svg",
                'product_types' => ['T-Shirt'],
            ],
            [
                'name' => 'Abstract Geometric',
                'description' => 'Modern abstract geometric patterns',
                'idea_file_urls' => 'https://example.com/ideas/abstract-geo-main.svg',
                'product_types' => ['T-Shirt', 'Mug'],
            ],
            [
                'name' => 'Minimalist Nature',
                'description' => 'Simple, clean nature-inspired ideas',
                'idea_file_urls' => "https://example.com/ideas/nature-minimal-1.svg\nhttps://example.com/ideas/nature-minimal-2.svg",
                'product_types' => ['T-Shirt', 'Mug'],
            ],
        ];

        foreach ($ideasData as $ideaData) {
            $productTypeNames = $ideaData['product_types'];
            unset($ideaData['product_types']);

            $idea = Idea::create($ideaData);

            // Attach product types
            $productTypes = ProductType::whereIn('name', $productTypeNames)->get();
            $idea->productTypes()->attach($productTypes);
        }

        // Create child ideas (variations)
        $parentIdea = Idea::where('name', 'Summer Vibes')->first();
        if ($parentIdea) {
            $childIdea1 = Idea::create([
                'name' => 'Summer Vibes - Dark Mode',
                'description' => 'Dark version of the Summer Vibes idea',
                'idea_file_urls' => 'https://example.com/ideas/summer-vibes-dark.svg',
                'parent_id' => $parentIdea->id,
            ]);

            $childIdea2 = Idea::create([
                'name' => 'Summer Vibes - Minimal',
                'description' => 'Minimal version of the Summer Vibes idea',
                'idea_file_urls' => 'https://example.com/ideas/summer-vibes-minimal.svg',
                'parent_id' => $parentIdea->id,
            ]);

            // Attach same product types as parent
            $childIdea1->productTypes()->attach($parentIdea->productTypes);
            $childIdea2->productTypes()->attach($parentIdea->productTypes);
        }
    }
}
