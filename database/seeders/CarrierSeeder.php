<?php

namespace Database\Seeders;

use App\Models\Carrier;
use Illuminate\Database\Seeder;

class CarrierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $carriers = [
            ['name' => 'USPS', 'tracking_url' => 'https://tools.usps.com/go/TrackConfirmAction?tLabels={tracking_number}'],
            ['name' => 'UPS', 'tracking_url' => 'https://www.ups.com/track?loc=en_US&tracknum={tracking_number}'],
            ['name' => 'FedEx', 'tracking_url' => 'https://www.fedex.com/fedextrack/?trknbr={tracking_number}'],
            ['name' => 'DHL', 'tracking_url' => 'https://www.dhl.com/en/express/tracking.html?AWB={tracking_number}'],
            ['name' => 'YUN', 'tracking_url' => 'https://www.yunexpress.com/Track/Index/{tracking_number}'],
            ['name' => 'China Post', 'tracking_url' => 'http://www.chinapost.com.cn/n/banli/ckfw/yjcx/?number={tracking_number}'],
            ['name' => 'SF Express', 'tracking_url' => 'https://www.sf-express.com/chn/sc/dynamic_function/waybill/#search/bill-number/{tracking_number}'],
            ['name' => 'Other', 'tracking_url' => null],
        ];

        // Add timestamps to all records
        $now = now();
        $carriersWithTimestamps = array_map(function ($carrier) use ($now) {
            return array_merge($carrier, [
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }, $carriers);

        Carrier::upsert(
            $carriersWithTimestamps,
            ['name'], // Unique key for conflict detection
            ['tracking_url', 'updated_at'] // Columns to update if conflict
        );
    }
}
