<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class DatabaseSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            UserSeeder::class,
            EtsyShopSeeder::class,
        ]);

        if (! App::isProduction()) {
            $this->call([
                SupplierSeeder::class,
                CarrierSeeder::class,
                ThirdPartyLogisticSeeder::class,
                ProductTypeSeeder::class,
            ]);
        }

        if (App::isLocal()) {
            $this->call([
                OrderSeeder::class,
            ]);
        }
    }
}
