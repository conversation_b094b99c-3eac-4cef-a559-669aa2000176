<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Nguyen Viet',
            'email' => '<EMAIL>',
            'password' => '$2y$12$yl0iQdNBfXX3NFHQJzylKOgXhI/g8w9YGFpbf6QjourM6P0P7X9GC',
            'role' => UserRole::Manager,
        ]);

        User::create([
            'name' => 'Tung',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(7)),
            'role' => UserRole::Manager,
        ]);

        User::create([
            'name' => 'Cong Quyen',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(7)),
            'role' => UserRole::Manager,
        ]);

        User::create([
            'name' => 'Phuong Anh',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(7)),
            'role' => UserRole::Manager,
        ]);

        $seller = User::create([
            'name' => 'Demo seller',
            'email' => '<EMAIL>',
            'password' => '123456',
            'role' => UserRole::Seller,
        ]);

        $operator = User::create([
            'name' => 'Demo Fullfill',
            'email' => '<EMAIL>',
            'password' => '123456',
            'role' => UserRole::Operator,
        ]);

        $designer = User::create([
            'name' => 'Demo Designer',
            'email' => '<EMAIL>',
            'password' => '123456',
            'role' => UserRole::Designer,
        ]);

        User::create([
            'name' => 'Duc Anh',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(7)),
            'role' => UserRole::Manager,
        ]);

        $seller->tokens()->create([
            'name' => 'Default',
            'token' => hash('sha256', 'meocua'),
            'abilities' => ['*'],
            'expires_at' => null,
        ]);
    }
}
