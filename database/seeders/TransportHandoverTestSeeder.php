<?php

namespace Database\Seeders;

use App\Models\Carrier;
use App\Models\Shipment;
use App\Models\ThirdPartyLogistic;
use Illuminate\Database\Seeder;

class TransportHandoverTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some 3PL providers
        $viettelPost = ThirdPartyLogistic::firstOrCreate([
            'name' => 'Viettel Post',
            'code' => 'VTP',
        ]);

        $ghn = ThirdPartyLogistic::firstOrCreate([
            'name' => 'Giao Hàng Nhanh (GHN)',
            'code' => 'GHN',
        ]);

        // Create carriers
        $carrier1 = Carrier::firstOrCreate([
            'name' => 'Viettel Post Carrier',
            'tracking_url' => 'https://viettelpost.com.vn/tra-cuu/{tracking_number}',
        ]);

        $carrier2 = Carrier::firstOrCreate([
            'name' => 'GHN Carrier',
            'tracking_url' => 'https://ghn.vn/tracking/{tracking_number}',
        ]);

        // Create test shipments
        $testShipments = [
            ['tracking_number' => 'VTP123456789', 'carrier_id' => $carrier1->id, 'third_party_logistic_id' => $viettelPost->id],
            ['tracking_number' => 'VTP987654321', 'carrier_id' => $carrier1->id, 'third_party_logistic_id' => $viettelPost->id],
            ['tracking_number' => 'GHN111222333', 'carrier_id' => $carrier2->id, 'third_party_logistic_id' => $ghn->id],
            ['tracking_number' => 'GHN444555666', 'carrier_id' => $carrier2->id, 'third_party_logistic_id' => $ghn->id],
            ['tracking_number' => 'TEST123ABC', 'carrier_id' => $carrier1->id, 'third_party_logistic_id' => $viettelPost->id],
        ];

        foreach ($testShipments as $shipmentData) {
            Shipment::firstOrCreate(
                ['tracking_number' => $shipmentData['tracking_number']],
                [
                    'carrier_id' => $shipmentData['carrier_id'],
                    'third_party_logistic_id' => $shipmentData['third_party_logistic_id'],
                    'status' => 'pending',
                    'shipping_total' => 50000, // 500 USD in cents
                    'currency_code' => 'USD',
                ]
            );
        }

        $this->command->info('Created test data for Transport Handover feature:');
        $this->command->info('- 2 Third Party Logistics providers');
        $this->command->info('- 2 Carriers');
        $this->command->info('- 5 Test shipments with tracking numbers');
        $this->command->info('');
        $this->command->info('Test tracking numbers you can use:');
        $this->command->info('Valid: VTP123456789, VTP987654321, GHN111222333, GHN444555666, TEST123ABC');
        $this->command->info('Invalid: INVALID123, NOTFOUND456, FAKE789');
    }
}
