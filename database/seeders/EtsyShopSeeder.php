<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EtsyShopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Read JSON data
        $jsonPath = database_path('data/etsy_shops.json');
        $jsonData = json_decode(file_get_contents($jsonPath), true);

        // Prepare data for bulk insert
        $shopsData = [];

        foreach ($jsonData as $shop) {
            // Skip records with blank/null id_etsy
            if (empty($shop['id_etsy'])) {
                continue;
            }

            $shopsData[] = [
                'id_etsy' => $shop['id_etsy'],
                'username' => $shop['username'],
            ];
        }

        // Use bulk insert for better performance
        // Split into chunks to avoid memory issues with large datasets
        $chunks = array_chunk($shopsData, 500);

        foreach ($chunks as $chunk) {
            DB::table('etsy_shops')->insert($chunk);
        }
    }
}
