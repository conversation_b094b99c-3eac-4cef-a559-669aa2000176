<?php

namespace Database\Seeders;

use App\Actions\CreateProductTypeOption;
use App\Models\ProductType;
use Illuminate\Database\Seeder;

class ProductTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $productTypesData = [
            [
                'name' => 'T-Shirt',
                'description' => 'Cotton t-shirts for custom printing',
                'is_active' => true,
                'options' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'is_required' => true,
                        'values' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                    ],
                    [
                        'name' => 'Color',
                        'sort_order' => 2,
                        'is_required' => true,
                        'values' => ['Black', 'White', 'Navy', 'Gray', 'Red'],
                    ],
                ],
            ],
            [
                'name' => 'Mug',
                'description' => 'Ceramic mugs for custom printing',
                'is_active' => true,
                'options' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'is_required' => true,
                        'values' => ['11oz', '15oz', '20oz'],
                    ],
                    [
                        'name' => 'Color',
                        'sort_order' => 2,
                        'is_required' => true,
                        'values' => ['White', 'Black', 'Blue'],
                    ],
                ],
            ],
            [
                'name' => 'Suncatcher',
                'description' => 'Acrylic suncatchers for custom designs',
                'is_active' => true,
                'options' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'is_required' => true,
                        'values' => ['4"', '6"', '8"', '10"'],
                    ],
                    [
                        'name' => 'Shape',
                        'sort_order' => 2,
                        'is_required' => true,
                        'values' => ['Circle', 'Square', 'Heart', 'Star'],
                    ],
                ],
            ],
            [
                'name' => 'Hoodie',
                'description' => 'Cotton blend hoodies for custom printing',
                'is_active' => true,
                'options' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'is_required' => true,
                        'values' => ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
                    ],
                    [
                        'name' => 'Color',
                        'sort_order' => 2,
                        'is_required' => true,
                        'values' => ['Black', 'White', 'Gray', 'Navy', 'Burgundy', 'Forest Green'],
                    ],
                ],
            ],
            [
                'name' => 'Poster',
                'description' => 'High-quality paper posters for custom printing',
                'is_active' => true,
                'options' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'is_required' => true,
                        'values' => ['8x10"', '11x14"', '16x20"', '18x24"', '24x36"'],
                    ],
                    [
                        'name' => 'Paper Type',
                        'sort_order' => 2,
                        'is_required' => true,
                        'values' => ['Matte', 'Glossy', 'Satin'],
                    ],
                ],
            ],
        ];

        foreach ($productTypesData as $productTypeData) {
            $options = $productTypeData['options'];
            unset($productTypeData['options']);

            $productType = ProductType::updateOrCreate(
                ['name' => $productTypeData['name']],
                $productTypeData
            );

            // Clear existing options to avoid duplicates
            $productType->options()->each(function ($option) {
                $option->values()->delete();
                $option->delete();
            });

            foreach ($options as $optionData) {
                CreateProductTypeOption::make()->handle(
                    $productType,
                    $optionData['name'],
                    $optionData['values'] ?? [],
                    $optionData['is_required'] ?? false
                );
            }
        }
    }
}
