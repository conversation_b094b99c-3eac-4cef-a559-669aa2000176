<?php

namespace Database\Factories;

use App\Enums\Platform;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderImportRequest>
 */
class OrdersImportRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'platform' => Platform::Etsy,
            'raw_data' => [
                'orders_search' => [
                    'type' => 'Orders_OrdersCollection',
                    'total_count' => 1,
                    'orders' => [],
                ],
            ],
            'processed_at' => null,
        ];
    }
}
