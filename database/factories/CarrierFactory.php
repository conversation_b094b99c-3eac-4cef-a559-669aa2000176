<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Carrier>
 */
class CarrierFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $carriers = ['UPS', 'FedEx', 'DHL', 'USPS', 'TNT', 'Aramex', 'DPD'];

        return [
            'name' => $this->faker->unique()->words(2, true),
            'tracking_url' => $this->faker->optional(0.8)->url().'/track?number={tracking_number}',
        ];
    }
}
