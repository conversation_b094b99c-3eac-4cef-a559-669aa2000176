<?php

namespace Database\Factories;

use App\Enums\ShipmentStatus;
use App\Models\Carrier;
use App\Models\ThirdPartyLogistic;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shipment>
 */
class ShipmentFactory extends Factory
{
    public function definition(): array
    {

        return [
            'third_party_logistic_id' => $this->faker->optional(0.4)->randomElement([
                ThirdPartyLogistic::factory(),
                null,
            ]),
            'carrier_id' => Carrier::factory(),
            'tracking_number' => $this->faker->regexify('[A-Z0-9]{10,20}'),
            'status' => $this->faker->randomElement(ShipmentStatus::cases())->value,
            'shipped_at' => $this->faker->optional(0.7)->dateTimeBetween('-30 days', 'now'),
            'expected_delivery_date' => $this->faker->optional(0.8)->dateTimeBetween('now', '+10 days'),
            'delivered_at' => $this->faker->optional(0.3)->dateTimeBetween('-15 days', 'now'),
            'weight' => $this->faker->optional(0.8)->numberBetween(100, 50000), // in grams
            'length' => $this->faker->optional(0.6)->numberBetween(10, 1000), // in cm
            'width' => $this->faker->optional(0.6)->numberBetween(10, 1000), // in cm
            'height' => $this->faker->optional(0.6)->numberBetween(10, 1000), // in cm
            'shipping_total' => $this->faker->numberBetween(500, 5000), // in cents
            'currency_code' => 'USD',
            'label_url' => $this->faker->optional(0.7)->url(),
        ];
    }

    public function pending(): static
    {
        return $this->state(fn () => [
            'status' => ShipmentStatus::Pending->value,
            'shipped_at' => null,
            'delivered_at' => null,
        ]);
    }

    public function delivered(): static
    {
        return $this->state(fn () => [
            'status' => ShipmentStatus::Delivered->value,
            'shipped_at' => $this->faker->dateTimeBetween('-15 days', '-5 days'),
            'delivered_at' => $this->faker->dateTimeBetween('-5 days', 'now'),
        ]);
    }

    public function inTransit(): static
    {
        return $this->state(fn () => [
            'status' => ShipmentStatus::InTransit->value,
            'shipped_at' => $this->faker->dateTimeBetween('-10 days', '-1 day'),
            'delivered_at' => null,
        ]);
    }

    public function withThirdPartyLogistic(?ThirdPartyLogistic $provider = null): static
    {
        return $this->state(fn () => [
            'third_party_logistic_id' => $provider?->id ?? ThirdPartyLogistic::factory(),
        ]);
    }
}
