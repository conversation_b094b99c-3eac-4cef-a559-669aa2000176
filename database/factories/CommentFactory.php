<?php

namespace Database\Factories;

use App\Models\Comment;
use App\Models\Design;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Comment>
 */
class CommentFactory extends Factory
{
    protected $model = Comment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'content' => $this->faker->paragraph(),
            'commentable_type' => Design::class,
            'commentable_id' => Design::factory(),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Create a comment for a specific commentable model.
     */
    public function forModel($commentable): static
    {
        return $this->state([
            'commentable_type' => get_class($commentable),
            'commentable_id' => $commentable->getKey(),
        ]);
    }

    /**
     * Create a comment by a specific user.
     */
    public function by(User $user): static
    {
        return $this->state([
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create a short comment.
     */
    public function short(): static
    {
        return $this->state([
            'content' => $this->faker->sentence(),
        ]);
    }

    /**
     * Create a long comment.
     */
    public function long(): static
    {
        return $this->state([
            'content' => $this->faker->paragraphs(3, true),
        ]);
    }
}
