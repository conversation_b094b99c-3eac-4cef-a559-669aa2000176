<?php

namespace Database\Factories;

use App\Enums\QcLineItemStatus;
use App\Models\LineItem;
use App\Models\QcOrderCheck;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QcLineItemCheck>
 */
class QcLineItemCheckFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'qc_order_check_id' => QcOrderCheck::factory(),
            'line_item_id' => LineItem::factory(),
            'status' => $this->faker->randomElement(QcLineItemStatus::cases())->value,
            'checked_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }
}
