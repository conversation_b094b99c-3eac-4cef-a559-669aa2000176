<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShippingAddress>
 */
class ShippingAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'line_one' => $this->faker->streetAddress(),
            'line_two' => $this->faker->optional()->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->optional()->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => $this->faker->countryCode(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'email' => $this->faker->optional()->safeEmail(),
            'is_usps_verified' => $this->faker->boolean(30), // 30% chance of being USPS verified
        ];
    }

    /**
     * Create a US shipping address
     */
    public function us(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => $this->faker->stateAbbr(),
            'country' => 'US',
            'zip' => $this->faker->numerify('#####'),
            'is_usps_verified' => $this->faker->boolean(80), // Higher chance for US addresses
        ]);
    }

    /**
     * Create a UK shipping address
     */
    public function uk(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => null,
            'country' => 'GB',
            'zip' => $this->faker->regexify('[A-Z]{1,2}[0-9]{1,2} [0-9][A-Z]{2}'),
            'is_usps_verified' => false, // USPS doesn't verify UK addresses
        ]);
    }

    /**
     * Create a USPS verified address
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_usps_verified' => true,
        ]);
    }
}
