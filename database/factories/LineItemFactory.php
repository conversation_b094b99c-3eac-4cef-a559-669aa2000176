<?php

namespace Database\Factories;

use App\Enums\LineItemStatus;
use App\Models\Order;
use App\Models\Shipment;
use App\Models\Supplier;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LineItem>
 */
class LineItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 5);
        $price = $this->faker->numberBetween(500, 10000); // Price in cents

        return [
            'id_platform' => $this->faker->optional()->randomNumber(8),
            'order_id' => Order::factory(),
            'number_portal' => $this->faker->unique()->randomNumber(6),
            'product_name' => $this->faker->words(3, true),
            'product_image_url' => 'https://picsum.photos/500/500',
            'sku' => $this->faker->bothify('SKU-####-????'),
            'quantity' => $quantity,
            'price' => $price,
            'currency_code' => $this->faker->randomElement(['USD', 'EUR', 'VND', 'GBP']),
            'subtotal' => $price * $quantity,
            'is_download' => $this->faker->boolean(20), // 20% chance of being downloadable
            'is_personalizable' => $this->faker->boolean(30), // 30% chance of being personalizable
            'etsy_listing_id' => $this->faker->optional()->randomNumber(8),
            'variations' => $this->generateVariations(),
            'meta_data' => $this->faker->optional()->randomElement([
                ['key' => 'value'],
                ['color' => 'blue', 'material' => 'cotton'],
                ['size' => 'large', 'weight' => '2kg'],
            ]),
            'private_note' => $this->faker->optional()->sentence(),
            'production_total' => $this->faker->numberBetween(0, $price * 0.7), // Production cost is usually less than selling price
            'supplier_id' => null, // Will be set manually when needed
            'supplier_order_number' => fn (array $attributes) => $attributes['supplier_id'] ? $this->faker->optional()->bothify('SO-####-????') : null,
            'shipment_id' => null, // Will be set manually when needed
            'status' => LineItemStatus::New,
        ];
    }

    /**
     * Generate realistic product variations
     */
    private function generateVariations(): ?array
    {
        if ($this->faker->boolean(40)) { // 40% chance of having variations
            return null;
        }

        $variations = [];
        $variationTypes = [
            ['label' => 'Color', 'values' => ['Red', 'Blue', 'Green', 'Black', 'White']],
            ['label' => 'Size', 'values' => ['XS', 'S', 'M', 'L', 'XL']],
            ['label' => 'Material', 'values' => ['Cotton', 'Polyester', 'Silk', 'Wool']],
            ['label' => 'Style', 'values' => ['Classic', 'Modern', 'Vintage', 'Minimalist']],
        ];

        $numVariations = $this->faker->numberBetween(1, 2);
        $selectedTypes = $this->faker->randomElements($variationTypes, $numVariations);

        foreach ($selectedTypes as $index => $type) {
            $variations[] = [
                'order' => $index + 1,
                'label' => $type['label'],
                'value' => $this->faker->randomElement($type['values']),
            ];
        }

        return $variations;
    }

    /**
     * Create a line item for a downloadable product
     */
    public function downloadable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_download' => true,
            'product_name' => $this->faker->randomElement([
                'Digital Art Print',
                'PDF Pattern',
                'Printable Wall Art',
                'Digital Planner',
                'SVG Cut File',
            ]),
            'price' => $this->faker->numberBetween(100, 2000), // Digital products are usually cheaper
        ]);
    }

    /**
     * Create a line item for a personalizable product
     */
    public function personalizable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_personalizable' => true,
            'product_name' => $this->faker->randomElement([
                'Custom Name Mug',
                'Personalized T-Shirt',
                'Custom Photo Frame',
                'Engraved Jewelry',
                'Monogrammed Towel',
            ]),
        ]);
    }

    /**
     * Create a line item with specific status
     */
    public function withStatus(LineItemStatus $status): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $status,
        ]);
    }

    /**
     * Create a line item in VND currency
     */
    public function vnd(): static
    {
        return $this->state(fn (array $attributes) => [
            'currency_code' => 'VND',
            'price' => $this->faker->numberBetween(50000, 2000000), // VND amounts are larger
            'subtotal' => fn (array $attributes) => $attributes['price'] * $attributes['quantity'],
            'production_total' => fn (array $attributes) => $this->faker->numberBetween(0, $attributes['price'] * 0.7),
        ]);
    }

    /**
     * Create a line item with high quantity
     */
    public function bulk(): static
    {
        return $this->state(function (array $attributes) {
            $quantity = $this->faker->numberBetween(10, 50);
            $price = $attributes['price'] ?? $this->faker->numberBetween(500, 5000);

            return [
                'quantity' => $quantity,
                'subtotal' => $price * $quantity,
            ];
        });
    }

    /**
     * Create a line item with a supplier
     */
    public function withSupplier(): static
    {
        return $this->state(fn (array $attributes) => [
            'supplier_id' => Supplier::factory(),
            'suplier_order_number' => $this->faker->bothify('SO-####-????'),
        ]);
    }

    /**
     * Create a line item with a shipment
     */
    public function withShipment(): static
    {
        return $this->state(fn (array $attributes) => [
            'shipment_id' => Shipment::factory(),
        ]);
    }
}
