<?php

namespace Database\Factories;

use App\Actions\GenerateInternalOrderNumber;
use App\Enums\OrderStatus;
use App\Enums\Platform;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = $this->faker->numberBetween(800, 90000);
        $shippingTotal = $this->faker->numberBetween(500, 2000);
        $taxTotal = $this->faker->numberBetween(0, $subtotal * 0.1);
        $discountTotal = $this->faker->numberBetween(0, $subtotal * 0.15);
        $total = $subtotal + $shippingTotal + $taxTotal - $discountTotal;

        return [
            'seller_id' => \App\Models\User::factory(),
            'customer_id' => Customer::factory(),
            'orders_import_request_id' => null,
            'number_portal' => null,
            'order_number' => 'ORD-'.rand(1000, 9999).'-ABCD',
            'order_date' => now()->subDays(rand(1, 30)),
            'platform' => Platform::Etsy,
            'id_platform' => rand(1000000, 9999999),
            'shop_id_platform' => rand(100000, 999999),
            'status' => Arr::random([
                OrderStatus::New,
                OrderStatus::Draft,
            ]),
            'is_gift' => false,
            'is_gift_wrapped' => false,
            'gift_message' => null,
            'currency_code' => 'USD',
            'total' => $total,
            'subtotal' => $subtotal,
            'shipping_total' => $shippingTotal,
            'tax_total' => $taxTotal,
            'discount_total' => $discountTotal,
            'cost_breakdown' => [],
            'date_paid' => now()->subDays(rand(1, 7)),
            'payment_method' => 'paypal',
            'expected_ship_date' => now()->addDays(rand(1, 30)),
            'customer_note' => null,
            'seller_confirmed_at' => fn (array $attributes) => $attributes['status'] === OrderStatus::New ? null : now(),
        ];
    }

    public function configure(): static
    {
        return $this->afterCreating(function (\App\Models\Order $order) {
            GenerateInternalOrderNumber::make()->handle($order);
        });
    }

    /**
     * Create a draft order
     */
    public function draft(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => OrderStatus::Draft,
                'seller_confirmed_at' => null,
            ];
        });
    }

    /**
     * Create a complete order with all relations
     */
    public function complete(): static
    {
        return $this->withLineItems()->withAddresses();
    }
}
