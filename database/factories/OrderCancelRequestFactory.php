<?php

namespace Database\Factories;

use App\Enums\OrderCancelRequestStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderCancelRequest>
 */
class OrderCancelRequestFactory extends Factory
{
    protected $model = OrderCancelRequest::class;

    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'seller_id' => User::factory()->state(['role' => 'seller']),
            'reason' => $this->faker->paragraph(3),
            'status' => OrderCancelRequestStatus::Pending,
            'processed_by' => null,
            'processed_at' => null,
            'feedback_note' => null,
        ];
    }

    public function pending(): static
    {
        return $this->state([
            'status' => OrderCancelRequestStatus::Pending,
            'processed_by' => null,
            'processed_at' => null,
            'feedback_note' => null,
        ]);
    }

    public function approved(): static
    {
        return $this->state([
            'status' => OrderCancelRequestStatus::Approved,
            'processed_by' => User::factory()->state(['role' => 'operator']),
            'processed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'feedback_note' => $this->faker->optional()->sentence(),
        ]);
    }

    public function rejected(): static
    {
        return $this->state([
            'status' => OrderCancelRequestStatus::Rejected,
            'processed_by' => User::factory()->state(['role' => 'operator']),
            'processed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'feedback_note' => $this->faker->sentence(),
        ]);
    }
}
