<?php

namespace Database\Factories;

use App\Enums\QcOrderCheckStatus;
use App\Models\Order;
use App\Models\QcSession;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QcOrderCheck>
 */
class QcOrderCheckFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(QcOrderCheckStatus::cases());
        $confirmedAt = $status === QcOrderCheckStatus::Completed ? $this->faker->dateTimeBetween('-1 week', 'now') : null;

        return [
            'qc_session_id' => QcSession::factory(),
            'order_id' => Order::factory(),
            'status' => $status->value,
            'confirmed_at' => $confirmedAt,
        ];
    }
}
