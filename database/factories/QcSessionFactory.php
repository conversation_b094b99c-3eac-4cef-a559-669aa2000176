<?php

namespace Database\Factories;

use App\Enums\QcSessionStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QcSession>
 */
class QcSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(QcSessionStatus::cases());
        $startedAt = $status !== QcSessionStatus::New ? $this->faker->dateTimeBetween('-1 week', 'now') : null;
        $completedAt = $status === QcSessionStatus::Completed ? $this->faker->dateTimeBetween($startedAt ?? '-1 week', 'now') : null;

        return [
            'user_id' => User::factory(),
            'status' => $status->value,
            'started_at' => $startedAt,
            'completed_at' => $completedAt,
        ];
    }
}
