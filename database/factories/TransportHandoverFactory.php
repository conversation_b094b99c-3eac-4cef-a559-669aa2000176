<?php

namespace Database\Factories;

use App\Enums\TransportHandoverStatus;
use App\Models\ThirdPartyLogistic;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TransportHandover>
 */
class TransportHandoverFactory extends Factory
{
    public function definition(): array
    {
        return [
            'third_party_logistic_id' => ThirdPartyLogistic::factory(),
            'tracking_numbers' => $this->faker->randomElement([
                "TDA123456789\nTDA987654321\nTDA555666777",
                'TDA111222333,TDA444555666,TDA777888999',
                "TDA123ABC456\nTDA789DEF012",
            ]),
            'notes' => $this->faker->optional(0.3)->sentence(),
            'status' => TransportHandoverStatus::Draft,
            'user_id' => User::factory(),
        ];
    }

    public function draft(): static
    {
        return $this->state(fn () => [
            'status' => TransportHandoverStatus::Draft,
        ]);
    }

    public function confirmed(): static
    {
        return $this->state(fn () => [
            'status' => TransportHandoverStatus::Confirmed,
            'confirmed_at' => now(),
        ]);
    }

    public function withTrackingNumbers(string $trackingNumbers): static
    {
        return $this->state(fn () => [
            'tracking_numbers' => $trackingNumbers,
        ]);
    }

    public function withThirdPartyLogistic(?ThirdPartyLogistic $provider = null): static
    {
        return $this->state(fn () => [
            'third_party_logistic_id' => $provider?->id ?? ThirdPartyLogistic::factory(),
        ]);
    }
}
