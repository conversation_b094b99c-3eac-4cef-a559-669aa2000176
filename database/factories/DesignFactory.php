<?php

namespace Database\Factories;

use App\Models\Design;
use App\Models\Idea;
use App\Models\ProductType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Design>
 */
class DesignFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Design::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'idea_id' => Idea::factory(),
            'product_type_id' => ProductType::factory(),
            'source_file_urls' => $this->faker->optional()->url(),
            'designer_id' => null, // No designer assigned by default
        ];
    }

    /**
     * Indicate that the design has an assigned designer.
     */
    public function withDesigner(?User $designer = null): static
    {
        return $this->state(fn (array $attributes) => [
            'designer_id' => $designer?->id ?? User::factory(),
        ]);
    }

    /**
     * Indicate that the design has no assigned designer.
     */
    public function unassigned(): static
    {
        return $this->state(fn (array $attributes) => [
            'designer_id' => null,
        ]);
    }
}
