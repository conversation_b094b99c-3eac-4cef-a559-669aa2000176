<?php

namespace Database\Factories;

use App\Enums\Platform;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'platform' => $this->faker->randomElement([Platform::Etsy->value, Platform::Amazon->value]),
            'id_platform' => $this->faker->unique()->randomNumber(8),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'username' => $this->faker->optional()->userName(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'avatar_url' => $this->faker->optional()->imageUrl(200, 200, 'people'),
        ];
    }

    /**
     * Create a customer for Etsy platform
     */
    public function etsy(): static
    {
        return $this->state(fn (array $attributes) => [
            'platform' => Platform::Etsy->value,
        ]);
    }

    /**
     * Create a customer for Amazon platform
     */
    public function amazon(): static
    {
        return $this->state(fn (array $attributes) => [
            'platform' => Platform::Amazon->value,
        ]);
    }
}
