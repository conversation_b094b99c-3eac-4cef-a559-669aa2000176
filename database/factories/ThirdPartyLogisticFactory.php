<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ThirdPartyLogistic>
 */
class ThirdPartyLogisticFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $providers = [
            ['name' => 'Viettel Post', 'code' => 'VTP'],
            ['name' => 'Vietnam Post', 'code' => 'VNP'],
            ['name' => '<PERSON>ia<PERSON>han<PERSON> (GHN)', 'code' => 'GHN'],
            ['name' => '<PERSON>ia<PERSON>ệm (GHTK)', 'code' => 'GHTK'],
            ['name' => 'J&T Express Vietnam', 'code' => 'JT'],
            ['name' => 'Best Express Vietnam', 'code' => 'BEST'],
            ['name' => 'Ninja Van Vietnam', 'code' => 'NINJA'],
            ['name' => 'Kerry Express Vietnam', 'code' => 'KERRY'],
        ];

        $provider = $this->faker->randomElement($providers);

        return [
            'name' => $this->faker->company(),
            'code' => $this->faker->unique()->bothify('??##'),
        ];
    }
}
