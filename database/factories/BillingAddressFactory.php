<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillingAddress>
 */
class BillingAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id_platform' => $this->faker->optional()->randomNumber(6),
            'name' => $this->faker->name(),
            'line_one' => $this->faker->streetAddress(),
            'line_two' => $this->faker->optional()->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->optional()->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => $this->faker->countryCode(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'email' => $this->faker->optional()->safeEmail(),
        ];
    }

    /**
     * Create a US billing address
     */
    public function us(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => $this->faker->stateAbbr(),
            'country' => 'US',
            'zip' => $this->faker->numerify('#####'),
        ]);
    }

    /**
     * Create a UK billing address
     */
    public function uk(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => null,
            'country' => 'GB',
            'zip' => $this->faker->regexify('[A-Z]{1,2}[0-9]{1,2} [0-9][A-Z]{2}'),
        ]);
    }
}
