@php
    $headers = !empty($rows) ? array_keys($rows[0]) : [];
@endphp
<table style="width: 100%; border-collapse: collapse; border: 1px solid #e2e8f0;">
    <thead>
    <tr>
        @foreach($headers as $header)
            <th style="border: 1px solid #ccc; padding: 8px 12px; text-align: left; font-weight: 600;">
                {{ ucwords(str_replace('_', ' ', $header)) }}
            </th>
        @endforeach
    </tr>
    </thead>

    <tbody>
    @foreach($rows as $index => $row)
        <tr>
            @foreach($row as $cell)
                <td style="border: 1px solid #ccc; padding: 8px 12px;">
                    {!! nl2br($cell) !!}
                </td>
            @endforeach
        </tr>
    @endforeach
    </tbody>

</table>
