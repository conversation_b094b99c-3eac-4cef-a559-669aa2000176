<div class="space-y-4 p-4">
    @if($this->activities->isEmpty())
        <div class="text-center py-8">
            <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                <x-heroicon-o-clock class="h-6 w-6 text-gray-400" />
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No activity found</h3>
            <p class="mt-1 text-sm text-gray-500">This record has no logged activities yet.</p>
        </div>
    @else
        <div class="flow-root">
            <ul role="list" class="-mb-8">
                @foreach($this->activities as $activity)
                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div class="flex items-center justify-center w-4 h-4 mt-2 mr-2">
                                    @php $iconName = $this->getEventIcon($activity->description) @endphp
                                    <x-dynamic-component :component="$iconName" class="size-3 {{ $this->getEventIconColor($activity->description) }}" />
                                </div>
                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm text-gray-900">
                                            {!! $this->getEventDescription($activity) !!}
                                        </p>

                                        @if($activity->description === 'updated' && !empty($activity->changes))
                                            <div class="mt-2 space-y-1">
                                                @php
                                                    $attributes = $activity->changes['attributes'] ?? [];
                                                    $oldValues = $activity->changes['old'] ?? [];

                                                    // Filter out updated_at field
                                                    $attributes = array_filter($attributes, function($key) {
                                                        return $key !== 'updated_at';
                                                    }, ARRAY_FILTER_USE_KEY);
                                                @endphp

                                                @foreach($attributes as $key => $newValue)
                                                    <div class="text-sm text-gray-600">
                                                        <span class="font-medium capitalize">{{ str_replace('_', ' ', $key) }}:</span>
                                                        @if(array_key_exists($key, $oldValues))
                                                            from <span class="text-rose-600 font-medium">{!! $this->formatValueCompact($oldValues[$key]) !!}</span>
                                                            to <span class="text-indigo-600 font-medium">{!! $this->formatValueCompact($newValue) !!}</span>
                                                        @else
                                                            <span class="font-medium">{!! $this->formatValueCompact($newValue) !!}</span>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif


                                    </div>
                                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                        <time datetime="{{ $activity->created_at->toISOString() }}">
                                            {{ $activity->created_at->setTimezone(config('app.user_timezone'))->format('Y-m-d H:i:s') }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif


</div>
