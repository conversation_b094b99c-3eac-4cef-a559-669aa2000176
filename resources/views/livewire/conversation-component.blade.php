<div class="conversation-component">
    <!-- Add New Comment Form -->
    <div class="rounded-lg">
        <div>
            <div>
                <textarea
                    wire:model="newComment"
                    id="newComment"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-600 focus:border-primary-600"
                    placeholder="Write your comment here..."></textarea>
                @error('newComment')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <div class="mt-1 flex justify-start">
                <x-filament::button type="button" outlined size="xs" wire:loading.attr="disabled"
                    wire:click="addComment">
                    Add comment
                </x-filament::button>
            </div>
        </div>
    </div>

    <!-- Comments List -->
    <div class="mt-4 space-y-2">
        @forelse($comments as $comment)
            <div class="bg-neutral-400/10 px-4 py-2 rounded-lg text-sm">
                <!-- Comment Header -->
                <div
                    class="flex items-center {{ $comment->user_id === auth()->id() ? 'justify-end' : 'justify-start' }}">
                    <div class="flex items-center space-x-3">
                        <h4 class="font-medium text-gray-900">
                            {{ $comment->user->name ?: $comment->user->email }}
                        </h4>
                        <p class="text-xs text-gray-500">
                            {{ $comment->created_at->diffForHumans() }}
                        </p>
                    </div>
                </div>

                <!-- Comment Content -->
                <div
                    class="text-gray-700 leading-relaxed {{ $comment->user_id === auth()->id() ? 'text-right' : 'text-start' }}">
                    {!! nl2br($comment->content) !!}
                </div>

                <!-- Comment Actions -->
                @if (user()->can('delete', $comment))
                    <div class="flex justify-start">
                        <button wire:click="deleteComment({{ $comment->id }})"
                            wire:confirm="Are you sure you want to delete this comment?"
                            class="text-gray-600 hover:text-gray-800 text-xs">
                            <x-heroicon-o-trash class="h-4 w-4 inline-block" />
                        </button>
                    </div>
                @endif
            </div>
        @empty
            <div class="text-center py-8">
                <div class="text-gray-400 mb-2">
                    <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
                <p class="text-sm text-gray-500">No comments yet</p>
                <p class="text-xs text-gray-400">Be the first to start the conversation!</p>
            </div>
        @endforelse
    </div>
</div>
