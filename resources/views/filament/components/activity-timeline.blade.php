<div class="space-y-4 p-4">
    @if($activities->isEmpty())
        <div class="text-center py-8">
            <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                <x-heroicon-o-clock class="h-6 w-6 text-gray-400" />
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No activity found</h3>
            <p class="mt-1 text-sm text-gray-500">This record has no logged activities yet.</p>
        </div>
    @else
        <div class="flow-root">
            <ul role="list" class="-mb-8">
                @foreach($activities as $activity)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last)
                                <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                            @endif
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white {{ $activity->description === 'created' ? 'bg-green-500' : ($activity->description === 'updated' ? 'bg-blue-500' : 'bg-red-500') }}">
                                        @if($activity->description === 'created')
                                            <x-heroicon-s-plus class="h-4 w-4 text-white" />
                                        @elseif($activity->description === 'updated')
                                            <x-heroicon-s-pencil class="h-4 w-4 text-white" />
                                        @else
                                            <x-heroicon-s-trash class="h-4 w-4 text-white" />
                                        @endif
                                    </span>
                                </div>
                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm text-gray-900">
                                            <span class="font-medium">{{ class_basename($activity->subject_type) }}</span>
                                            was
                                            <span class="font-medium">{{ $activity->description }}</span>
                                            @if($activity->causer)
                                                by
                                                <span class="font-medium text-blue-600">{{ $activity->causer->name ?? $activity->causer->email ?? 'Unknown User' }}</span>
                                            @endif
                                        </p>
                                        
                                        @if($activity->description === 'updated' && !empty($activity->changes))
                                            <div class="mt-2">
                                                <button 
                                                    type="button"
                                                    onclick="showActivityDetails({{ $activity->id }})"
                                                    class="inline-flex items-center text-xs text-blue-600 hover:text-blue-800"
                                                >
                                                    <x-heroicon-o-eye class="mr-1 h-3 w-3" />
                                                    View Changes ({{ count($activity->changes['attributes'] ?? []) }} fields)
                                                </button>
                                            </div>
                                        @endif
                                        
                                        @if($activity->description === 'created' && !empty($activity->changes['attributes']))
                                            <div class="mt-2">
                                                <button 
                                                    type="button"
                                                    onclick="showActivityDetails({{ $activity->id }})"
                                                    class="inline-flex items-center text-xs text-green-600 hover:text-green-800"
                                                >
                                                    <x-heroicon-o-eye class="mr-1 h-3 w-3" />
                                                    View Initial Values
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                        <time datetime="{{ $activity->created_at->toISOString() }}" title="{{ $activity->created_at->format('F j, Y \a\t g:i A') }}">
                                            {{ $activity->created_at->diffForHumans() }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>

{{-- Activity Details Modal --}}
<div id="activity-details-modal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="hideActivityDetails()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                        Activity Details
                    </h3>
                    <div class="mt-4">
                        <div id="activity-details-content" class="text-sm text-gray-500">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="hideActivityDetails()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    const activities = @json($activities->keyBy('id')->map(function($activity) {
        return [
            'id' => $activity->id,
            'description' => $activity->description,
            'changes' => $activity->changes,
            'created_at' => $activity->created_at->format('F j, Y \a\t g:i A'),
            'causer' => $activity->causer ? ($activity->causer->name ?? $activity->causer->email ?? 'Unknown User') : null,
            'subject_type' => class_basename($activity->subject_type)
        ];
    }));

    function showActivityDetails(activityId) {
        const activity = activities[activityId];
        if (!activity) return;

        const modal = document.getElementById('activity-details-modal');
        const content = document.getElementById('activity-details-content');
        
        let html = `
            <div class="mb-4">
                <h4 class="font-medium text-gray-900">${activity.subject_type} ${activity.description}</h4>
                <p class="text-xs text-gray-500">${activity.created_at}</p>
                ${activity.causer ? `<p class="text-xs text-gray-500">by ${activity.causer}</p>` : ''}
            </div>
        `;

        if (activity.changes && (activity.changes.attributes || activity.changes.old)) {
            html += '<div class="space-y-3">';
            
            const attributes = activity.changes.attributes || {};
            const oldValues = activity.changes.old || {};
            
            Object.keys(attributes).forEach(key => {
                const newValue = attributes[key];
                const oldValue = oldValues[key];
                
                html += `
                    <div class="border-l-4 border-blue-200 pl-3">
                        <div class="font-medium text-gray-900 capitalize">${key.replace(/_/g, ' ')}</div>
                        ${oldValue !== undefined ? `
                            <div class="text-sm">
                                <span class="text-red-600">- ${formatValue(oldValue)}</span>
                            </div>
                            <div class="text-sm">
                                <span class="text-green-600">+ ${formatValue(newValue)}</span>
                            </div>
                        ` : `
                            <div class="text-sm text-gray-600">${formatValue(newValue)}</div>
                        `}
                    </div>
                `;
            });
            
            html += '</div>';
        } else {
            html += '<p class="text-gray-500">No detailed changes available.</p>';
        }

        content.innerHTML = html;
        modal.classList.remove('hidden');
    }

    function hideActivityDetails() {
        document.getElementById('activity-details-modal').classList.add('hidden');
    }

    function formatValue(value) {
        if (value === null) return 'null';
        if (value === '') return '(empty)';
        if (typeof value === 'boolean') return value ? 'true' : 'false';
        if (typeof value === 'object') return JSON.stringify(value);
        return String(value);
    }
</script>
