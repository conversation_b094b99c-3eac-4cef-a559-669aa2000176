<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Genesis Portal</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.png">
    <link rel="icon" type="image/svg+xml" href="/favicon.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(45deg, #0a0a0a, #1a0a1a, #0a1a1a);
            background-size: 400% 400%;
            animation: backgroundShift 8s ease infinite;
            color: #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        @keyframes backgroundShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .main-container {
            text-align: center;
            z-index: 10;
            position: relative;
        }

        .title {
            display: none;
        }

        .ascii-title {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.1;
            color: #00ff41;
            text-shadow:
                0 0 5px #00ff41,
                0 0 10px #00ff41,
                0 0 15px #00ff41;
            margin-bottom: 2rem;
            white-space: pre;
            position: relative;
            opacity: 0;
            transform: translateY(50px) scale(0.8);
        }

        .ascii-title::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            color: #ff0080;
            animation: glitch 3s ease-in-out infinite;
            clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
            text-shadow: -2px 0 #ff0080, 2px 0 #00ffff;
        }

        .ascii-title::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            color: #00ffff;
            animation: glitch 3s ease-in-out infinite reverse;
            clip-path: polygon(0 55%, 100% 55%, 100% 100%, 0 100%);
            text-shadow: -2px 0 #ff0080, 2px 0 #00ffff;
        }

        @keyframes glitch {
            0% { transform: translate(0); }
            2% { transform: translate(-3px, 1px); }
            4% { transform: translate(0); }
            15% { transform: translate(0); }
            17% { transform: translate(4px, -2px); }
            18% { transform: translate(-2px, 3px); }
            19% { transform: translate(0); }
            45% { transform: translate(0); }
            47% { transform: translate(-1px, -1px); }
            48% { transform: translate(0); }
            65% { transform: translate(0); }
            66% { transform: translate(2px, 1px); }
            67% { transform: translate(-3px, -2px); }
            68% { transform: translate(1px, 2px); }
            69% { transform: translate(0); }
            85% { transform: translate(0); }
            87% { transform: translate(-2px, 0); }
            88% { transform: translate(0); }
            100% { transform: translate(0); }
        }

        .ascii-char {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px) rotateX(90deg);
            animation: none;
        }

        .subtitle {
            font-family: 'Orbitron', monospace;
            font-size: 1.4rem;
            color: #ff0080;
            text-transform: uppercase;
            letter-spacing: 6px;
            margin-bottom: 3rem;
            opacity: 0;
            transform: translateY(30px);
            min-height: 2rem;
        }

        .typewriter-char {
            opacity: 0;
            display: inline-block;
        }

        .cursor {
            display: inline-block;
            background-color: #ff0080;
            width: 3px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .particle {
            position: fixed;
            width: 3px;
            height: 3px;
            background: #00ff41;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
            opacity: 0.7;
        }

        .particle.pink {
            background: #ff0080;
        }

        .particle.cyan {
            background: #00ffff;
        }

        .grid-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 65, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 65, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 0;
            opacity: 0.3;
        }

        .scanlines {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 65, 0.03) 2px,
                rgba(0, 255, 65, 0.03) 4px
            );
            pointer-events: none;
            z-index: 2;
        }

        .glow-effect {
            transition: all 0.3s ease;
        }

        .glow-effect:hover {
            text-shadow: 0 0 30px #00ff41;
            transform: scale(1.02);
        }

        @media (max-width: 768px) {
            .ascii-title {
                font-size: 0.5rem;
                line-height: 1;
            }

            .subtitle {
                font-size: 1rem;
                letter-spacing: 3px;
            }
        }
    </style>
</head>
<body>
    <!-- Background Elements -->
    <div class="grid-bg"></div>
    <div class="scanlines"></div>

    <!-- Particles Container -->
    <div id="particles"></div>

    <!-- Main Content -->
    <div class="main-container">
        <h1 class="title glow-effect">GENESIS</h1>
        <div class="ascii-title glow-effect" id="ascii-title" data-text=" ______     ______     __   __     ______     ______     __     ______
/\  ___\   /\  ___\   /\ &quot;-.\ \   /\  ___\   /\  ___\   /\ \   /\  ___\
\ \ \__ \  \ \  __\   \ \ \-.  \  \ \  __\   \ \___  \  \ \ \  \ \___  \
 \ \_____\  \ \_____\  \ \_\\&quot;\_\  \ \_____\  \/\_____\  \ \_\  \/\_____\
  \/_____/   \/_____/   \/_/ \/_/   \/_____/   \/_____/   \/_/   \/_____/
                                                                          "> ______     ______     __   __     ______     ______     __     ______
/\  ___\   /\  ___\   /\ "-.\ \   /\  ___\   /\  ___\   /\ \   /\  ___\
\ \ \__ \  \ \  __\   \ \ \-.  \  \ \  __\   \ \___  \  \ \ \  \ \___  \
 \ \_____\  \ \_____\  \ \_\\"\_\  \ \_____\  \/\_____\  \ \_\  \/\_____\
  \/_____/   \/_____/   \/_/ \/_/   \/_____/   \/_____/   \/_/   \/_____/
                                                                          </div>
        <p class="subtitle" id="subtitle"></p>
    </div>

    <!-- Anime.js v4.0.2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@4.0.2/lib/anime.iife.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for anime.js to load
            setTimeout(initAnimations, 100);
        });

        function initAnimations() {
            if (typeof anime === 'undefined') {
                console.error('Anime.js not loaded');
                return;
            }

            createParticles();
            prepareAsciiTitle();
            startAnimationSequence();
        }

        function createParticles() {
            const container = document.getElementById('particles');
            const colors = ['', 'pink', 'cyan'];

            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = `particle ${colors[Math.floor(Math.random() * colors.length)]}`;
                particle.style.left = Math.random() * 100 + 'vw';
                particle.style.top = Math.random() * 100 + 'vh';
                container.appendChild(particle);

                // Animate each particle
                anime.animate(particle, {
                    translateX: () => anime.utils.random(-200, 200),
                    translateY: () => anime.utils.random(-200, 200),
                    scale: () => anime.utils.random(0.5, 2),
                    opacity: [0.3, 0.8, 0.3],
                    duration: () => anime.utils.random(3000, 8000),
                    delay: () => anime.utils.random(0, 2000),
                    loop: true,
                    alternate: true,
                    easing: 'inOutSine'
                });
            }
        }

        function prepareAsciiTitle() {
            const asciiElement = document.getElementById('ascii-title');
            const text = asciiElement.textContent;
            asciiElement.innerHTML = '';

            // Wrap each character in a span
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const span = document.createElement('span');
                span.className = 'ascii-char';

                if (char === ' ') {
                    span.innerHTML = '&nbsp;';
                } else if (char === '\n') {
                    asciiElement.appendChild(document.createElement('br'));
                    continue;
                } else {
                    span.textContent = char;
                }

                asciiElement.appendChild(span);
            }
        }

        function createTypewriterEffect() {
            const subtitle = document.getElementById('subtitle');
            const text = 'Portal Initialized';
            subtitle.innerHTML = '';

            // Create spans for each character
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const span = document.createElement('span');
                span.className = 'typewriter-char';
                span.textContent = char === ' ' ? '\u00A0' : char;
                subtitle.appendChild(span);
            }

            // Add cursor
            const cursor = document.createElement('span');
            cursor.className = 'cursor';
            cursor.innerHTML = '&nbsp;';
            subtitle.appendChild(cursor);

            // Animate typewriter effect
            anime.animate('.typewriter-char', {
                opacity: [0, 1],
                delay: anime.stagger(100, {start: 2000}),
                duration: 50,
                easing: 'linear',
                complete: function() {
                    // Remove cursor after typing is complete
                    setTimeout(() => {
                        cursor.style.display = 'none';

                        // Scale ASCII art to 2x after typewriter finishes
                        anime.animate('.ascii-title', {
                            scale: [1, 2],
                            duration: 1500,
                            easing: 'outElastic(1, .6)',
                            complete: function() {
                                // Add extra glow effect after scaling
                                anime.animate('.ascii-title', {
                                    textShadow: [
                                        '0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41',
                                        '0 0 20px #00ff41, 0 0 40px #00ff41, 0 0 60px #00ff41, 0 0 80px #00ff41',
                                        '0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41'
                                    ],
                                    duration: 2000,
                                    easing: 'inOutSine'
                                });
                            }
                        });
                    }, 1000);
                }
            });
        }

        function startAnimationSequence() {
            // Create timeline
            const tl = anime.createTimeline({
                defaults: {
                    easing: 'outExpo'
                }
            });

            // 1. ASCII title entrance with stunning effects
            tl.add('.ascii-title', {
                opacity: [0, 1],
                translateY: [50, 0],
                scale: [0.8, 1],
                duration: 1000,
                easing: 'outBack'
            })

            // 2. ASCII characters wave effect
            .add('.ascii-char', {
                opacity: [0, 1],
                translateY: [20, 0],
                rotateX: [90, 0],
                scale: [0.5, 1],
                delay: anime.stagger(15, {
                    grid: [80, 6],
                    from: 'center'
                }),
                duration: 300,
                easing: 'outElastic(1, .8)'
            }, '-=500')

            // 3. Show subtitle container
            .add('.subtitle', {
                opacity: [0, 1],
                translateY: [30, 0],
                duration: 600,
                easing: 'outQuad',
                complete: function() {
                    // Start typewriter effect after subtitle container appears
                    createTypewriterEffect();
                }
            }, '-=200');

            // Add continuous glow effect
            startContinuousEffects();

            // Add hover effects
            setupHoverEffects();
        }

        function startContinuousEffects() {
            // Pulsing glow effect for ASCII title
            anime.animate('.ascii-title', {
                textShadow: [
                    '0 0 5px #00ff41, 0 0 10px #00ff41, 0 0 15px #00ff41',
                    '0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41, 0 0 40px #00ff41',
                    '0 0 5px #00ff41, 0 0 10px #00ff41, 0 0 15px #00ff41'
                ],
                duration: 4000,
                loop: true,
                easing: 'inOutSine'
            });
        }

        function setupHoverEffects() {
            const asciiTitle = document.querySelector('.ascii-title');
            const subtitle = document.querySelector('.subtitle');

            // ASCII title hover effect
            asciiTitle.addEventListener('mouseenter', function() {
                anime.animate(this, {
                    scale: 1.02,
                    textShadow: '0 0 20px #00ff41, 0 0 40px #00ff41, 0 0 60px #00ff41',
                    duration: 300,
                    easing: 'outQuad'
                });

                // Random character effects
                const chars = this.querySelectorAll('.ascii-char');
                const randomChars = Array.from(chars).sort(() => 0.5 - Math.random()).slice(0, 30);

                anime.animate(randomChars, {
                    scale: [1, 1.3, 1],
                    color: ['#00ff41', '#ff0080', '#00ffff', '#00ff41'],
                    duration: 600,
                    delay: anime.stagger(20),
                    easing: 'outElastic(1, .6)'
                });
            });

            asciiTitle.addEventListener('mouseleave', function() {
                anime.animate(this, {
                    scale: 1,
                    duration: 300,
                    easing: 'outQuad'
                });
            });

            // Subtitle hover effect
            subtitle.addEventListener('mouseenter', function() {
                const chars = this.querySelectorAll('.typewriter-char');

                anime.animate(chars, {
                    scale: [1, 1.2, 1],
                    color: ['#ff0080', '#00ffff', '#ff0080'],
                    duration: 400,
                    delay: anime.stagger(30),
                    easing: 'outElastic(1, .8)'
                });
            });
        }
    </script>
</body>
</html>
