<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Phiếu Bàn <PERSON>ng Hóa - BGHH-{{ $transportHandover->created_at->format('Y') }}-{{ str_pad($transportHandover->id, 4, '0', STR_PAD_LEFT) }}</title>

    <style>
        @media print {
            @page {
                margin: 1cm;
                size: A4;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 14px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            color: #000;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            text-transform: uppercase;
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .document-info div {
            flex: 1;
        }

        .party-info {
            display: flex;
            margin-bottom: 30px;
            gap: 32px;
        }

        .party-info .left,
        .party-info .right {
            flex: 1;
        }

        .info-row {
            margin-bottom: 8px;
            display: flex;
        }

        .info-label {
            font-weight: bold;
            min-width: 120px;
        }

        .info-value {
            flex: 1;
            border-bottom: 1px dotted #000;
            min-height: 20px;
        }

        .items-table {
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }

        .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .items-table .tracking-code {
            font-weight: normal;
        }

        .summary {
            margin-bottom: 20px;
        }

        .commitment {
            margin-bottom: 30px;
            text-align: justify;
        }

        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }

        .signature-box {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            height: 80px;
            margin-bottom: 10px;
        }

        .date-received {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PHIẾU BÀN GIAO HÀNG HÓA</h1>
    </div>

    <div class="document-info">
        <div>
            <strong>Số phiếu:</strong> BGHH-{{ $transportHandover->created_at->format('Y') }}-{{ str_pad($transportHandover->id, 4, '0', STR_PAD_LEFT) }}
        </div>
        <div style="text-align: right;">
            <strong>Ngày in phiếu:</strong> {{ today()->format('d/m/Y') }}
        </div>
    </div>

    <div class="party-info">
        <div class="left">
            <h3>Thông tin bên giao (Công ty gửi hàng):</h3>
            <div class="info-row">
                <span class="info-label">Tên công ty:</span>
                <span class="info-value"></span>
            </div>
            <div class="info-row">
                <span class="info-label">Nhân viên gửi:</span>
                <span class="info-value"></span>
            </div>
            <div class="info-row">
                <span class="info-label">Số điện thoại:</span>
                <span class="info-value"></span>
            </div>
        </div>

        <div class="right">
            <h3>Thông tin bên nhận:</h3>
            <div class="info-row">
                <span class="info-label">Tên đơn vị:</span>
                <span class="info-value">{{ $transportHandover->thirdPartyLogistic->name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Số điện thoại:</span>
                <span class="info-value"></span>
            </div>
            <div class="info-row">
                <span class="info-label">Địa điểm nhận hàng:</span>
                <span class="info-value"></span>
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50px;">STT</th>
                <th style="width: 150px;">Order TDA Number</th>
                <th style="width: 150px;">Mã Line Item</th>
                <th>Tracking Code</th>
            </tr>
        </thead>
        <tbody>
            @php
                $rowNumber = 1;
                $uniqueTrackingCodes = [];
            @endphp

            @forelse($transportHandover->shipments as $shipment)
                @forelse($shipment->lineItems as $lineItem)
                    <tr>
                        <td style="text-align: center;">{{ $rowNumber++ }}</td>
                        <td>{{ $lineItem->order->number_portal ?? 'N/A' }}</td>
                        <td>{{ $lineItem->number_portal ?? 'N/A' }}</td>
                        <td class="tracking-code">{{ $shipment->tracking_number }}</td>
                    </tr>
                    @php
                        if (!in_array($shipment->tracking_number, $uniqueTrackingCodes)) {
                            $uniqueTrackingCodes[] = $shipment->tracking_number;
                        }
                    @endphp
                @empty
                    {{-- If shipment has no line items, still show the tracking number --}}
                    <tr>
                        <td style="text-align: center;">{{ $rowNumber++ }}</td>
                        <td>N/A</td>
                        <td>N/A</td>
                        <td class="tracking-code">{{ $shipment->tracking_number }}</td>
                    </tr>
                    @php
                        if (!in_array($shipment->tracking_number, $uniqueTrackingCodes)) {
                            $uniqueTrackingCodes[] = $shipment->tracking_number;
                        }
                    @endphp
                @endforelse
            @empty
                {{-- If no shipments found, show message --}}
                <tr>
                    <td colspan="4" style="text-align: center; font-style: italic; color: #666;">
                        Không tìm thấy shipment nào cho các tracking code đã nhập
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="summary">
        <strong>Tổng kiện hàng:</strong> {{ count($uniqueTrackingCodes) }}
    </div>

    <div class="commitment">
        <strong>Cam kết:</strong><br>
        Bên nhận đã kiểm tra và xác nhận số lượng, tình trạng hàng hóa theo danh sách trên. Mọi vấn đề phát sinh sẽ được hai bên phối hợp xử lý theo thỏa thuận.
    </div>


    <div class="date-received">
        <strong>Ngày nhận bàn giao:</strong> _______________
    </div>

    <div class="signatures">
        <div class="signature-box">
            <strong>Đại diện bên giao</strong>
        </div>

        <div class="signature-box">
            <div>
                <strong>Đại diện bên nhận (3PL)</strong>
            </div>
        </div>
    </div>
</body>
</html>
