@import '../../../../vendor/filament/filament/resources/css/theme.css';

@source '../../../../app/Filament/**/*';
@source '../../../../resources/views/filament/**/*';
@source '../../../../resources/views/livewire/**/*';

/* Nyan Cat Animation */
:root {
    --nyan-duration: 0.1s;
    --nyan-size: 150px;
}

.nyan-cat {
    position: fixed;
    bottom: 20px;
    left: -200px;
    width: var(--nyan-size);
    height: auto;
    z-index: 9999;
    opacity: 0;
}

.nyan-cat.running {
    opacity: 1;
    animation: nyanRun var(--nyan-duration) linear;
}

@keyframes nyanRun {
    0% {
        left: -200px;
    }
    100% {
        left: calc(100vw + 50px);
    }
}

/* ready button*/
.qc-status-buttons label.fi-btn[for$="status-ready"] {
    color: rgb(22 163 74);
    font-weight: 600;
}

/* missing button */
.qc-status-buttons label.fi-btn[for$="status-missing"] {
    color: rgb(255, 198, 10);
    font-weight: 600;
}
