name: Deploy dev

on:
  push:
    branches: [ "dev" ]

jobs:
  setup:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Deploy scripts
        uses: appleboy/ssh-action@master
        with:
          host: **************
          username: flashvps
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /home/<USER>/genesis-dev.tdagroup.online
            git pull origin dev
            composer install --no-dev --optimize-autoloader --no-ansi
            /home/<USER>/.local/share/pnpm/pnpm install && /home/<USER>/.local/share/pnpm/pnpm run build
            php artisan migrate --force
            php artisan optimize
            php artisan horizon:terminate

            echo "Restarting php8.3-fpm"
            sudo -S service php8.3-fpm reload

            curl -s "https://ping2.me/@daudau/sweb-stuff?message=genesis-dev.tdagroup.online%20deployed" > /dev/null