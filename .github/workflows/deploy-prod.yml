name: Deploy Prod

on:
  workflow_run:
    workflows: ["tests"]
    types:
      - completed
    branches: [ "main" ]

jobs:
  setup:
    name: Deploy
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - name: Deploy scripts
        uses: appleboy/ssh-action@master
        with:
          host: ************
          username: flashvps
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /home/<USER>/genesis.tdagroup.co
            git pull origin main
            composer install --no-dev --optimize-autoloader --no-ansi
            /home/<USER>/.local/share/pnpm/pnpm install && /home/<USER>/.local/share/pnpm/pnpm run build
            php artisan migrate --force
            php artisan optimize
            php artisan horizon:terminate

            echo "Restarting php8.3-fpm"
            sudo -S service php8.3-fpm reload

            curl -s "https://ping2.me/@daudau/sweb-stuff?message=genesis.tdagroup.co%20deployed" > /dev/null