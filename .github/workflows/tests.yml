name: tests

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

jobs:
  ci:
    runs-on: ubuntu-latest
    environment: Testing

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Cache Composer dependencies
      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      # Setup PHP with extensions and caching
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          tools: composer:v2
          coverage: none  # Disable xdebug for faster execution unless coverage is needed
          extensions: mbstring, dom, fileinfo, mysql, redis
          ini-values: memory_limit=512M

      # Install Composer dependencies with optimizations
      - name: Install Composer Dependencies
        run: composer install

      # Setup Node.js with pnpm caching
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      # Install Node.js dependencies
      - name: Install Node.js dependencies
        run: pnpm install --frozen-lockfile

      # Cache built assets
      - name: Cache built assets
        uses: actions/cache@v4
        with:
          path: |
            public/build
            public/hot
          key: ${{ runner.os }}-assets-${{ hashFiles('resources/**/*', 'package.json', 'vite.config.js') }}
          restore-keys: |
            ${{ runner.os }}-assets-

      # Setup environment and generate key
      - name: Copy Environment File
        run: cp .env.example .env

      - name: Generate Application Key
        run: php artisan key:generate

      # Build assets
      - name: Build assets
        run: pnpm run build

      # Run tests
      - name: Run Tests
        run: composer run test
