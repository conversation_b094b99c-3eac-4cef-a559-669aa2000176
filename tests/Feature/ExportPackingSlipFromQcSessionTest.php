<?php

use App\Actions\ExportPackingSlipFromQcSession;
use App\Enums\QcLineItemStatus;
use App\Enums\QcSessionStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\QcLineItemCheck;
use App\Models\QcOrderCheck;
use App\Models\QcSession;
use App\Models\ShippingAddress;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

test('can export packing slip CSV from completed QC session', function () {
    // Create a user
    $user = User::factory()->create();

    // Create a completed QC session
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
        'started_at' => now()->subHour(),
        'completed_at' => now(),
    ]);

    // Create an order with line items and shipping address
    $order = Order::factory()->create([
        'number_portal' => '20240630E0001',
        'order_number' => 'ETSY123456',
    ]);

    ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'John Doe',
        'country' => 'United States',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000101',
        'product_name' => 'Test Product',
        'product_type' => 'T-Shirt',
        'product_image_url' => 'https://example.com/image.jpg',
        'quantity' => 2,
        'variations' => [
            ['order' => 1, 'label' => 'Size', 'value' => 'Large'],
            ['order' => 2, 'label' => 'Color', 'value' => 'Blue'],
            ['order' => 3, 'label' => 'Personalization', 'value' => 'Custom Text'],
        ],
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    // Create QC line item check
    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Execute the export action
    $filePath = ExportPackingSlipFromQcSession::make()->handle($qcSession);

    // Assert file path is returned
    expect($filePath)->toBeString();
    expect($filePath)->toContain('packing_slip_'.$qcSession->id);
    expect($filePath)->toContain('.csv');

    // Assert file exists in storage
    expect(Storage::disk('public')->exists($filePath))->toBeTrue();

    // Clean up
    Storage::disk('public')->delete($filePath);
});

test('packing slip CSV has correct headers', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create();
    ShippingAddress::factory()->create(['order_id' => $order->id]);
    $lineItem = LineItem::factory()->create(['order_id' => $order->id]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Get CSV data for testing
    $csvData = ExportPackingSlipFromQcSession::make()->handle($qcSession, true);

    // Check first row contains date header
    $firstRow = $csvData[0];
    expect($firstRow[0])->toContain('PHIẾU ĐÓNG GÓI NGÀY');
    expect($firstRow[0])->toContain(now()->format('d/m/Y'));

    // Check second row contains column headers
    $headers = $csvData[1];
    $expectedHeaders = [
        'Order TDA Number',
        'Order Number',
        'Line Item Code',
        'Shipping Name',
        'Shipping Country',
        'Quantity',
        'Notes',
        'Seller Note',
        'Type of Product',
        'Product Attributes',
        'Size',
        'Color',
        'Personalization',
        'Image URL',
        'Image',
        'Has Gift Info',
        'Gift Message',
        'Packed',
    ];

    expect($headers)->toBe($expectedHeaders);
});

test('packing slip CSV contains correct data', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create();

    ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'Jane Smith',
        'country' => 'Canada',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'product_type' => 'Mug',
        'product_image_url' => 'https://example.com/mug.jpg',
        'quantity' => 1,
        'variations' => [
            ['order' => 1, 'label' => 'Size', 'value' => 'Medium'],
            ['order' => 2, 'label' => 'Color', 'value' => 'Red'],
        ],
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Get CSV data for testing
    $csvData = ExportPackingSlipFromQcSession::make()->handle($qcSession, true);
    $dataRow = $csvData[2]; // Third row contains the actual data

    // Verify data content
    expect($dataRow[0])->toBe($order->number_portal); // Order TDA Number
    expect($dataRow[1])->toBe($order->order_number); // Order Number
    expect($dataRow[2])->toBe((string) $lineItem->number_portal); // Line Item Code
    expect($dataRow[3])->toBe('Jane Smith'); // Shipping Name
    expect($dataRow[4])->toBe('Canada'); // Shipping Country
    expect($dataRow[5])->toBe(1); // Quantity
    // skip Note
    // skip Seller Note
    expect($dataRow[8])->toBe('Mug'); // Type of Product
    // skip Product Attributes
    expect($dataRow[10])->toBe('Medium'); // Size
    expect($dataRow[11])->toBe('Red'); // Color
    // skip Personalization
    expect($dataRow[13])->toBe('https://example.com/mug.jpg'); // Image URL
    expect($dataRow[14])->toBe(''); // Image placeholder
    expect($dataRow[15])->toBe(''); // Packed - empty
});

test('export creates file with correct content', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'number_portal' => '20240630E0001',
    ]);

    ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'Test User',
        'country' => 'Vietnam',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000101',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Execute the export action
    $filePath = ExportPackingSlipFromQcSession::make()->handle($qcSession);

    // Read the file content
    $fileContent = Storage::disk('public')->get($filePath);

    // Verify file contains expected data
    expect($fileContent)->toContain('PHIẾU ĐÓNG GÓI NGÀY');
    expect($fileContent)->toContain('Order TDA Number');
    expect($fileContent)->toContain('20240630E0001');
    expect($fileContent)->toContain('20240630E000101');
    expect($fileContent)->toContain('Test User');
    expect($fileContent)->toContain('Vietnam');
    expect($fileContent)->toContain('');

    // Clean up
    Storage::disk('public')->delete($filePath);
});

test('packing slip export only includes ready line items, excludes missing items', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'number_portal' => '20240630E0005',
    ]);

    ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'Test User',
        'country' => 'Vietnam',
    ]);

    // Create two line items - one ready, one missing
    $readyLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000501',
        'product_type' => 'Ready Product',
    ]);

    \Log::info('Order order_number: '.$order->order_number);
    \Log::info('LineItem number_portal: '.$readyLineItem->number_portal);

    $missingLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000502',
        'product_type' => 'Missing Product',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    // Create QC line item checks with different statuses
    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $readyLineItem->id,
        'status' => \App\Enums\QcLineItemStatus::Ready,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $missingLineItem->id,
        'status' => \App\Enums\QcLineItemStatus::Missing,
    ]);

    // Get CSV data for testing
    $csvData = ExportPackingSlipFromQcSession::make()->handle($qcSession, true);

    // Should have header row + column headers + 1 data row (only the ready item)
    expect(count($csvData))->toBe(3);

    // Verify only the ready line item is included
    $dataRow = $csvData[2]; // Third row contains the actual data
    expect($dataRow[2])->toBe($readyLineItem->number_portal); // Line Item Code
    expect($dataRow[8])->toBe('Ready Product'); // Type of Product

    // Verify the missing line item is not included by checking all data rows
    $allLineItemCodes = collect($csvData)->skip(2)->pluck(1)->toArray();
    expect($allLineItemCodes)->not->toContain($missingLineItem->number_portal);
});
