<?php

use App\Actions\GenerateInternalOrderNumber;
use App\Enums\Platform;
use App\Models\LineItem;
use App\Models\Order;

test('order number follows the new format platformcode', function () {
    $order = Order::factory()
        ->has(LineItem::factory(2)->state(['number_portal' => null]))
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    GenerateInternalOrderNumber::make()->handle($order);

    $order->refresh();

    expect($order->number_portal)->not->toBeNull();

    // Check format: E{100322 + id}
    $expectedNumber = 'E'.(100322 + $order->id);
    expect($order->number_portal)->toBe($expectedNumber);

    // Check line items have correct format: orderNumber-01, orderNumber-02, etc.
    $order->lineItems->each(function (LineItem $lineItem, $index) use ($order) {
        $expectedLineItemNumber = sprintf('%s-%02d', $order->number_portal, $index + 1);
        expect($lineItem->number_portal)->toBe($expectedLineItemNumber);
    });
});

test('order number increments correctly based on order id', function () {
    // Create first order
    $order1 = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    GenerateInternalOrderNumber::make()->handle($order1);

    // Create second order
    $order2 = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    GenerateInternalOrderNumber::make()->handle($order2);

    $order1->refresh();
    $order2->refresh();

    // Check that the numbers are based on order ID
    $expectedNumber1 = 'E'.(100322 + $order1->id);
    $expectedNumber2 = 'E'.(100322 + $order2->id);

    expect($order1->number_portal)->toBe($expectedNumber1);
    expect($order2->number_portal)->toBe($expectedNumber2);
});

test('different platforms have different codes', function () {
    $etsyOrder = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    $amazonOrder = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Amazon,
        ]);

    GenerateInternalOrderNumber::make()->handle($etsyOrder);
    GenerateInternalOrderNumber::make()->handle($amazonOrder);

    $etsyOrder->refresh();
    $amazonOrder->refresh();

    // Etsy should have 'E' code
    $expectedEtsyNumber = 'E'.(100322 + $etsyOrder->id);
    expect($etsyOrder->number_portal)->toBe($expectedEtsyNumber);

    // Amazon should have 'A' code
    $expectedAmazonNumber = 'A'.(100322 + $amazonOrder->id);
    expect($amazonOrder->number_portal)->toBe($expectedAmazonNumber);
});

test('order numbers are unique and based on order id', function () {
    $order1 = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    $order2 = Order::factory()
        ->create([
            'number_portal' => null,
            'platform' => Platform::Etsy,
        ]);

    GenerateInternalOrderNumber::make()->handle($order1);
    GenerateInternalOrderNumber::make()->handle($order2);

    $order1->refresh();
    $order2->refresh();

    // Numbers should be different because order IDs are different
    expect($order1->number_portal)->not->toBe($order2->number_portal);

    // Each should follow the correct format
    expect($order1->number_portal)->toBe('E'.(100322 + $order1->id));
    expect($order2->number_portal)->toBe('E'.(100322 + $order2->id));
});
