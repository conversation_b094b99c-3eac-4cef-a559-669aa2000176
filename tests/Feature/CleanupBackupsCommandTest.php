<?php

use App\Console\Commands\CleanupBackups;
use Illuminate\Support\Facades\Storage;

test('cleanup backups command shows no backups message when empty', function () {
    Storage::fake('backup');

    $this->artisan(CleanupBackups::class)
        ->expectsOutput('No backup files found.')
        ->assertExitCode(0);
});

test('cleanup backups command shows warning when backup directory does not exist', function () {
    Storage::fake('backup');

    // Remove the backup directory
    Storage::disk('backup')->deleteDirectory('.');

    $this->artisan(CleanupBackups::class)
        ->expectsOutput('Backup directory does not exist.')
        ->assertExitCode(0);
});

test('cleanup backups command keeps files within limit', function () {
    Storage::fake('backup');

    // Create 3 backup files
    Storage::disk('backup')->put('genesis_mysql_2025-01-01_10-00-00.sql', 'backup 1');
    Storage::disk('backup')->put('genesis_mysql_2025-01-02_10-00-00.sql', 'backup 2');
    Storage::disk('backup')->put('genesis_mysql_2025-01-03_10-00-00.sql', 'backup 3');

    $this->artisan(CleanupBackups::class, ['--keep' => 5])
        ->expectsOutput('Found 3 backup files.')
        ->expectsOutput('All files are within the keep limit (5). Nothing to clean up.')
        ->assertExitCode(0);
});

test('cleanup backups command fails with invalid keep option', function () {
    $this->artisan(CleanupBackups::class, ['--keep' => 0])
        ->expectsOutput('The --keep option must be at least 1.')
        ->assertExitCode(1);
});

test('cleanup backups command shows files to delete without confirmation in testing', function () {
    Storage::fake('backup');

    // Create 5 backup files with different timestamps
    for ($i = 1; $i <= 5; $i++) {
        Storage::disk('backup')->put("genesis_mysql_2025-01-0{$i}_10-00-00.sql", "backup content {$i}");
        // Sleep to ensure different timestamps
        usleep(1000);
    }

    $this->artisan(CleanupBackups::class, ['--keep' => 2])
        ->expectsOutput('Found 5 backup files.')
        ->expectsOutputToContain('Keeping 2 most recent files:')
        ->expectsOutputToContain('Will delete 3 old backup files')
        ->expectsQuestion('Do you want to proceed with the cleanup?', false)
        ->expectsOutput('Cleanup cancelled.')
        ->assertExitCode(0);
});

test('cleanup backups command works in non-interactive mode', function () {
    Storage::fake('backup');

    // Create 5 backup files with different timestamps
    for ($i = 1; $i <= 5; $i++) {
        Storage::disk('backup')->put("genesis_mysql_2025-01-0{$i}_10-00-00.sql", "backup content {$i}");
        // Sleep to ensure different timestamps
        usleep(1000);
    }

    $this->artisan(CleanupBackups::class, ['--keep' => 2, '--no-interaction' => true])
        ->expectsOutput('Found 5 backup files.')
        ->expectsOutputToContain('Keeping 2 most recent files:')
        ->expectsOutputToContain('Will delete 3 old backup files')
        ->expectsOutputToContain('Cleanup completed successfully!')
        ->expectsOutputToContain('Deleted 3 files')
        ->assertExitCode(0);

    // Verify only 2 files remain
    $remainingFiles = Storage::disk('backup')->files();
    expect(count($remainingFiles))->toBe(2);
});

test('cleanup backups command is registered', function () {
    expect(app()->make(CleanupBackups::class))->toBeInstanceOf(CleanupBackups::class);
});
