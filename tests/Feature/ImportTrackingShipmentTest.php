<?php

use App\Actions\ImportTrackingShipment;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\ShipmentStatus;
use App\Models\Carrier;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Shipment;

test('can import tracking shipment with SUP packer', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with SUP packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify shipment was created with correct status
    expect(Shipment::count())->toBe(1);

    $shipment = Shipment::first();
    expect($shipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->tracking_number->toBe('1Z999AA1234567890')
        ->carrier_id->toBe($carrier->id)
        ->shipped_at->not->toBeNull()
        ->weight->toBe(500)
        ->height->toBe(10)
        ->length->toBe(20)
        ->width->toBe(15)
        ->shipping_total->toBe(1550) // 15.50 * 100
        ->packer->toBe('SUP');

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem)
        ->shipment_id->toBe($shipment->id)
        ->status->toBe(LineItemStatus::Shipped)
        ->production_total->toBe(2550) // 25.50 * 100
        ->packaging_total->toBe(200);  // 2.00 * 100

    // Clean up
    unlink($tempFile);
});

test('can import tracking shipment with TDA packer', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '2465989-AA0002']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::Packing,
    ]);

    // Create CSV content with TDA packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify shipment was created with correct status
    expect(Shipment::count())->toBe(1);

    $shipment = Shipment::first();
    expect($shipment)
        ->status->toBe(ShipmentStatus::Pending)
        ->tracking_number->toBe('FX123456789')
        ->carrier_id->toBe($carrier->id)
        ->shipped_at->toBeNull()
        ->weight->toBe(300)
        ->shipping_total->toBe(1200) // 12.00 * 100
        ->packer->toBe('TDA');

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem)
        ->shipment_id->toBe($shipment->id)
        ->status->toBe(LineItemStatus::Packed)
        ->production_total->toBe(3000) // 30.00 * 100
        ->packaging_total->toBe(300);  // 3.00 * 100

    // Clean up
    unlink($tempFile);
});

test('can import multiple orders in same shipment', function () {
    // Create test data - two different orders
    $carrier = Carrier::factory()->create(['name' => 'DHL']);

    $order1 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order1->update(['number_portal' => '1465989-AA0001']);

    $order2 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order2->update(['number_portal' => '2465989-AA0002']);

    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order1->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with same tracking for both orders
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,DHL987654321,DHL,400,12,25,18,20.00,35.00,4.00,SUP\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,DHL987654321,DHL,400,12,25,18,20.00,40.00,5.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify only one shipment was created (shared tracking)
    expect(Shipment::count())->toBe(1);

    $shipment = Shipment::first();
    expect($shipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->tracking_number->toBe('DHL987654321')
        ->carrier_id->toBe($carrier->id)
        ->packer->toBe('SUP');

    // Verify both line items belong to the same shipment
    $lineItem1->refresh();
    $lineItem2->refresh();

    expect($lineItem1->shipment_id)->toBe($shipment->id);
    expect($lineItem2->shipment_id)->toBe($shipment->id);
    expect($lineItem1->status)->toBe(LineItemStatus::Shipped);
    expect($lineItem2->status)->toBe(LineItemStatus::Shipped);
    expect($lineItem1->production_total)->toBe(3500); // 35.00 * 100
    expect($lineItem1->packaging_total)->toBe(400);   // 4.00 * 100
    expect($lineItem2->production_total)->toBe(4000); // 40.00 * 100
    expect($lineItem2->packaging_total)->toBe(500);   // 5.00 * 100

    // Clean up
    unlink($tempFile);
});

test('validates required packer field', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // Add status for validation
    ]);

    // Create CSV with missing packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing 'Packer'.");

    // Clean up
    unlink($tempFile);
});

test('validates invalid packer value', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // Add status for validation
    ]);

    // Create CSV with invalid packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,INVALID\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 has invalid 'Packer'. Must be 'TDA' or 'SUP'.");

    // Clean up
    unlink($tempFile);
});

test('creates new shipment even if line item already has shipment', function () {
    // Create test data with existing shipment (from 3PL)
    $carrier3PL = Carrier::factory()->create(['name' => 'HP']);
    $carrierFinal = Carrier::factory()->create(['name' => 'UPS']);

    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    // Create existing shipment (simulating 3PL import)
    $existingShipment = Shipment::factory()->create([
        'carrier_id' => $carrier3PL->id,
        'tracking_number' => '3PL123456789',
        'status' => ShipmentStatus::Pending,
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
        'shipment_id' => $existingShipment->id,
    ]);

    // Create CSV content with final carrier tracking
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,45.00,6.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify new shipment was created (not updated)
    expect(Shipment::count())->toBe(2);

    $newShipment = Shipment::where('tracking_number', '1Z999AA1234567890')->first();
    expect($newShipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->tracking_number->toBe('1Z999AA1234567890')
        ->carrier_id->toBe($carrierFinal->id)
        ->shipped_at->not->toBeNull()
        ->weight->toBe(500)
        ->shipping_total->toBe(1550)
        ->packer->toBe('SUP');

    // Verify line item was moved to new shipment
    $lineItem->refresh();
    expect($lineItem)
        ->shipment_id->toBe($newShipment->id)
        ->status->toBe(LineItemStatus::Shipped)
        ->production_total->toBe(4500) // 45.00 * 100
        ->packaging_total->toBe(600);  // 6.00 * 100

    // Clean up
    unlink($tempFile);
});

test('validates invalid production cost', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // SUP packer requires InProduction status
    ]);

    // Create CSV with invalid production cost
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,invalid_cost,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 has invalid 'Production cost'.");

    // Clean up
    unlink($tempFile);
});

test('validates invalid packaging fee', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // SUP packer requires InProduction status
    ]);

    // Create CSV with invalid packaging fee
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,invalid_fee,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 has invalid 'Packaging fee'.");

    // Clean up
    unlink($tempFile);
});

test('can import with empty production cost and packaging fee', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with empty production cost and packaging fee
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,,,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item was updated without production/packaging costs
    $lineItem->refresh();
    expect($lineItem)
        ->shipment_id->not->toBeNull()
        ->status->toBe(LineItemStatus::Shipped)
        ->production_total->toBe(0)  // Should be 0 when empty, not null due to schema
        ->packaging_total->toBe(0);  // Should be 0 when empty, not null due to schema

    // Clean up
    unlink($tempFile);
});

// Order Status Validation Tests
test('validates order with invalid status - new', function () {
    // Create test data with New status order
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 1465989-AA0001-01 does not belong to the specified order.');

    // Clean up
    unlink($tempFile);
});

test('validates order with invalid status - cancelled', function () {
    // Create test data with Cancelled status order
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Cancelled]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 1465989-AA0001-01 does not belong to the specified order.');

    // Clean up
    unlink($tempFile);
});

test('accepts order with partial shipped status', function () {
    // Create test data with PartialShipped status order
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::PartialShipped]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Should not throw exception
    ImportTrackingShipment::make()->validate($tempFile);

    // Import should work
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem->status)->toBe(LineItemStatus::Shipped);

    // Clean up
    unlink($tempFile);
});

test('accepts order with shipped status', function () {
    // Create test data with Shipped status order
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Shipped]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Should not throw exception
    ImportTrackingShipment::make()->validate($tempFile);

    // Import should work
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem->status)->toBe(LineItemStatus::Shipped);

    // Clean up
    unlink($tempFile);
});

// Line Item Status Conditional Update Tests
test('SUP packer only changes InProduction line items to Shipped', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItemInProduction = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    $lineItemPacking = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-02',
        'status' => LineItemStatus::Packing,
    ]);

    // Create CSV content with SUP packer for both line items
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-02,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item statuses
    $lineItemInProduction->refresh();
    $lineItemPacking->refresh();

    expect($lineItemInProduction->status)->toBe(LineItemStatus::Shipped); // Changed from InProduction
    expect($lineItemPacking->status)->toBe(LineItemStatus::Packing); // Unchanged

    // Clean up
    unlink($tempFile);
});

test('TDA packer only changes Packing line items to Packed', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '2465989-AA0002']);

    $lineItemInProduction = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::InProduction,
    ]);

    $lineItemPacking = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-02',
        'status' => LineItemStatus::Packing,
    ]);

    // Create CSV content with TDA packer for both line items
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-02,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item statuses
    $lineItemInProduction->refresh();
    $lineItemPacking->refresh();

    expect($lineItemInProduction->status)->toBe(LineItemStatus::InProduction); // Unchanged
    expect($lineItemPacking->status)->toBe(LineItemStatus::Packed); // Changed from Packing

    // Clean up
    unlink($tempFile);
});

test('SUP packer with New status fails validation', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::New, // Wrong status for SUP packer
    ]);

    // Create CSV content with SUP packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 1465989-AA0001-01 with packer SUP is not in In Production status.');

    // Clean up
    unlink($tempFile);
});

test('TDA packer with New status fails validation', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '2465989-AA0002']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::New, // Wrong status for TDA packer
    ]);

    // Create CSV content with TDA packer
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 2465989-AA0002-01 with packer TDA is not in Packing status.');

    // Clean up
    unlink($tempFile);
});

// Packer vs Line Item Status Validation Tests
test('validates SUP packer requires InProduction status', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::Packing, // Wrong status for SUP packer
    ]);

    // Create CSV content with SUP packer but line item not in InProduction
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 1465989-AA0001-01 with packer SUP is not in In Production status.');

    // Clean up
    unlink($tempFile);
});

test('validates TDA packer requires Packing status', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '2465989-AA0002']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::InProduction, // Wrong status for TDA packer
    ]);

    // Create CSV content with TDA packer but line item not in Packing
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 2465989-AA0002-01 with packer TDA is not in Packing status.');

    // Clean up
    unlink($tempFile);
});

test('accepts SUP packer with InProduction status', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // Correct status for SUP packer
    ]);

    // Create CSV content with SUP packer and correct status
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Should not throw exception
    ImportTrackingShipment::make()->validate($tempFile);

    // Import should work
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem->status)->toBe(LineItemStatus::Shipped);

    // Clean up
    unlink($tempFile);
});

test('accepts TDA packer with Packing status', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '2465989-AA0002']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::Packing, // Correct status for TDA packer
    ]);

    // Create CSV content with TDA packer and correct status
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "2465989-AA0002,2465989-AA0002-01,FX123456789,FedEx,300,8,15,12,12.00,30.00,3.00,TDA\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Should not throw exception
    ImportTrackingShipment::make()->validate($tempFile);

    // Import should work
    ImportTrackingShipment::make()->handle($tempFile);

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem->status)->toBe(LineItemStatus::Packed);

    // Clean up
    unlink($tempFile);
});

test('line item code is source of truth - detaches from old shipment when tracking changes', function () {
    // Create test data
    $carrier1 = Carrier::factory()->create(['name' => 'UPS']);
    $carrier2 = Carrier::factory()->create(['name' => 'FedEx']);

    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // First import - create initial shipment
    $csvContent1 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent1 .= "1465989-AA0001,1465989-AA0001-01,TRACK001,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    $tempFile1 = tempnam(sys_get_temp_dir(), 'test_tracking_import_1');
    file_put_contents($tempFile1, $csvContent1);

    ImportTrackingShipment::make()->handle($tempFile1);

    // Verify first shipment was created and line item is attached
    expect(Shipment::count())->toBe(1);
    $firstShipment = Shipment::first();
    expect($firstShipment->tracking_number)->toBe('TRACK001');

    $lineItem->refresh();
    expect($lineItem->shipment_id)->toBe($firstShipment->id);

    // Second import - same line item but different tracking number
    $csvContent2 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent2 .= "1465989-AA0001,1465989-AA0001-01,TRACK002,FedEx,600,12,25,18,20.00,30.00,3.00,SUP\n";

    $tempFile2 = tempnam(sys_get_temp_dir(), 'test_tracking_import_2');
    file_put_contents($tempFile2, $csvContent2);

    ImportTrackingShipment::make()->handle($tempFile2);

    // Verify second shipment was created
    expect(Shipment::count())->toBe(2);
    $secondShipment = Shipment::where('tracking_number', 'TRACK002')->first();
    expect($secondShipment)->not->toBeNull();
    expect($secondShipment->carrier_id)->toBe($carrier2->id);

    // Verify line item was detached from first shipment and attached to second
    $lineItem->refresh();
    expect($lineItem->shipment_id)->toBe($secondShipment->id);

    // Verify first shipment still exists but has no line items
    $firstShipment->refresh();
    expect($firstShipment->lineItems()->count())->toBe(0);

    // Clean up
    unlink($tempFile1);
    unlink($tempFile2);
});

test('multiple line items detached from different shipments when tracking changes', function () {
    // Create test data
    $carrier1 = Carrier::factory()->create(['name' => 'UPS']);
    $carrier2 = Carrier::factory()->create(['name' => 'FedEx']);

    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-02',
        'status' => LineItemStatus::InProduction,
    ]);

    // First import - create two separate shipments
    $csvContent1 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent1 .= "1465989-AA0001,1465989-AA0001-01,TRACK001,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";
    $csvContent1 .= "1465989-AA0001,1465989-AA0001-02,TRACK002,UPS,600,12,25,18,20.00,30.00,3.00,SUP\n";

    $tempFile1 = tempnam(sys_get_temp_dir(), 'test_tracking_import_1');
    file_put_contents($tempFile1, $csvContent1);

    ImportTrackingShipment::make()->handle($tempFile1);

    // Verify two shipments were created
    expect(Shipment::count())->toBe(2);

    $lineItem1->refresh();
    $lineItem2->refresh();
    $shipment1Id = $lineItem1->shipment_id;
    $shipment2Id = $lineItem2->shipment_id;

    expect($shipment1Id)->not->toBe($shipment2Id);

    // Second import - both line items now go to same new shipment
    $csvContent2 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent2 .= "1465989-AA0001,1465989-AA0001-01,TRACK003,FedEx,700,15,30,20,25.00,35.00,4.00,SUP\n";
    $csvContent2 .= "1465989-AA0001,1465989-AA0001-02,TRACK003,FedEx,700,15,30,20,25.00,35.00,4.00,SUP\n";

    $tempFile2 = tempnam(sys_get_temp_dir(), 'test_tracking_import_2');
    file_put_contents($tempFile2, $csvContent2);

    ImportTrackingShipment::make()->handle($tempFile2);

    // Verify third shipment was created
    expect(Shipment::count())->toBe(3);
    $newShipment = Shipment::where('tracking_number', 'TRACK003')->first();
    expect($newShipment)->not->toBeNull();

    // Verify both line items were detached from old shipments and attached to new one
    $lineItem1->refresh();
    $lineItem2->refresh();
    expect($lineItem1->shipment_id)->toBe($newShipment->id);
    expect($lineItem2->shipment_id)->toBe($newShipment->id);

    // Verify old shipments still exist but have no line items
    expect(Shipment::find($shipment1Id)->lineItems()->count())->toBe(0);
    expect(Shipment::find($shipment2Id)->lineItems()->count())->toBe(0);

    // Clean up
    unlink($tempFile1);
    unlink($tempFile2);
});

test('reuses existing shipment when same tracking number and carrier combination exists', function () {
    // Create test data
    $carrier = Carrier::factory()->create(['name' => 'UPS']);

    $order1 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order1->update(['number_portal' => '1465989-AA0001']);

    $order2 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order2->update(['number_portal' => '2465989-AA0002']);

    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order1->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // First import - create shipment with first line item
    $csvContent1 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent1 .= "1465989-AA0001,1465989-AA0001-01,SHARED123,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    $tempFile1 = tempnam(sys_get_temp_dir(), 'test_tracking_import_1');
    file_put_contents($tempFile1, $csvContent1);

    ImportTrackingShipment::make()->handle($tempFile1);

    // Verify first shipment was created
    expect(Shipment::count())->toBe(1);
    $shipment = Shipment::first();
    expect($shipment->tracking_number)->toBe('SHARED123');
    expect($shipment->lineItems()->count())->toBe(1);

    // Second import - same tracking number and carrier, different line item
    $csvContent2 = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent2 .= "2465989-AA0002,2465989-AA0002-01,SHARED123,UPS,600,12,25,18,20.00,30.00,3.00,SUP\n";

    $tempFile2 = tempnam(sys_get_temp_dir(), 'test_tracking_import_2');
    file_put_contents($tempFile2, $csvContent2);

    ImportTrackingShipment::make()->handle($tempFile2);

    // Verify no new shipment was created (reused existing one)
    expect(Shipment::count())->toBe(1);

    // Verify both line items are now attached to the same shipment
    $shipment->refresh();
    expect($shipment->lineItems()->count())->toBe(2);

    $lineItem1->refresh();
    $lineItem2->refresh();
    expect($lineItem1->shipment_id)->toBe($shipment->id);
    expect($lineItem2->shipment_id)->toBe($shipment->id);

    // Clean up
    unlink($tempFile1);
    unlink($tempFile2);
});

// Line Item-Order Relationship Validation Tests
test('validates line item belongs to specified order', function () {
    // Create test data with two different orders
    Carrier::factory()->create(['name' => 'UPS']);

    $order1 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order1->update(['number_portal' => '1465989-AA0001']);

    $order2 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order2->update(['number_portal' => '2465989-AA0002']);

    // Line item belongs to order2, but CSV specifies order1
    LineItem::factory()->create([
        'order_id' => $order2->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with wrong order number
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,2465989-AA0002-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 2465989-AA0002-01 does not belong to the specified order.');

    // Clean up
    unlink($tempFile);
});

test('validates line item exists', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    // Create CSV content with non-existent line item
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,NONEXISTENT-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing or invalid 'Line Item Code'.");

    // Clean up
    unlink($tempFile);
});

test('validates order exists', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with non-existent order
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "NONEXISTENT-ORDER,1465989-AA0001-01,1Z999AA1234567890,UPS,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1: Line item 1465989-AA0001-01 does not belong to the specified order.');

    // Clean up
    unlink($tempFile);
});

test('validates carrier case insensitively', function () {
    // Create test data with uppercase carrier name
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with lowercase carrier name
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,ups,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Validation should pass with case-insensitive comparison
    ImportTrackingShipment::make()->validate($tempFile);

    // If we reach here, validation passed
    expect(true)->toBeTrue();

    // Clean up
    unlink($tempFile);
});

test('validates carrier with mixed case', function () {
    // Create test data with uppercase carrier name
    Carrier::factory()->create(['name' => 'FedEx']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with different case carrier name
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,FEDEX,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Validation should pass with case-insensitive comparison
    ImportTrackingShipment::make()->validate($tempFile);

    // If we reach here, validation passed
    expect(true)->toBeTrue();

    // Clean up
    unlink($tempFile);
});

test('validates invalid carrier name', function () {
    // Create test data
    Carrier::factory()->create(['name' => 'UPS']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction,
    ]);

    // Create CSV content with invalid carrier name
    $csvContent = "order_tda_number,line_item_code,tracking_code,carrier,weight_(g),height_(cm),length_(cm),width_(cm),shipping_fee,production_cost,packaging_fee,packer\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,1Z999AA1234567890,InvalidCarrier,500,10,20,15,15.50,25.50,2.00,SUP\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_tracking_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportTrackingShipment::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing or invalid 'Carrier'.");

    // Clean up
    unlink($tempFile);
});
