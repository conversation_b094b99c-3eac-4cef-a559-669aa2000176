<?php

use App\Enums\OrderStatus;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Order Status Timestamps', function () {
    it('sets processing_at when order status changes to processing', function () {
        $order = Order::factory()->create(['status' => OrderStatus::New]);
        expect($order->processing_at)->toBeNull();

        $order->update(['status' => OrderStatus::Processing]);

        expect($order->fresh()->processing_at)->not->toBeNull();
        expect($order->fresh()->processing_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets partial_shipped_at when order status changes to partial-shipped', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Processing]);
        expect($order->partial_shipped_at)->toBeNull();

        $order->update(['status' => OrderStatus::PartialShipped]);

        expect($order->fresh()->partial_shipped_at)->not->toBeNull();
        expect($order->fresh()->partial_shipped_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets shipped_at when order status changes to shipped', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Processing]);
        expect($order->shipped_at)->toBeNull();

        $order->update(['status' => OrderStatus::Shipped]);

        expect($order->fresh()->shipped_at)->not->toBeNull();
        expect($order->fresh()->shipped_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets cancelled_at when order status changes to cancelled', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Processing]);
        expect($order->cancelled_at)->toBeNull();

        $order->update(['status' => OrderStatus::Cancelled]);

        expect($order->fresh()->cancelled_at)->not->toBeNull();
        expect($order->fresh()->cancelled_at)->toBeInstanceOf(Carbon::class);
    });

    it('does not set timestamp when order status changes to new', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Draft]);

        $order->update(['status' => OrderStatus::New]);

        $order = $order->fresh();
        expect($order->processing_at)->toBeNull();
        expect($order->partial_shipped_at)->toBeNull();
        expect($order->shipped_at)->toBeNull();
        expect($order->cancelled_at)->toBeNull();
    });

    it('does not set timestamp when order status changes to draft', function () {
        $order = Order::factory()->create(['status' => OrderStatus::New]);

        $order->update(['status' => OrderStatus::Draft]);

        $order = $order->fresh();
        expect($order->processing_at)->toBeNull();
        expect($order->partial_shipped_at)->toBeNull();
        expect($order->shipped_at)->toBeNull();
        expect($order->cancelled_at)->toBeNull();
    });

    it('does not update timestamp when status does not change', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Processing]);
        $order->update(['status' => OrderStatus::Processing]);
        $originalTimestamp = $order->fresh()->processing_at;

        // Update other fields but not status
        $order->update(['seller_note' => 'Updated note']);

        expect($order->fresh()->processing_at)->toEqual($originalTimestamp);
    });

    it('updates timestamp when status transitions back to a previous state', function () {
        $order = Order::factory()->create(['status' => OrderStatus::New]);

        $order->update(['status' => OrderStatus::Processing]);
        $firstTimestamp = $order->fresh()->processing_at;

        // Change to different status and back
        $order->update(['status' => OrderStatus::Shipped]);

        // Small delay to ensure timestamp difference
        sleep(1);

        $order->update(['status' => OrderStatus::Processing]);
        $secondTimestamp = $order->fresh()->processing_at;

        expect($secondTimestamp)->not->toEqual($firstTimestamp);
        // Simulate time passing to ensure timestamp difference
        Carbon::setTestNow(Carbon::now()->addSeconds(5));

        $order->update(['status' => OrderStatus::Processing]);
        $secondTimestamp = $order->fresh()->processing_at;

        expect($secondTimestamp)->not->toEqual($firstTimestamp);
        expect($secondTimestamp->isAfter($firstTimestamp))->toBeTrue();
        Carbon::setTestNow(null); // Reset test time
    });
});
