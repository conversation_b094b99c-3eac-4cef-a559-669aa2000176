<?php

use App\Enums\LineItemStatus;
use App\Enums\ShipmentStatus;
use App\Models\Carrier;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Shipment;
use Carbon\Carbon;

describe('MigrateShippedLineItemTimestamps Command', function () {
    test('it migrates shipped_at timestamps from shipment relations', function () {
        // Create test data
        $carrier = Carrier::factory()->create();
        $order = Order::factory()->create();

        // Create a shipment with shipped_at timestamp
        $shippedAt = Carbon::now()->subDays(2);
        $shipment = Shipment::factory()->create([
            'carrier_id' => $carrier->id,
            'status' => ShipmentStatus::PickedUp,
            'shipped_at' => $shippedAt,
        ]);

        // Create shipped line items without shipped_at timestamp (simulating bulk update)
        $lineItem1 = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => null, // Missing timestamp due to bulk update
        ]);

        $lineItem2 = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => null, // Missing timestamp due to bulk update
        ]);

        // Run the command
        $this->artisan('migrate:shipped-line-item-timestamps', ['--force' => true])
            ->expectsOutput('🚢 Migrating shipped_at timestamps for line items from shipment relations...')
            ->expectsOutput('📊 Found 2 shipped line items missing shipped_at timestamps')
            ->expectsOutput('  ✅ LineItems: 2 updated, 0 skipped')
            ->expectsOutput('✅ Shipped line item timestamp migration completed!')
            ->assertExitCode(0);

        // Verify timestamps were updated
        $lineItem1->refresh();
        $lineItem2->refresh();

        expect($lineItem1->shipped_at)
            ->not->toBeNull()
            ->format('Y-m-d H:i:s')->toEqual($shippedAt->format('Y-m-d H:i:s'));

        expect($lineItem2->shipped_at)
            ->not->toBeNull()
            ->format('Y-m-d H:i:s')->toEqual($shippedAt->format('Y-m-d H:i:s'));
    });

    test('it skips line items that already have shipped_at timestamps', function () {
        // Create test data
        $carrier = Carrier::factory()->create();
        $order = Order::factory()->create();

        $shippedAt = Carbon::now()->subDays(2);
        $shipment = Shipment::factory()->create([
            'carrier_id' => $carrier->id,
            'status' => ShipmentStatus::PickedUp,
            'shipped_at' => $shippedAt,
        ]);

        // Create line item that already has shipped_at timestamp
        $existingTimestamp = Carbon::now()->subDays(1);
        $lineItem = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => $existingTimestamp,
        ]);

        // Run the command
        $this->artisan('migrate:shipped-line-item-timestamps', ['--force' => true])
            ->expectsOutput('✅ No line items need timestamp migration from shipments.')
            ->assertExitCode(0);

        // Verify timestamp was not changed
        $lineItem->refresh();
        expect($lineItem->shipped_at->format('Y-m-d H:i:s'))->toEqual($existingTimestamp->format('Y-m-d H:i:s'));
    });

    test('it skips line items with shipments that have no shipped_at timestamp', function () {
        // Create test data
        $carrier = Carrier::factory()->create();
        $order = Order::factory()->create();

        // Create shipment without shipped_at timestamp
        $shipment = Shipment::factory()->create([
            'carrier_id' => $carrier->id,
            'status' => ShipmentStatus::Pending,
            'shipped_at' => null,
        ]);

        // Create shipped line item
        $lineItem = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => null,
        ]);

        // Run the command
        $this->artisan('migrate:shipped-line-item-timestamps', ['--force' => true])
            ->expectsOutput('✅ No line items need timestamp migration from shipments.')
            ->assertExitCode(0);

        // Verify timestamp was not set
        $lineItem->refresh();
        expect($lineItem->shipped_at)->toBeNull();
    });

    test('it handles dry run mode correctly', function () {
        // Create test data
        $carrier = Carrier::factory()->create();
        $order = Order::factory()->create();

        $shippedAt = Carbon::now()->subDays(2);
        $shipment = Shipment::factory()->create([
            'carrier_id' => $carrier->id,
            'status' => ShipmentStatus::PickedUp,
            'shipped_at' => $shippedAt,
        ]);

        $lineItem = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => null,
        ]);

        // Run in dry-run mode
        $this->artisan('migrate:shipped-line-item-timestamps', ['--dry-run' => true])
            ->expectsOutput('🧪 DRY RUN MODE - No data will be modified')
            ->expectsOutput('📊 Found 1 shipped line items missing shipped_at timestamps')
            ->expectsOutput("  Would update LineItem #{$lineItem->id} - shipped_at = {$shippedAt}")
            ->expectsOutput('  ✅ Would update: 1 line items')
            ->assertExitCode(0);

        // Verify no actual changes were made
        $lineItem->refresh();
        expect($lineItem->shipped_at)->toBeNull();
    });

    test('it ignores non-shipped line items', function () {
        // Create test data
        $carrier = Carrier::factory()->create();
        $order = Order::factory()->create();

        $shippedAt = Carbon::now()->subDays(2);
        $shipment = Shipment::factory()->create([
            'carrier_id' => $carrier->id,
            'status' => ShipmentStatus::PickedUp,
            'shipped_at' => $shippedAt,
        ]);

        // Create line items with different statuses
        $lineItemInProduction = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::InProduction,
            'shipped_at' => null,
        ]);

        $lineItemPacking = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => $shipment->id,
            'status' => LineItemStatus::Packing,
            'shipped_at' => null,
        ]);

        // Run the command
        $this->artisan('migrate:shipped-line-item-timestamps', ['--force' => true])
            ->expectsOutput('✅ No line items need timestamp migration from shipments.')
            ->assertExitCode(0);

        // Verify no timestamps were set
        $lineItemInProduction->refresh();
        $lineItemPacking->refresh();

        expect($lineItemInProduction->shipped_at)->toBeNull();
        expect($lineItemPacking->shipped_at)->toBeNull();
    });

    test('it ignores line items without shipments', function () {
        // Create test data
        $order = Order::factory()->create();

        // Create shipped line item without shipment
        $lineItem = LineItem::factory()->create([
            'order_id' => $order->id,
            'shipment_id' => null,
            'status' => LineItemStatus::Shipped,
            'shipped_at' => null,
        ]);

        // Run the command
        $this->artisan('migrate:shipped-line-item-timestamps', ['--force' => true])
            ->expectsOutput('✅ No line items need timestamp migration from shipments.')
            ->assertExitCode(0);

        // Verify no timestamp was set
        $lineItem->refresh();
        expect($lineItem->shipped_at)->toBeNull();
    });
});
