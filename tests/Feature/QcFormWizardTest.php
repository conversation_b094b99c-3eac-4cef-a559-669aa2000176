<?php

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\QcLineItemStatus;
use App\Enums\QcOrderCheckStatus;
use App\Enums\QcSessionStatus;
use App\Filament\Backoffice\Resources\QcSessions\QcFormWizard;
use App\Models\Order;
use App\Models\QcOrderCheck;
use App\Models\QcSession;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function () {
    $this->user = User::factory()->create(['role' => 'operator']);
    $this->be($this->user);

    $this->qcSession = QcSession::factory()->create([
        'user_id' => $this->user->id,
        'status' => QcSessionStatus::Processing,
    ]);

    $this->order = Order::factory()
        ->hasLineItems(3, ['status' => LineItemStatus::InProduction])
        ->create(['status' => OrderStatus::Processing]);

    $this->orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $this->qcSession->id,
        'order_id' => $this->order->id,
        'status' => QcOrderCheckStatus::Processing,
    ]);
});

test('qc form wizard can be mounted', function () {
    Livewire::test(QcFormWizard::class, ['qcSession' => $this->qcSession])
        ->assertStatus(200);
});

test('qc form wizard blocks confirmation when all items are missing', function () {
    // Create line item checks data with all items missing
    $lineItemChecks = $this->order->lineItems->map(function ($lineItem, $i) {
        return [
            'line_item_id' => $lineItem->id,
            'index' => $i + 1,
            'product_name' => $lineItem->product_name,
            'product_url' => $lineItem->productUrl(),
            'product_image_url' => $lineItem->product_image_url,
            'quantity' => $lineItem->quantity,
            'attributes' => $lineItem->attributesString(),
            'size' => $lineItem->size ?? '',
            'color' => $lineItem->color ?? '',
            'personalization' => $lineItem->personalization ?? '',
            'seller_note' => $lineItem->seller_note ?? '',
            'notes' => $lineItem->compositeNotes(),
            'status' => QcLineItemStatus::Missing->value,
        ];
    })->toArray();

    // Test the component
    Livewire::test(QcFormWizard::class, ['qcSession' => $this->qcSession])
        ->set('qualityCheckData', [
            'order_check_id' => $this->orderCheck->id,
            'line_item_checks' => $lineItemChecks,
        ])
        ->call('confirmOrder');

    // Verify order check status remains unchanged
    $this->orderCheck->refresh();
    expect($this->orderCheck->status)->toBe(QcOrderCheckStatus::Processing);
});

test('qc form wizard shows confirmation dialog when some items are missing', function () {
    // Create line item checks data with some items missing
    $lineItems = $this->order->lineItems;

    // Create a mix of Ready and Missing items
    $lineItemChecks = [];

    // First item: Ready
    $lineItemChecks[] = [
        'line_item_id' => $lineItems[0]->id,
        'index' => 1,
        'product_name' => $lineItems[0]->product_name,
        'product_url' => '',
        'product_image_url' => $lineItems[0]->product_image_url,
        'quantity' => $lineItems[0]->quantity,
        'attributes' => '',
        'size' => $lineItems[0]->size ?? '',
        'color' => $lineItems[0]->color ?? '',
        'personalization' => $lineItems[0]->personalization ?? '',
        'seller_note' => $lineItems[0]->seller_note ?? '',
        'notes' => '',
        'status' => QcLineItemStatus::Ready->value,
    ];

    // Second item: Missing
    $lineItemChecks[] = [
        'line_item_id' => $lineItems[1]->id,
        'index' => 2,
        'product_name' => $lineItems[1]->product_name,
        'product_url' => '',
        'product_image_url' => $lineItems[1]->product_image_url,
        'quantity' => $lineItems[1]->quantity,
        'attributes' => '',
        'size' => $lineItems[1]->size ?? '',
        'color' => $lineItems[1]->color ?? '',
        'personalization' => $lineItems[1]->personalization ?? '',
        'seller_note' => $lineItems[1]->seller_note ?? '',
        'notes' => '',
        'status' => QcLineItemStatus::Missing->value,
    ];

    // Third item: Ready
    $lineItemChecks[] = [
        'line_item_id' => $lineItems[2]->id,
        'index' => 3,
        'product_name' => $lineItems[2]->product_name,
        'product_url' => '',
        'product_image_url' => $lineItems[2]->product_image_url,
        'quantity' => $lineItems[2]->quantity,
        'attributes' => '',
        'size' => $lineItems[2]->size ?? '',
        'color' => $lineItems[2]->color ?? '',
        'personalization' => $lineItems[2]->personalization ?? '',
        'seller_note' => $lineItems[2]->seller_note ?? '',
        'notes' => '',
        'status' => QcLineItemStatus::Ready->value,
    ];

    // Test the component - should not complete the order immediately due to missing items
    Livewire::test(QcFormWizard::class, ['qcSession' => $this->qcSession])
        ->set('qualityCheckData', [
            'order_check_id' => $this->orderCheck->id,
            'line_item_checks' => $lineItemChecks,
        ])
        ->call('confirmOrder');

    // Verify order check status remains unchanged (should trigger confirmation dialog)
    $this->orderCheck->refresh();
    expect($this->orderCheck->status)->toBe(QcOrderCheckStatus::Processing);
});
