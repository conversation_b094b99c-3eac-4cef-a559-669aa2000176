<?php

use App\Models\User;

test('seller can not access the operator backoffice', function () {
    $this->be(User::factory()->asSeller()->create());

    $this->get('/backoffice')
        ->assertForbidden();
});

test('seller can access the seller dashboard', function () {
    $this->be(User::factory()->asSeller()->create());

    $this->get('/seller')
        ->assertOk();
});

test('operator can access the operator backoffice', function () {
    $this->be(User::factory()->asOperator()->create());

    $this->get('/backoffice')
        ->assertOk();
});

test('manager can access the operator backoffice', function () {
    $this->be(User::factory()->asManager()->create());

    $this->get('/backoffice')
        ->assertOk();
});

test('operator can not access seller dashboard', function () {
    $this->be(User::factory()->asOperator()->create());

    $this->get('/seller')
        ->assertForbidden();
});

test('manager can not access seller dashboard', function () {
    $this->be(User::factory()->asManager()->create());

    $this->get('/seller')
        ->assertForbidden();
});
