<?php

use App\Actions\SplitTrackingNumbers;

test('can split tracking numbers separated by newlines', function () {
    $input = "ABC123456789\nDEF987654321\nGHI456789123";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
    ]);
});

test('can split tracking numbers separated by commas', function () {
    $input = 'ABC123456789,DEF987654321,GHI456789123';

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
    ]);
});

test('can split tracking numbers with mixed separators', function () {
    $input = "ABC123456789\nDEF987654321,GHI456789123\nJKL789123456";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
        'JKL789123456',
    ]);
});

test('handles empty lines and extra whitespace', function () {
    $input = "  ABC123456789  \n\n  DEF987654321  ,  GHI456789123  \n\n  JKL789123456  ";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
        'JKL789123456',
    ]);
});

test('removes duplicate tracking numbers', function () {
    $input = "ABC123456789\nDEF987654321\nABC123456789\nGHI456789123\nDEF987654321";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
    ]);
});

test('handles empty input', function () {
    $input = '';

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([]);
});

test('handles input with only whitespace and separators', function () {
    $input = "  \n\n  ,  \n  ";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([]);
});

test('handles single tracking number', function () {
    $input = 'ABC123456789';

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe(['ABC123456789']);
});

test('handles single tracking number with whitespace', function () {
    $input = '  ABC123456789  ';

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe(['ABC123456789']);
});

test('handles windows line endings', function () {
    $input = "ABC123456789\r\nDEF987654321\r\nGHI456789123";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
    ]);
});

test('handles complex real-world input', function () {
    $input = "  1Z999AA1234567890  \n\n  9214490324919300821456  ,  FX123456789012345678  \r\n  \n  1Z999AA1234567890  \n  YT1234567890123456  ";

    $result = SplitTrackingNumbers::make()->handle($input);

    expect($result)->toBe([
        '1Z999AA1234567890',
        '9214490324919300821456',
        'FX123456789012345678',
        'YT1234567890123456',
    ]);
});
