<?php

use App\Actions\CheckOrderCancelEligibility;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('new order is eligible for cancellation', function () {
    $order = Order::factory()->create(['status' => OrderStatus::New]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeTrue();
    expect($result['reason'])->toBe('Order can be cancelled.');
});

test('draft order is eligible for cancellation', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Draft]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeTrue();
    expect($result['reason'])->toBe('Draft order can be cancelled.');
});

test('cancelled order is not eligible for cancellation', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Cancelled]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeFalse();
    expect($result['reason'])->toBe('Order is already cancelled.');
});

test('shipped order is not eligible for cancellation', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Shipped]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeFalse();
    expect($result['reason'])->toBe('Order has already been shipped and cannot be cancelled.');
});

test('order with pending cancel request is not eligible', function () {
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $seller = User::factory()->create(['role' => 'seller']);

    OrderCancelRequest::factory()->pending()->create([
        'order_id' => $order->id,
        'seller_id' => $seller->id,
    ]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeFalse();
    expect($result['reason'])->toBe('Order already has a pending cancel request.');
});

test('processing order is eligible for cancellation', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeTrue();
    expect($result['reason'])->toBe('Processing order can be cancelled.');
});

test('partially shipped order is eligible with warning', function () {
    $order = Order::factory()->create(['status' => OrderStatus::PartialShipped]);

    $result = CheckOrderCancelEligibility::make()->handle($order);

    expect($result['eligible'])->toBeTrue();
    expect($result['reason'])->toBe('Remaining unshipped items can be cancelled.');
    expect($result['warning'])->toBeTrue();
});
