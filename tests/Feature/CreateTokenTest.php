<?php

use App\Actions\CreateAccessToken;
use App\Models\User;

test('user can create personal api token', function () {
    $user = User::factory()->create();
    $token = CreateAccessToken::make()->handle($user, 'Test Token');

    $this->assertNotEmpty($token);

    $this->assertDatabaseHas('personal_access_tokens', [
        'tokenable_id' => $user->id,
        'name' => 'Test Token',
    ]);

    $response = $this->actingAs($user, 'sanctum')->get('/api/user');
    $response->assertStatus(200)
        ->assertJson([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
        ]);
});
