<?php

use App\Actions\CreateOrderCancelRequest;
use App\Actions\ProcessOrderCancelRequest;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Validation\ValidationException;

beforeEach(function () {
    $this->seller = User::factory()->create(['role' => 'seller']);
    $this->operator = User::factory()->create(['role' => 'operator']);
    $this->order = Order::factory()->create([
        'seller_id' => $this->seller->id,
        'status' => OrderStatus::New,
    ]);
});

test('seller can create cancel request for their order', function () {
    $reason = 'Customer requested cancellation due to change of mind.';

    $cancelRequest = CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->seller,
        $reason
    );

    expect($cancelRequest)->toBeInstanceOf(OrderCancelRequest::class);
    expect($cancelRequest->order_id)->toBe($this->order->id);
    expect($cancelRequest->seller_id)->toBe($this->seller->id);
    expect($cancelRequest->reason)->toBe($reason);
    expect($cancelRequest->status)->toBe(OrderCancelRequestStatus::Pending);
});

test('seller cannot create cancel request for order they do not own', function () {
    $otherSeller = User::factory()->create(['role' => 'seller']);
    $reason = 'Customer requested cancellation.';

    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $otherSeller,
        $reason
    ))->toThrow(ValidationException::class);
});

test('non-seller cannot create cancel request', function () {
    $reason = 'Customer requested cancellation.';

    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->operator,
        $reason
    ))->toThrow(ValidationException::class);
});

test('cancel request requires valid reason', function () {
    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->seller,
        ''
    ))->toThrow(ValidationException::class);

    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->seller,
        'short'
    ))->toThrow(ValidationException::class);
});

test('cannot create cancel request for shipped order', function () {
    $this->order->update(['status' => OrderStatus::Shipped]);

    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->seller,
        'Customer requested cancellation.'
    ))->toThrow(ValidationException::class);
});

test('cannot create cancel request when one already exists', function () {
    OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    expect(fn () => CreateOrderCancelRequest::make()->handle(
        $this->order,
        $this->seller,
        'Another cancellation request.'
    ))->toThrow(ValidationException::class);
});

test('operator can approve cancel request', function () {
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $processedRequest = ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->operator,
        true,
        'Approved as requested'
    );

    expect($processedRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($processedRequest->processed_by)->toBe($this->operator->id);
    expect($processedRequest->processed_at)->not->toBeNull();
    expect($processedRequest->feedback_note)->toBe('Approved as requested');

    // Check that order status is updated
    $this->order->refresh();
    expect($this->order->status)->toBe(OrderStatus::Cancelled);
});

test('operator can reject cancel request', function () {
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $processedRequest = ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->operator,
        false,
        'Order already in production'
    );

    expect($processedRequest->status)->toBe(OrderCancelRequestStatus::Rejected);
    expect($processedRequest->processed_by)->toBe($this->operator->id);
    expect($processedRequest->processed_at)->not->toBeNull();
    expect($processedRequest->feedback_note)->toBe('Order already in production');

    // Check that order status is NOT updated
    $this->order->refresh();
    expect($this->order->status)->toBe(OrderStatus::New);
});

test('rejection requires feedback note', function () {
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    expect(fn () => ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->operator,
        false,
        ''
    ))->toThrow(ValidationException::class);
});

test('seller cannot process cancel request for orders they do not own', function () {
    $anotherSeller = User::factory()->create(['role' => 'seller']);
    $anotherOrder = Order::factory()->create([
        'seller_id' => $anotherSeller->id,
        'status' => OrderStatus::New,
    ]);

    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $anotherOrder->id,
        'seller_id' => $anotherSeller->id,
    ]);

    // This seller should NOT be able to process another seller's cancel request
    expect(fn () => ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->seller,
        true
    ))->toThrow(ValidationException::class);
});

test('cannot process already processed cancel request', function () {
    $cancelRequest = OrderCancelRequest::factory()->approved()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
        'processed_by' => $this->operator->id,
    ]);

    expect(fn () => ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->operator,
        false,
        'Trying to reject approved request'
    ))->toThrow(ValidationException::class);
});

test('order model methods work correctly', function () {
    expect($this->order->hasPendingCancelRequest())->toBeFalse();
    expect($this->order->canBeCancelled())->toBeTrue();
    expect($this->order->getLatestCancelRequest())->toBeNull();

    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $this->order->refresh();

    expect($this->order->hasPendingCancelRequest())->toBeTrue();
    expect($this->order->canBeCancelled())->toBeFalse();
    expect($this->order->getLatestCancelRequest()->id)->toBe($cancelRequest->id);
});

test('seller can immediately cancel draft order', function () {
    // Create a draft order
    $draftOrder = Order::factory()->create([
        'seller_id' => $this->seller->id,
        'status' => OrderStatus::Draft,
    ]);

    $reason = 'Customer requested cancellation before order confirmation.';

    $cancelRequest = CreateOrderCancelRequest::make()->handle(
        $draftOrder,
        $this->seller,
        $reason
    );

    // Verify the cancel request was created as approved and processed
    expect($cancelRequest)->toBeInstanceOf(OrderCancelRequest::class);
    expect($cancelRequest->order_id)->toBe($draftOrder->id);
    expect($cancelRequest->seller_id)->toBe($this->seller->id);
    expect($cancelRequest->reason)->toBe($reason);
    expect($cancelRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($cancelRequest->processed_at)->not->toBeNull();
    expect($cancelRequest->processed_by)->toBe($this->seller->id);

    // Verify the order was actually cancelled
    $draftOrder->refresh();
    expect($draftOrder->status)->toBe(OrderStatus::Cancelled);
});

test('backup seller can immediately cancel draft order', function () {
    $backupSeller = User::factory()->create(['role' => 'seller']);

    // Create a draft order with backup seller
    $draftOrder = Order::factory()->create([
        'seller_id' => $this->seller->id,
        'backup_seller_id' => $backupSeller->id,
        'status' => OrderStatus::Draft,
    ]);

    $reason = 'Backup seller cancelling draft order.';

    $cancelRequest = CreateOrderCancelRequest::make()->handle(
        $draftOrder,
        $backupSeller,
        $reason
    );

    // Verify the cancel request was created as approved
    expect($cancelRequest)->toBeInstanceOf(OrderCancelRequest::class);
    expect($cancelRequest->seller_id)->toBe($backupSeller->id);
    expect($cancelRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($cancelRequest->processed_by)->toBe($backupSeller->id);

    // Verify the order was actually cancelled
    $draftOrder->refresh();
    expect($draftOrder->status)->toBe(OrderStatus::Cancelled);
});

test('seller can process their own cancel request for draft orders', function () {
    $draftOrder = Order::factory()->create([
        'seller_id' => $this->seller->id,
        'status' => OrderStatus::Draft,
    ]);

    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $draftOrder->id,
        'seller_id' => $this->seller->id,
    ]);

    // Seller should be able to process their own cancel request for draft orders
    $processedRequest = ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->seller,
        true
    );

    expect($processedRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($processedRequest->processed_by)->toBe($this->seller->id);
    expect($processedRequest->processed_at)->not->toBeNull();

    // Check that order status is updated
    $draftOrder->refresh();
    expect($draftOrder->status)->toBe(OrderStatus::Cancelled);
});

test('seller can process their own cancel request for any order they own', function () {
    // Test with a "New" order (non-draft)
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id, // This is a "New" order
        'seller_id' => $this->seller->id,
    ]);

    // Seller should be able to process their own cancel request for orders they own
    $processedRequest = ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->seller,
        true
    );

    expect($processedRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($processedRequest->processed_by)->toBe($this->seller->id);
    expect($processedRequest->processed_at)->not->toBeNull();

    // Check that order status is updated
    $this->order->refresh();
    expect($this->order->status)->toBe(OrderStatus::Cancelled);
});

test('seller cannot process another sellers cancel request even for draft orders', function () {
    $anotherSeller = User::factory()->create(['role' => 'seller']);

    $draftOrder = Order::factory()->create([
        'seller_id' => $anotherSeller->id,
        'status' => OrderStatus::Draft,
    ]);

    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $draftOrder->id,
        'seller_id' => $anotherSeller->id,
    ]);

    // This seller should NOT be able to process another seller's cancel request
    expect(fn () => ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->seller,
        true
    ))->toThrow(ValidationException::class);
});

test('ProcessOrderCancelRequest validation logic works correctly', function () {
    // Test that operators can process any cancel request
    $anotherSeller = User::factory()->create(['role' => 'seller']);
    $anotherOrder = Order::factory()->create([
        'seller_id' => $anotherSeller->id,
        'status' => OrderStatus::New,
    ]);

    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $anotherOrder->id,
        'seller_id' => $anotherSeller->id,
    ]);

    // Operator should be able to process any cancel request
    $processedRequest = ProcessOrderCancelRequest::make()->handle(
        $cancelRequest,
        $this->operator,
        true,
        'Approved by operator'
    );

    expect($processedRequest->status)->toBe(OrderCancelRequestStatus::Approved);
    expect($processedRequest->processed_by)->toBe($this->operator->id);

    // Manager should also be able to process any cancel request
    $managerUser = User::factory()->create(['role' => 'manager']);
    $anotherCancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $anotherOrder->id,
        'seller_id' => $anotherSeller->id,
    ]);

    $processedByManager = ProcessOrderCancelRequest::make()->handle(
        $anotherCancelRequest,
        $managerUser,
        false,
        'Rejected by manager'
    );

    expect($processedByManager->status)->toBe(OrderCancelRequestStatus::Rejected);
    expect($processedByManager->processed_by)->toBe($managerUser->id);
});
