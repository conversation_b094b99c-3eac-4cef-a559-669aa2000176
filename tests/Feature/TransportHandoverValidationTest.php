<?php

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\ShipmentStatus;
use App\Models\Carrier;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Shipment;
use App\Models\ThirdPartyLogistic;
use App\Models\TransportHandover;

test('transport handover can parse tracking numbers correctly', function () {
    $handover = new TransportHandover([
        'tracking_numbers' => "ABC123456789\nDEF987654321,GHI456789123\n\nJKL789123456",
    ]);

    $parsed = $handover->getParsedTrackingNumbers();

    expect($parsed)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
        'JKL789123456',
    ]);
});

test('transport handover validation identifies not found tracking numbers', function () {
    // Create a carrier and shipment
    $carrier = Carrier::factory()->create();
    $existingShipment = Shipment::factory()->create([
        'tracking_number' => 'EXISTING123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    $handover = new TransportHandover([
        'tracking_numbers' => "EXISTING123\nNOTFOUND456",
    ]);

    $codes = $handover->getParsedTrackingNumbers();

    $validationResults = [];
    $invalidCodes = [];
    $validCodes = [];

    foreach ($codes as $code) {
        $shipment = Shipment::where('tracking_number', $code)
            ->where('packer', 'TDA')
            ->first();

        if (! $shipment) {
            $validationResults[] = "{$code} - not found";
            $invalidCodes[] = $code;
        } elseif ($shipment->status !== ShipmentStatus::Pending) {
            $statusLabel = match ($shipment->status) {
                ShipmentStatus::PickedUp => 'already picked up',
                ShipmentStatus::InTransit => 'in transit',
                ShipmentStatus::OutForDelivery => 'out for delivery',
                ShipmentStatus::Delivered => 'delivered',
                ShipmentStatus::Failed => 'failed',
                ShipmentStatus::Returned => 'returned',
                ShipmentStatus::Cancelled => 'cancelled',
                default => 'invalid status'
            };
            $validationResults[] = "{$code} - {$statusLabel} (only pending shipments allowed)";
            $invalidCodes[] = $code;
        } else {
            $validCodes[] = $code;
        }
    }

    expect($validCodes)->toBe(['EXISTING123']);
    expect($invalidCodes)->toBe(['NOTFOUND456']);
    expect($validationResults)->toBe(['NOTFOUND456 - not found']);
});

test('transport handover validation identifies already picked up tracking numbers', function () {
    // Create a carrier and shipment with picked up status
    $carrier = Carrier::factory()->create();
    $pickedUpShipment = Shipment::factory()->create([
        'tracking_number' => 'PICKEDUP123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::PickedUp,
        'carrier_id' => $carrier->id,
    ]);

    $handover = new TransportHandover([
        'tracking_numbers' => 'PICKEDUP123',
    ]);

    $codes = $handover->getParsedTrackingNumbers();

    $validationResults = [];
    $invalidNumbers = [];
    $validCodes = [];

    foreach ($codes as $code) {
        $shipment = Shipment::where('tracking_number', $code)
            ->where('packer', 'TDA')
            ->first();

        if (! $shipment) {
            $validationResults[] = "{$code} - not found";
            $invalidNumbers[] = $code;
        } elseif ($shipment->status !== ShipmentStatus::Pending) {
            $statusLabel = match ($shipment->status) {
                ShipmentStatus::PickedUp => 'already picked up',
                ShipmentStatus::InTransit => 'in transit',
                ShipmentStatus::OutForDelivery => 'out for delivery',
                ShipmentStatus::Delivered => 'delivered',
                ShipmentStatus::Failed => 'failed',
                ShipmentStatus::Returned => 'returned',
                ShipmentStatus::Cancelled => 'cancelled',
                default => 'invalid status'
            };
            $validationResults[] = "{$code} - {$statusLabel} (only pending shipments allowed)";
            $invalidNumbers[] = $code;
        } else {
            $validCodes[] = $code;
        }
    }

    expect($validCodes)->toBe([]);
    expect($invalidNumbers)->toBe(['PICKEDUP123']);
    expect($validationResults)->toBe(['PICKEDUP123 - already picked up (only pending shipments allowed)']);
});

test('transport handover validation identifies tracking numbers already assigned to 3PL', function () {
    // Create a carrier, 3PL, and shipment already assigned to 3PL
    $carrier = Carrier::factory()->create();
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create();

    $assignedShipment = Shipment::factory()->create([
        'tracking_number' => 'ASSIGNED123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
        'third_party_logistic_id' => $thirdPartyLogistic->id,
    ]);

    $handover = new TransportHandover([
        'tracking_numbers' => 'ASSIGNED123',
    ]);

    $codes = $handover->getParsedTrackingNumbers();

    $validationResults = [];
    $invalidNumbers = [];
    $validCodes = [];

    foreach ($codes as $code) {
        $shipment = Shipment::where('tracking_number', $code)
            ->where('packer', 'TDA')
            ->first();

        if (! $shipment) {
            $validationResults[] = "{$code} - not found";
            $invalidNumbers[] = $code;
        } elseif ($shipment->status !== ShipmentStatus::Pending) {
            $statusLabel = match ($shipment->status) {
                ShipmentStatus::PickedUp => 'already picked up',
                ShipmentStatus::InTransit => 'in transit',
                ShipmentStatus::OutForDelivery => 'out for delivery',
                ShipmentStatus::Delivered => 'delivered',
                ShipmentStatus::Failed => 'failed',
                ShipmentStatus::Returned => 'returned',
                ShipmentStatus::Cancelled => 'cancelled',
                default => 'invalid status'
            };
            $validationResults[] = "{$code} - {$statusLabel} (only pending shipments allowed)";
            $invalidNumbers[] = $code;
        } elseif ($shipment->third_party_logistic_id !== null) {
            $validationResults[] = "{$code} - already assigned to 3PL";
            $invalidNumbers[] = $code;
        } else {
            $validCodes[] = $code;
        }
    }

    expect($validCodes)->toBe([]);
    expect($invalidNumbers)->toBe(['ASSIGNED123']);
    expect($validationResults)->toBe(['ASSIGNED123 - already assigned to 3PL']);
});

test('transport handover validation allows tracking numbers with null 3PL assignment', function () {
    // Create a carrier and shipment with null third_party_logistic_id
    $carrier = Carrier::factory()->create();

    $validShipment = Shipment::factory()->create([
        'tracking_number' => 'VALID123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
        'third_party_logistic_id' => null,
    ]);

    $handover = new TransportHandover([
        'tracking_numbers' => 'VALID123',
    ]);

    $codes = $handover->getParsedTrackingNumbers();

    $validationResults = [];
    $invalidNumbers = [];
    $validCodes = [];

    foreach ($codes as $code) {
        $shipment = Shipment::where('tracking_number', $code)
            ->where('packer', 'TDA')
            ->first();

        if (! $shipment) {
            $validationResults[] = "{$code} - not found";
            $invalidNumbers[] = $code;
        } elseif ($shipment->status !== ShipmentStatus::Pending) {
            $statusLabel = match ($shipment->status) {
                ShipmentStatus::PickedUp => 'already picked up',
                ShipmentStatus::InTransit => 'in transit',
                ShipmentStatus::OutForDelivery => 'out for delivery',
                ShipmentStatus::Delivered => 'delivered',
                ShipmentStatus::Failed => 'failed',
                ShipmentStatus::Returned => 'returned',
                ShipmentStatus::Cancelled => 'cancelled',
                default => 'invalid status'
            };
            $validationResults[] = "{$code} - {$statusLabel} (only pending shipments allowed)";
            $invalidNumbers[] = $code;
        } elseif ($shipment->third_party_logistic_id !== null) {
            $validationResults[] = "{$code} - already assigned to 3PL";
            $invalidNumbers[] = $code;
        } else {
            $validCodes[] = $code;
        }
    }

    expect($validCodes)->toBe(['VALID123']);
    expect($invalidNumbers)->toBe([]);
    expect($validationResults)->toBe([]);
});

test('split tracking numbers action works correctly', function () {
    $trackingNumbers = "ABC123456789\nDEF987654321,GHI456789123\n\nJKL789123456";

    $result = \App\Actions\SplitTrackingNumbers::make()->handle($trackingNumbers);

    expect($result)->toBe([
        'ABC123456789',
        'DEF987654321',
        'GHI456789123',
        'JKL789123456',
    ]);
});

test('transport handover validation identifies tracking numbers with all cancelled line items', function () {
    // Create a carrier and shipment
    $carrier = Carrier::factory()->create();
    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
    ]);

    // Update the order number_portal after creation to override the auto-generated one
    $order->update(['number_portal' => '1465989-AA0001']);

    $shipment = Shipment::factory()->create([
        'tracking_number' => 'CANCELLED123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    // Create line items that are all cancelled
    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::Cancelled,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '1465989-AA0001-02',
        'status' => LineItemStatus::Cancelled,
    ]);

    // Test validation logic
    $shipmentWithLineItems = Shipment::where('tracking_number', 'CANCELLED123')
        ->where('packer', 'TDA')
        ->with(['lineItems.order'])
        ->first();

    $lineItems = $shipmentWithLineItems->lineItems;
    $cancelledLineItems = $lineItems->where('status', LineItemStatus::Cancelled);

    // Group by orders to count orders with cancelled items
    $allOrders = $lineItems->pluck('order')->unique('id');
    $ordersWithCancelledItems = $cancelledLineItems->pluck('order')->unique('id');
    $totalOrders = $allOrders->count();
    $cancelledOrdersCount = $ordersWithCancelledItems->count();

    expect($totalOrders)->toBe(1);
    expect($cancelledOrdersCount)->toBe(1);
    expect($cancelledOrdersCount === $totalOrders)->toBeTrue();

    // Verify the validation message format
    $orderNumbers = $ordersWithCancelledItems->pluck('number_portal')->implode(', ');
    $lineItemNumbers = $cancelledLineItems->pluck('number_portal')->implode(', ');
    $expectedMessage = "CANCELLED123 - all orders cancelled (orders: {$orderNumbers} - line items: {$lineItemNumbers})";

    expect($orderNumbers)->toBe('1465989-AA0001');
    expect($lineItemNumbers)->toBe('1465989-AA0001-01, 1465989-AA0001-02');
    expect($expectedMessage)->toBe('CANCELLED123 - all orders cancelled (orders: 1465989-AA0001 - line items: 1465989-AA0001-01, 1465989-AA0001-02)');
});

test('transport handover validation identifies tracking numbers with some cancelled orders as warnings', function () {
    // Create a carrier and shipment
    $carrier = Carrier::factory()->create();

    // Create two orders
    $order1 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order1->update(['number_portal' => '2465989-AA0002']);

    $order2 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order2->update(['number_portal' => '3465989-AA0003']);

    $shipment = Shipment::factory()->create([
        'tracking_number' => 'PARTIAL123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    // Create line items - order1 has cancelled items, order2 has active items
    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order1->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '2465989-AA0002-01',
        'status' => LineItemStatus::Cancelled,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '3465989-AA0003-01',
        'status' => LineItemStatus::Packed,
    ]);

    $lineItem3 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '3465989-AA0003-02',
        'status' => LineItemStatus::InProduction,
    ]);

    // Test validation logic
    $shipmentWithLineItems = Shipment::where('tracking_number', 'PARTIAL123')
        ->where('packer', 'TDA')
        ->with(['lineItems.order'])
        ->first();

    $lineItems = $shipmentWithLineItems->lineItems;
    $cancelledLineItems = $lineItems->where('status', LineItemStatus::Cancelled);

    // Group by orders to count orders with cancelled items
    $allOrders = $lineItems->pluck('order')->unique('id');
    $ordersWithCancelledItems = $cancelledLineItems->pluck('order')->unique('id');
    $totalOrders = $allOrders->count();
    $cancelledOrdersCount = $ordersWithCancelledItems->count();

    expect($totalOrders)->toBe(2);
    expect($cancelledOrdersCount)->toBe(1);
    expect($cancelledOrdersCount > 0 && $cancelledOrdersCount < $totalOrders)->toBeTrue();

    // Verify the warning message format
    $orderNumbers = $ordersWithCancelledItems->pluck('number_portal')->implode(', ');
    $expectedMessage = "PARTIAL123 - {$cancelledOrdersCount}/{$totalOrders} orders cancelled (orders: {$orderNumbers})";

    expect($orderNumbers)->toBe('2465989-AA0002');
    expect($expectedMessage)->toBe('PARTIAL123 - 1/2 orders cancelled (orders: 2465989-AA0002)');
});

test('transport handover validation allows tracking numbers with no cancelled line items', function () {
    // Create a carrier and shipment
    $carrier = Carrier::factory()->create();
    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '3465989-AA0003',
    ]);

    $shipment = Shipment::factory()->create([
        'tracking_number' => 'VALID123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    // Create line items with no cancelled statuses
    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '3465989-AA0003-01',
        'status' => LineItemStatus::Packed,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
        'number_portal' => '3465989-AA0003-02',
        'status' => LineItemStatus::InProduction,
    ]);

    // Test validation logic
    $shipmentWithLineItems = Shipment::where('tracking_number', 'VALID123')
        ->where('packer', 'TDA')
        ->with(['lineItems.order'])
        ->first();

    $lineItems = $shipmentWithLineItems->lineItems;
    $cancelledLineItems = $lineItems->where('status', LineItemStatus::Cancelled);

    // Group by orders to count orders with cancelled items
    $allOrders = $lineItems->pluck('order')->unique('id');
    $ordersWithCancelledItems = $cancelledLineItems->pluck('order')->unique('id');
    $totalOrders = $allOrders->count();
    $cancelledOrdersCount = $ordersWithCancelledItems->count();

    expect($totalOrders)->toBe(1);
    expect($cancelledOrdersCount)->toBe(0);

    // This should be considered valid (no cancelled orders)
    expect($cancelledOrdersCount === 0)->toBeTrue();
});
