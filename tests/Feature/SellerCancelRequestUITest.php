<?php

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seller = User::factory()->create(['role' => 'seller']);
    $this->operator = User::factory()->create(['role' => 'operator']);
    $this->order = Order::factory()->create([
        'seller_id' => $this->seller->id,
        'status' => OrderStatus::New,
    ]);
});

test('seller can access cancel requests list page', function () {
    $this->actingAs($this->seller);

    $response = $this->get('/seller/cancel-requests');

    $response->assertStatus(200);
    // Should be able to access the cancel requests list page
});

test('seller can see create new cancel request button', function () {
    $this->actingAs($this->seller);

    $response = $this->get('/seller/cancel-requests');

    $response->assertStatus(200);
    // Should see the "Create New Cancel Request" button
});

test('seller can view their cancel requests list', function () {
    // Create some cancel requests for the seller
    OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    OrderCancelRequest::factory()->approved()->create([
        'order_id' => Order::factory()->create(['seller_id' => $this->seller->id])->id,
        'seller_id' => $this->seller->id,
        'processed_by' => $this->operator->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get('/seller/cancel-requests');

    $response->assertStatus(200);
    // Should see both cancel requests
});

test('seller can only see their own cancel requests', function () {
    $otherSeller = User::factory()->create(['role' => 'seller']);
    $otherOrder = Order::factory()->create(['seller_id' => $otherSeller->id]);

    // Create cancel request for current seller
    $myRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    // Create cancel request for other seller
    $otherRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $otherOrder->id,
        'seller_id' => $otherSeller->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get('/seller/cancel-requests');

    $response->assertStatus(200);
    // Should only see own cancel request, not the other seller's
});

test('seller can view individual cancel request details', function () {
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get("/seller/cancel-requests/{$cancelRequest->id}");

    $response->assertStatus(200);
});

test('seller cannot view other sellers cancel requests', function () {
    $otherSeller = User::factory()->create(['role' => 'seller']);
    $otherOrder = Order::factory()->create(['seller_id' => $otherSeller->id]);

    $otherRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $otherOrder->id,
        'seller_id' => $otherSeller->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get("/seller/cancel-requests/{$otherRequest->id}");

    $response->assertStatus(404);
});

test('seller sees warning banner for orders with pending cancel requests', function () {
    OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get("/seller/orders/{$this->order->id}");

    $response->assertStatus(200);
    // Should see warning banner
});

test('seller sees cancel request history in order details', function () {
    $cancelRequest = OrderCancelRequest::factory()->approved()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
        'processed_by' => $this->operator->id,
        'feedback_note' => 'Approved as requested',
    ]);

    $this->actingAs($this->seller);

    $response = $this->get("/seller/orders/{$this->order->id}");

    $response->assertStatus(200);
    // Should see cancel request history section
});

test('navigation badge shows pending cancel requests count', function () {
    // Create multiple pending cancel requests
    OrderCancelRequest::factory()->pending()->count(3)->create([
        'seller_id' => $this->seller->id,
    ]);

    // Create one approved request (should not count)
    OrderCancelRequest::factory()->approved()->create([
        'seller_id' => $this->seller->id,
        'processed_by' => $this->operator->id,
    ]);

    $this->actingAs($this->seller);

    $response = $this->get('/seller');

    $response->assertStatus(200);
    // Navigation should show badge with count of 3
});

test('seller can delete pending cancel request', function () {
    $cancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
    ]);

    $this->actingAs($this->seller);

    // Verify the cancel request exists
    expect(OrderCancelRequest::find($cancelRequest->id))->not->toBeNull();

    // Delete the cancel request
    $cancelRequest->delete();

    // Verify the cancel request is deleted
    expect(OrderCancelRequest::find($cancelRequest->id))->toBeNull();

    // Verify the order can now be cancelled again
    expect($this->order->fresh()->hasPendingCancelRequest())->toBeFalse();
    expect($this->order->fresh()->canBeCancelled())->toBeTrue();
});

test('seller cannot delete processed cancel request', function () {
    $cancelRequest = OrderCancelRequest::factory()->approved()->create([
        'order_id' => $this->order->id,
        'seller_id' => $this->seller->id,
        'processed_by' => $this->operator->id,
    ]);

    $this->actingAs($this->seller);

    // Verify the cancel request is not pending
    expect($cancelRequest->isPending())->toBeFalse();
    expect($cancelRequest->isApproved())->toBeTrue();
});

test('seller can only delete their own cancel requests', function () {
    $otherSeller = User::factory()->create(['role' => 'seller']);
    $otherOrder = Order::factory()->create(['seller_id' => $otherSeller->id]);

    $otherCancelRequest = OrderCancelRequest::factory()->pending()->create([
        'order_id' => $otherOrder->id,
        'seller_id' => $otherSeller->id,
    ]);

    $this->actingAs($this->seller);

    // Verify the seller cannot access other seller's cancel request
    $response = $this->get("/seller/cancel-requests/{$otherCancelRequest->id}");
    $response->assertStatus(404);
});
