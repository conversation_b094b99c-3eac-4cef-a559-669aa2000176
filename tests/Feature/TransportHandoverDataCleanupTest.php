<?php

use App\Enums\ShipmentStatus;
use App\Filament\Backoffice\Resources\TransportHandovers\Concerns\CleansUpTrackingNumberData;
use App\Models\Carrier;
use App\Models\Shipment;
use App\Models\ThirdPartyLogistic;
use App\Models\TransportHandover;
use App\Models\User;

test('trait cleans up tracking number data correctly', function () {
    // Create a test class that uses the trait
    $testClass = new class
    {
        use CleansUpTrackingNumberData;

        public function testCleanUp(array $data): array
        {
            return $this->cleanUpTrackingNumberData($data);
        }
    };

    // Test data with invalid tracking numbers
    $formData = [
        'third_party_logistic_id' => 1,
        'tracking_numbers' => "VALID123\nINVALID456\nVALID789",
        'invalid_tracking_numbers' => ['INVALID456'],
        'validation_results' => 'INVALID456 - not found',
        'other_field' => 'should remain',
    ];

    $cleanedData = $testClass->testCleanUp($formData);

    // Verify that invalid tracking numbers were removed from tracking_numbers
    expect($cleanedData['tracking_numbers'])->toBe("VALID123\nVALID789");

    // Verify that validation fields were removed
    expect($cleanedData)->not->toHaveKey('invalid_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('validation_results');

    // Verify that other fields remain unchanged
    expect($cleanedData['third_party_logistic_id'])->toBe(1);
    expect($cleanedData['other_field'])->toBe('should remain');
});

test('trait cleans up warning tracking number data correctly', function () {
    // Create a test class that uses the trait
    $testClass = new class
    {
        use CleansUpTrackingNumberData;

        public function testCleanUp(array $data): array
        {
            return $this->cleanUpTrackingNumberData($data);
        }
    };

    // Test data with warning tracking numbers
    $formData = [
        'third_party_logistic_id' => 1,
        'tracking_numbers' => "VALID123\nWARNING456\nVALID789",
        'warning_tracking_numbers' => ['WARNING456'],
        'warning_results' => 'WARNING456 - 1/2 orders cancelled (orders: 2465989-AA0002)',
        'other_field' => 'should remain',
    ];

    $cleanedData = $testClass->testCleanUp($formData);

    // Verify that tracking numbers remain unchanged (warnings don't remove tracking numbers)
    expect($cleanedData['tracking_numbers'])->toBe("VALID123\nWARNING456\nVALID789");

    // Verify that warning fields were removed
    expect($cleanedData)->not->toHaveKey('warning_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('warning_results');

    // Verify that other fields remain unchanged
    expect($cleanedData['third_party_logistic_id'])->toBe(1);
    expect($cleanedData['other_field'])->toBe('should remain');
});

test('trait handles empty invalid tracking numbers', function () {
    $testClass = new class
    {
        use CleansUpTrackingNumberData;

        public function testCleanUp(array $data): array
        {
            return $this->cleanUpTrackingNumberData($data);
        }
    };

    $formData = [
        'tracking_numbers' => "VALID123\nVALID456",
        'invalid_tracking_numbers' => [],
        'validation_results' => '',
    ];

    $cleanedData = $testClass->testCleanUp($formData);

    // Verify that tracking numbers remain unchanged when no invalid numbers exist
    expect($cleanedData['tracking_numbers'])->toBe("VALID123\nVALID456");

    // Verify that validation fields were still removed
    expect($cleanedData)->not->toHaveKey('invalid_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('validation_results');
});

test('create transport handover cleans up invalid tracking numbers from data', function () {
    // Create test data
    $user = User::factory()->create();
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create();
    $carrier = Carrier::factory()->create();

    // Create some shipments - one valid (pending), one invalid (picked up)
    $validShipment = Shipment::factory()->create([
        'tracking_number' => 'VALID123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    $invalidShipment = Shipment::factory()->create([
        'tracking_number' => 'INVALID456',
        'packer' => 'TDA',
        'status' => ShipmentStatus::PickedUp,
        'carrier_id' => $carrier->id,
    ]);

    // Simulate form data with both valid and invalid tracking numbers
    $formData = [
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "VALID123\nINVALID456\nNOTFOUND789",
        'invalid_tracking_numbers' => ['INVALID456', 'NOTFOUND789'],
        'validation_results' => "INVALID456 - already picked up\nNOTFOUND789 - not found",
    ];

    // Create the CreateTransportHandover page instance
    $createPage = new \App\Filament\Backoffice\Resources\TransportHandovers\Pages\CreateTransportHandover;

    // Use reflection to access the protected method
    $reflection = new ReflectionClass($createPage);
    $method = $reflection->getMethod('mutateFormDataBeforeCreate');
    $method->setAccessible(true);

    // Mock the user() function
    $this->actingAs($user);

    // Call the method
    $cleanedData = $method->invoke($createPage, $formData);

    // Verify that invalid tracking numbers were removed from tracking_numbers
    expect($cleanedData['tracking_numbers'])->toBe('VALID123');

    // Verify that validation fields were removed
    expect($cleanedData)->not->toHaveKey('invalid_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('validation_results');

    // Verify that user_id was added
    expect($cleanedData['user_id'])->toBe($user->id);

    // Verify that other fields remain unchanged
    expect($cleanedData['third_party_logistic_id'])->toBe($thirdPartyLogistic->id);
});

test('edit transport handover cleans up invalid tracking numbers from data', function () {
    // Create test data
    $user = User::factory()->create();
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create();
    $carrier = Carrier::factory()->create();

    // Create some shipments
    $validShipment = Shipment::factory()->create([
        'tracking_number' => 'VALID123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    $invalidShipment = Shipment::factory()->create([
        'tracking_number' => 'INVALID456',
        'packer' => 'TDA',
        'status' => ShipmentStatus::PickedUp,
        'carrier_id' => $carrier->id,
    ]);

    // Create existing transport handover
    $handover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => 'VALID123',
        'user_id' => $user->id,
    ]);

    // Simulate form data with both valid and invalid tracking numbers
    $formData = [
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "VALID123\nINVALID456\nNOTFOUND789",
        'invalid_tracking_numbers' => ['INVALID456', 'NOTFOUND789'],
        'validation_results' => "INVALID456 - already picked up\nNOTFOUND789 - not found",
    ];

    // Create the EditTransportHandover page instance
    $editPage = new \App\Filament\Backoffice\Resources\TransportHandovers\Pages\EditTransportHandover;

    // Use reflection to access the protected method
    $reflection = new ReflectionClass($editPage);
    $method = $reflection->getMethod('mutateFormDataBeforeSave');
    $method->setAccessible(true);

    // Call the method
    $cleanedData = $method->invoke($editPage, $formData);

    // Verify that invalid tracking numbers were removed from tracking_numbers
    expect($cleanedData['tracking_numbers'])->toBe('VALID123');

    // Verify that validation fields were removed
    expect($cleanedData)->not->toHaveKey('invalid_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('validation_results');

    // Verify that other fields remain unchanged
    expect($cleanedData['third_party_logistic_id'])->toBe($thirdPartyLogistic->id);
});

test('transport handover data cleanup handles empty invalid tracking numbers', function () {
    $user = User::factory()->create();
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create();

    // Simulate form data with no invalid tracking numbers
    $formData = [
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "VALID123\nVALID456",
        'invalid_tracking_numbers' => [],
        'validation_results' => '',
    ];

    $createPage = new \App\Filament\Backoffice\Resources\TransportHandovers\Pages\CreateTransportHandover;

    // Use reflection to access the protected method
    $reflection = new ReflectionClass($createPage);
    $method = $reflection->getMethod('mutateFormDataBeforeCreate');
    $method->setAccessible(true);

    $this->actingAs($user);

    $cleanedData = $method->invoke($createPage, $formData);

    // Verify that tracking numbers remain unchanged when no invalid numbers exist
    expect($cleanedData['tracking_numbers'])->toBe("VALID123\nVALID456");

    // Verify that validation fields were still removed
    expect($cleanedData)->not->toHaveKey('invalid_tracking_numbers');
    expect($cleanedData)->not->toHaveKey('validation_results');
});

test('transport handover creation works end-to-end with data cleanup', function () {
    $user = User::factory()->create();
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create();
    $carrier = Carrier::factory()->create();

    // Create a valid shipment
    $validShipment = Shipment::factory()->create([
        'tracking_number' => 'VALID123',
        'packer' => 'TDA',
        'status' => ShipmentStatus::Pending,
        'carrier_id' => $carrier->id,
    ]);

    // Create an invalid shipment (already picked up)
    $invalidShipment = Shipment::factory()->create([
        'tracking_number' => 'INVALID456',
        'packer' => 'TDA',
        'status' => ShipmentStatus::PickedUp,
        'carrier_id' => $carrier->id,
    ]);

    $this->actingAs($user);

    // Create transport handover with mixed valid/invalid tracking numbers
    $handover = TransportHandover::create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "VALID123\nINVALID456\nNOTFOUND789",
        'user_id' => $user->id,
    ]);

    // Verify that the handover was created successfully
    expect($handover)->toBeInstanceOf(TransportHandover::class);
    expect($handover->tracking_numbers)->toBe("VALID123\nINVALID456\nNOTFOUND789");
    expect($handover->user_id)->toBe($user->id);
    expect($handover->third_party_logistic_id)->toBe($thirdPartyLogistic->id);

    // Verify that the handover doesn't have validation fields in the database
    $freshHandover = TransportHandover::find($handover->id);
    expect($freshHandover->getAttributes())->not->toHaveKey('invalid_tracking_numbers');
    expect($freshHandover->getAttributes())->not->toHaveKey('validation_results');
});
