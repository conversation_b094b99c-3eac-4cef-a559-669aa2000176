<?php

use App\Actions\ImportLineItemsSuppliers;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Supplier;

test('can import line items suppliers data', function () {
    // Create test data
    $supplier = Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);
    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::New,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportLineItemsSuppliers::make()->handle($tempFile);

    // Verify line item was updated
    $lineItem->refresh();
    expect($lineItem)
        ->supplier_id->toBe($supplier->id)
        ->supplier_order_number->toBe('SO-123456')
        ->design_file_url->toBe('https://example.com/design.png')
        ->status->toBe(LineItemStatus::InProduction);

    // Verify order status was updated
    $order->refresh();
    expect($order->status)->toBe(OrderStatus::Processing);

    // Clean up
    unlink($tempFile);
});

test('validates invalid order_tda_number', function () {
    // Create CSV with non-existent order
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "INVALID-ORDER,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing or invalid 'order_tda_number'.");

    // Clean up
    unlink($tempFile);
});

test('validates invalid line_item_code', function () {
    // Create test data - order exists but line item doesn't
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    // Create CSV with non-existent line item
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,INVALID-LINE-ITEM,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing or invalid 'line_item_code'.");

    // Clean up
    unlink($tempFile);
});

test('validates invalid supplier code', function () {
    // Create test data
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
    ]);

    // Create CSV with non-existent supplier
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,INVALID_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Row 1 is missing supplier or supplier does not exist.');

    // Clean up
    unlink($tempFile);
});

test('validates missing product name', function () {
    // Create test data
    Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
    ]);

    // Create CSV with missing product name
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing 'product_name'.");

    // Clean up
    unlink($tempFile);
});

test('validates missing link design', function () {
    // Create test data
    Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
    ]);

    // Create CSV with missing link design
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, "Row 1 is missing or invalid 'link_design'.");

    // Clean up
    unlink($tempFile);
});

test('validates line item belongs to specified order', function () {
    // Create test data
    Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order1 = Order::factory()->create(['status' => OrderStatus::New]);
    $order1->update(['number_portal' => '1465989-AA0001']);

    $order2 = Order::factory()->create(['status' => OrderStatus::New]);
    $order2->update(['number_portal' => '1465989-AA0002']);

    // Line item belongs to order2, but CSV specifies order1
    LineItem::factory()->create([
        'order_id' => $order2->id,
        'number_portal' => '1465989-AA0001-01',
    ]);

    // Create CSV with mismatched order and line item
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Expect validation to fail
    expect(fn () => ImportLineItemsSuppliers::make()->validate($tempFile))
        ->toThrow(InvalidArgumentException::class, 'Some of `order_number_platform`, `order_tda_number`, `line_item_code` is invalid');

    // Clean up
    unlink($tempFile);
});

test('can import multiple line items in single file', function () {
    // Create test data
    $supplier1 = Supplier::factory()->create(['code' => 'SUPPLIER_1']);
    $supplier2 = Supplier::factory()->create(['code' => 'SUPPLIER_2']);

    $order = Order::factory()->create(['status' => OrderStatus::New]);
    $order->update(['number_portal' => '1465989-AA0001']);

    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::New,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-02',
        'status' => LineItemStatus::New,
    ]);

    // Create CSV content with multiple line items
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product 1,TEST-SKU-1,https://example.com/design1.png,SUPPLIER_1,SO-123456\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-02,Test Product 2,TEST-SKU-2,https://example.com/design2.png,SUPPLIER_2,SO-789012\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportLineItemsSuppliers::make()->handle($tempFile);

    // Verify first line item was updated
    $lineItem1->refresh();
    expect($lineItem1)
        ->supplier_id->toBe($supplier1->id)
        ->supplier_order_number->toBe('SO-123456')
        ->design_file_url->toBe('https://example.com/design1.png')
        ->status->toBe(LineItemStatus::InProduction);

    // Verify second line item was updated
    $lineItem2->refresh();
    expect($lineItem2)
        ->supplier_id->toBe($supplier2->id)
        ->supplier_order_number->toBe('SO-789012')
        ->design_file_url->toBe('https://example.com/design2.png')
        ->status->toBe(LineItemStatus::InProduction);

    // Verify order status was updated
    $order->refresh();
    expect($order->status)->toBe(OrderStatus::Processing);

    // Clean up
    unlink($tempFile);
});

test('does not change status if line item is not new', function () {
    // Create test data
    $supplier = Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);
    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::InProduction, // Already in production
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportLineItemsSuppliers::make()->handle($tempFile);

    // Verify line item was updated but status remained the same
    $lineItem->refresh();
    expect($lineItem)
        ->supplier_id->toBe($supplier->id)
        ->supplier_order_number->toBe('SO-123456')
        ->design_file_url->toBe('https://example.com/design.png')
        ->status->toBe(LineItemStatus::InProduction); // Status unchanged

    // Clean up
    unlink($tempFile);
});

test('does not change order status if order is not new', function () {
    // Create test data
    Supplier::factory()->create(['code' => 'TEST_SUPPLIER']);
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order->update(['number_portal' => '1465989-AA0001']);
    LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '1465989-AA0001-01',
        'status' => LineItemStatus::New,
    ]);

    // Create CSV content
    $csvContent = "order_tda_number,line_item_code,product_name,sku,link_design,supplier,supplier_order_number\n";
    $csvContent .= "1465989-AA0001,1465989-AA0001-01,Test Product,TEST-SKU,https://example.com/design.png,TEST_SUPPLIER,SO-123456\n";

    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_suppliers_import');
    file_put_contents($tempFile, $csvContent);

    // Import the data
    ImportLineItemsSuppliers::make()->handle($tempFile);

    // Verify order status remained the same
    $order->refresh();
    expect($order->status)->toBe(OrderStatus::Processing); // Status unchanged

    // Clean up
    unlink($tempFile);
});
