<?php

use App\Console\Commands\ListBackups;
use Illuminate\Support\Facades\Storage;

test('list backups command shows no backups message when empty', function () {
    Storage::fake('backup');

    $this->artisan(ListBackups::class)
        ->expectsOutput('No backup files found.')
        ->assertExitCode(0);
});

test('list backups command shows warning when backup directory does not exist', function () {
    Storage::fake('backup');

    // Remove the backup directory
    Storage::disk('backup')->deleteDirectory('.');

    $this->artisan(ListBackups::class)
        ->expectsOutput('Backup directory does not exist.')
        ->assertExitCode(0);
});

test('list backups command displays backup files in table format', function () {
    Storage::fake('backup');

    // Create some fake backup files
    Storage::disk('backup')->put('database_backup_mysql_2025-01-01_10-00-00.sql', 'fake backup content 1');
    Storage::disk('backup')->put('database_backup_mysql_2025-01-02_11-00-00.sql', 'fake backup content 2 with more data');
    Storage::disk('backup')->put('other_file.txt', 'not a backup file');

    $this->artisan(ListBackups::class)
        ->expectsOutputToContain('database_backup_mysql_2025-01-02_11-00-00.sql')
        ->expectsOutputToContain('database_backup_mysql_2025-01-01_10-00-00.sql')
        ->expectsOutputToContain('Total backups: 2')
        ->assertExitCode(0);
});

test('list backups command is registered', function () {
    expect(app()->make(ListBackups::class))->toBeInstanceOf(ListBackups::class);
});
