<?php

use App\Actions\ReplicateProductType;
use App\Models\ProductType;
use App\Models\ProductTypeOption;
use App\Models\ProductTypeOptionValue;

it('can replicate a product type with copy suffix', function () {
    $productType = ProductType::factory()->create(['name' => 'Original Product']);

    $replica = ReplicateProductType::run($productType);

    expect($replica)
        ->toBeInstanceOf(ProductType::class)
        ->and($replica->name)->toBe('Original Product (Copy)')
        ->and($replica->description)->toBe($productType->description)
        ->and($replica->is_active)->toBe($productType->is_active)
        ->and($replica->id)->not->toBe($productType->id);

    expect(ProductType::count())->toBe(2);
});

it('can replicate a product type with options and values', function () {
    $productType = ProductType::factory()->create(['name' => 'Complex Product']);

    // Create options with values
    $option1 = ProductTypeOption::create([
        'product_type_id' => $productType->id,
        'name' => 'Size',
        'is_required' => true,
        'sort_order' => 1,
    ]);

    $option2 = ProductTypeOption::create([
        'product_type_id' => $productType->id,
        'name' => 'Color',
        'is_required' => false,
        'sort_order' => 2,
    ]);

    // Create values for options
    ProductTypeOptionValue::create([
        'product_type_option_id' => $option1->id,
        'value' => 'Small',
        'sort_order' => 1,
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option1->id,
        'value' => 'Large',
        'sort_order' => 2,
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option2->id,
        'value' => 'Red',
        'sort_order' => 1,
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option2->id,
        'value' => 'Blue',
        'sort_order' => 2,
    ]);

    $replica = ReplicateProductType::run($productType);

    // Check replica was created
    expect($replica->name)->toBe('Complex Product (Copy)');
    expect(ProductType::count())->toBe(2);

    // Check options were replicated
    expect($replica->options)->toHaveCount(2);
    expect(ProductTypeOption::count())->toBe(4);

    $replicaOptions = $replica->options;
    $sizeOption = $replicaOptions->where('name', 'Size')->first();
    $colorOption = $replicaOptions->where('name', 'Color')->first();

    expect($sizeOption)
        ->not->toBeNull()
        ->and($sizeOption->is_required)->toBeTrue()
        ->and($sizeOption->sort_order)->toBe(1)
        ->and($sizeOption->id)->not->toBe($option1->id);

    expect($colorOption)
        ->not->toBeNull()
        ->and($colorOption->is_required)->toBeFalse()
        ->and($colorOption->sort_order)->toBe(2)
        ->and($colorOption->id)->not->toBe($option2->id);

    // Check values were replicated
    $replicaSizeOption = $replicaOptions->where('name', 'Size')->first();
    $replicaColorOption = $replicaOptions->where('name', 'Color')->first();

    expect($replicaSizeOption->values)->toHaveCount(2);
    expect($replicaColorOption->values)->toHaveCount(2);
    expect(ProductTypeOptionValue::count())->toBe(8);

    // Verify specific values
    $sizeValues = $replicaSizeOption->values->pluck('value')->toArray();
    expect($sizeValues)->toContain('Small', 'Large');

    $colorValues = $replicaColorOption->values->pluck('value')->toArray();
    expect($colorValues)->toContain('Red', 'Blue');
});

it('preserves sort order when replicating options and values', function () {
    $productType = ProductType::factory()->create();

    $option = ProductTypeOption::create([
        'product_type_id' => $productType->id,
        'name' => 'Priority',
        'sort_order' => 5,
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option->id,
        'value' => 'High',
        'sort_order' => 10,
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option->id,
        'value' => 'Low',
        'sort_order' => 20,
    ]);

    $replica = ReplicateProductType::run($productType);

    $replicaOption = $replica->options->first();
    expect($replicaOption->sort_order)->toBe(5);

    $replicaValues = $replicaOption->values->sortBy('sort_order');
    expect($replicaValues->first()->value)->toBe('High');
    expect($replicaValues->first()->sort_order)->toBe(10);
    expect($replicaValues->last()->value)->toBe('Low');
    expect($replicaValues->last()->sort_order)->toBe(20);
});

it('creates completely independent replicas', function () {
    $productType = ProductType::factory()->create(['name' => 'Independent Test']);

    $option = ProductTypeOption::create([
        'product_type_id' => $productType->id,
        'name' => 'Test Option',
    ]);

    ProductTypeOptionValue::create([
        'product_type_option_id' => $option->id,
        'value' => 'Test Value',
    ]);

    $replica = ReplicateProductType::run($productType);

    // Modify original
    $productType->update(['name' => 'Modified Original']);
    $option->update(['name' => 'Modified Option']);

    // Verify replica is unchanged
    expect($replica->fresh()->name)->toBe('Independent Test (Copy)');
    expect($replica->options->first()->name)->toBe('Test Option');

    // Verify they have different IDs
    expect($replica->id)->not->toBe($productType->id);
    expect($replica->options->first()->id)->not->toBe($option->id);
    expect($replica->options->first()->values->first()->id)
        ->not->toBe($option->values->first()->id);
});
