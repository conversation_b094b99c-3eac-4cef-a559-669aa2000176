<?php

use App\Actions\GenerateInternalOrderNumber;
use App\Actions\ProcessOrdersImportRequest;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\Platform;
use App\Models\OrdersImportRequest;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

test('we can not process orders import request if not from etsy', function () {
    $importRequest = OrdersImportRequest::factory()->create([
        'platform' => Platform::Amazon->value,
    ]);

    ProcessOrdersImportRequest::make()->handle($importRequest);
})->throws(InvalidArgumentException::class);

test('we can process orders import request if from etsy', function () {
    $seller = User::factory()->asSeller()->create();
    $data = json_decode(file_get_contents(__DIR__.'/../fixtures/esty-orders.json'), true);
    $importRequest = OrdersImportRequest::factory()->create([
        'platform' => Platform::Etsy->value,
        'raw_data' => $data,
        'user_id' => $seller->id,
    ]);
    GenerateInternalOrderNumber::shouldRun()->times(4);

    ProcessOrdersImportRequest::make()->handle($importRequest);

    // basic expectations
    expect($importRequest->processed_at)->not->toBeNull();
    expect($seller->importedOrders)->toHaveCount(4);

    // more details expectations of order
    $order = $seller->importedOrders->first();
    $order->load(['billingAddress', 'shippingAddress', 'couponLines', 'lineItems', 'customer']);

    $rawOrder = $data['orders_search']['orders'][0];
    $rawPayment = $rawOrder['payment'];
    $costBreakdown = $rawPayment['cost_breakdown'];

    expect($order)
        ->platform->toEqual(Platform::Etsy)
        ->number_portal->toBeNull()
        ->id_platform->toEqual($rawOrder['order_id'])
        ->shop_id_platform->toEqual($rawOrder['business_id'])
        ->order_number->toEqual($rawOrder['order_id'])
        ->order_date->toEqual(Carbon::parse($rawOrder['order_date']))
        ->status->toEqual(OrderStatus::Draft)
        ->is_gift->toEqual($rawOrder['is_gift'])
        ->is_gift_wrapped->toEqual($rawOrder['is_gift_wrapped'])
        ->gift_message->toEqual($rawOrder['gift_message'])
        ->currency_code->toEqual($costBreakdown['total_cost']['currency_code'])
        ->total->toEqual($costBreakdown['total_cost']['value'])
        ->subtotal->toEqual($costBreakdown['items_cost']['value'])
        ->shipping_total->toEqual($costBreakdown['shipping_cost']['value'])
        ->tax_total->toEqual($costBreakdown['tax_cost']['value'])
        ->discount_total->toEqual($costBreakdown['discount']['value'])
        ->cost_breakdown->toEqual($costBreakdown)
        ->date_paid->toEqual(Carbon::parse($rawPayment['payment_date']))
        ->payment_method->toEqual($rawPayment['payment_method'])
        ->expected_ship_date->toEqual(Carbon::parse($rawOrder['fulfillment']['expected_ship_date']))
        ->production_total->toEqual(0)
        ->customer_note->toEqual($rawOrder['notes']['note_from_buyer']);

    // order lines
    $orderLines = $order->lineItems;
    $rawTransactions = $rawOrder['transactions'];
    $rawProduct = $rawTransactions[0]['product'];
    expect($orderLines)->toHaveCount(1)
        ->and($orderLines->first())
        ->number_portal->toBeNull()
        ->id_platform->toEqual($rawTransactions[0]['transaction_id'])
        ->etsy_listing_id->toEqual($rawTransactions[0]['listing_id'])
        ->product_name->toEqual($rawProduct['title'])
        ->product_image_url->toEqual(str_replace('75x75', '1000x1000', $rawProduct['image_url_75x75']))
        ->sku->toEqual($rawProduct['product_identifier'])
        ->quantity->toEqual($rawTransactions[0]['quantity'])
        ->price->toEqual($rawTransactions[0]['cost']['value'])
        ->currency_code->toEqual($rawTransactions[0]['cost']['currency_code'])
        ->supplier_id->toBeNull()
        ->supplier_order_number->toBeNull()
        ->file_design_url->toBeNull()
        ->is_download->toEqual($rawTransactions[0]['is_download'])
        ->is_personalizable->toEqual($rawTransactions[0]['is_personalizable'])
        ->variations->toEqual(array_map(function ($v) {
            return [
                'label' => $v['property'],
                'value' => $v['value'],
                'order' => $v['order'],
            ];
        }, $rawTransactions[0]['variations']))
        ->subtotal->toEqual($rawTransactions[0]['cost']['value'] * $rawTransactions[0]['quantity'])
        ->shipment_id->toBeNull()
        ->status->toEqual(LineItemStatus::New); // wtf is this?

    // expect the coupon lines
    $rawCoupons = $rawPayment['sellermarketing_coupons'];

    expect($order->couponLines)->toHaveCount(1)
        ->and($order->couponLines->first())
        ->type->toEqual($rawCoupons[0]['type'])
        ->code->toEqual($rawCoupons[0]['code'])
        ->percentage->toEqual($rawCoupons[0]['percentage']);

    // expect the billing
    $billing = $order->billingAddress;
    $rawBuyer = Arr::first($data['orders_search']['buyers'], function ($i) use ($rawOrder) {
        return $i['buyer_id'] === $rawOrder['buyer_id'];
    });
    expect($billing)
        ->id_platform->toEqual($rawBuyer['buyer_id'])
        ->name->toEqual($rawBuyer['name'])
        ->email->toEqual($rawBuyer['email'])
        ->line_one->toBeNUll()
        ->line_two->toBeNull()
        ->city->toBeNull()
        ->state->toBeNull()
        ->zip->toBeNull()
        ->country->toBeNull()
        ->phone->toBeNull();

    // expect the customer
    $customer = $order->customer;
    expect($customer)
        ->platform->toEqual(Platform::Etsy)
        ->id_platform->toEqual($rawBuyer['buyer_id'])
        ->name->toEqual($rawBuyer['name'])
        ->email->toEqual($rawBuyer['email'])
        ->username->toEqual($rawBuyer['username'])
        ->avatar_url->toEqual($rawBuyer['avatar_url']);

    // expect the shipping
    $shipping = $order->shippingAddress;
    $toAddress = $rawOrder['fulfillment']['to_address'];
    expect($shipping)
        ->name->toEqual($toAddress['name'])
        ->line_one->toEqual($toAddress['first_line'])
        ->line_two->toEqual($toAddress['second_line'])
        ->city->toEqual($toAddress['city'])
        ->state->toEqual($toAddress['state'])
        ->zip->toEqual($toAddress['zip'])
        ->country->toEqual($toAddress['country'])
        ->phone->toEqual($toAddress['phone'])
        ->is_usps_verified->toEqual($toAddress['is_usps_verified']);
});

test('product type is parsed correctly from SKU during import', function () {
    $seller = User::factory()->asSeller()->create();
    $data = json_decode(file_get_contents(__DIR__.'/../fixtures/esty-orders.json'), true);

    // Modify the test data to include SKUs that match our product codes
    $data['orders_search']['orders'][0]['transactions'][0]['product']['product_identifier'] = 'SCC01-TEST-123';
    $data['orders_search']['orders'][1]['transactions'][0]['product']['product_identifier'] = 'RSO01-CUSTOM-456';
    $data['orders_search']['orders'][2]['transactions'][0]['product']['product_identifier'] = 'UNKNOWN-SKU-789';
    $data['orders_search']['orders'][3]['transactions'][0]['product']['product_identifier'] = 'RKC01-TDA-KEYCAP';

    $importRequest = OrdersImportRequest::factory()->create([
        'platform' => Platform::Etsy->value,
        'raw_data' => $data,
        'user_id' => $seller->id,
    ]);

    GenerateInternalOrderNumber::shouldRun()->times(4);

    ProcessOrdersImportRequest::make()->handle($importRequest);

    $orders = $seller->importedOrders()->with('lineItems')->get();

    // Check first order - should match SCC01 (Suncatcher)
    $firstLineItem = $orders[0]->lineItems->first();
    expect($firstLineItem->sku)->toBe('SCC01-TEST-123');
    expect($firstLineItem->product_type)->toBe('Suncatcher');

    // Check second order - should match RSO01 (Resin Ornament)
    $secondLineItem = $orders[1]->lineItems->first();
    expect($secondLineItem->sku)->toBe('RSO01-CUSTOM-456');
    expect($secondLineItem->product_type)->toBe('Resin Ornament');

    // Check third order - should not match any code (null)
    $thirdLineItem = $orders[2]->lineItems->first();
    expect($thirdLineItem->sku)->toBe('UNKNOWN-SKU-789');
    expect($thirdLineItem->product_type)->toBeNull();

    // Check fourth order - should match RKC01 (TDA - RESIN KEYCAP)
    $fourthLineItem = $orders[3]->lineItems->first();
    expect($fourthLineItem->sku)->toBe('RKC01-TDA-KEYCAP');
    expect($fourthLineItem->product_type)->toBe('TDA - RESIN KEYCAP');
});
