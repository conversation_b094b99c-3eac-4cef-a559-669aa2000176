<?php

use App\Actions\ConfirmDraftOrder;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;

test('seller can confirm his draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create();

    ConfirmDraftOrder::make()->handle($seller, $order);

    expect($order->refresh())
        ->status->toBe(OrderStatus::New)
        ->seller_confirmed_at->not->toBeNull();
});

test('backup seller can confirm a draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $backupSeller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create([
        'backup_seller_id' => $backupSeller->id,
    ]);

    ConfirmDraftOrder::make()->handle($backupSeller, $order);

    expect($order->refresh())
        ->status->toBe(OrderStatus::New)
        ->seller_confirmed_at->not->toBeNull();
});

test('seller cannot confirm a non-draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->create([
        'status' => OrderStatus::New,
    ]);

    ConfirmDraftOrder::make()->handle($seller, $order);
})->throws(AuthorizationException::class);

test('seller cannot confirm another seller\'s draft order', function () {
    $seller1 = User::factory()->asSeller()->create();
    $seller2 = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller1, 'seller')->draft()->create();

    ConfirmDraftOrder::make()->handle($seller2, $order);
})->throws(AuthorizationException::class);
