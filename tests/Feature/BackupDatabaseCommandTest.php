<?php

use App\Console\Commands\BackupDatabase;
use Illuminate\Support\Facades\Storage;

test('backup database command is registered', function () {
    // Test that the command is properly registered
    expect(app()->make(BackupDatabase::class))->toBeInstanceOf(BackupDatabase::class);
});

test('backup database command fails when mysqldump not available for sqlite', function () {
    Storage::fake('backup');

    $this->artisan(BackupDatabase::class)
        ->expectsOutput('Starting database backup for connection: sqlite')
        ->assertExitCode(1);
});
