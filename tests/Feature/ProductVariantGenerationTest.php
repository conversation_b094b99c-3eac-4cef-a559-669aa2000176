<?php

use App\Actions\GenerateProductVariants;
use App\Models\Idea;
use App\Models\ProductType;
use App\Models\ProductVariant;

it('generates product variants with new structure', function () {
    // Create test data
    $idea = Idea::factory()->create();
    $productType = ProductType::factory()->create(['name' => 'Test T-Shirt']);

    // Create options with values
    $sizeOption = $productType->options()->create([
        'name' => 'Size',
        'is_required' => true,
        'sort_order' => 1,
    ]);
    $sizeOption->values()->createMany([
        ['value' => 'S', 'sort_order' => 0],
        ['value' => 'M', 'sort_order' => 1],
    ]);

    $colorOption = $productType->options()->create([
        'name' => 'Color',
        'is_required' => true,
        'sort_order' => 2,
    ]);
    $colorOption->values()->createMany([
        ['value' => 'Red', 'sort_order' => 0],
        ['value' => 'Blue', 'sort_order' => 1],
    ]);

    // Test variant generation
    $optionsData = [
        ['name' => 'Size', 'values' => ['S', 'M']],
        ['name' => 'Color', 'values' => ['Red', 'Blue']],
    ];

    $variants = GenerateProductVariants::make()->handle($idea, $productType, $optionsData);

    // Should generate 4 variants (2 sizes × 2 colors)
    expect($variants)->toHaveCount(4);

    // Test that each variant has the correct option values
    foreach ($variants as $variant) {
        $variant->load('optionValues.option'); // Explicit load for test
        expect($variant->idea_id)->toBe($idea->id);
        expect($variant->product_type_id)->toBe($productType->id);
        expect($variant->optionValues)->toHaveCount(2); // Should have 2 option values (size + color)

        $attributesData = $variant->getAttributesData();
        expect($attributesData)->toHaveKey('Size');
        expect($attributesData)->toHaveKey('Color');
        expect(['S', 'M'])->toContain($attributesData['Size']);
        expect(['Red', 'Blue'])->toContain($attributesData['Color']);
    }
});

it('prevents duplicate variants', function () {
    // Create test data
    $idea = Idea::factory()->create();
    $productType = ProductType::factory()->create(['name' => 'Test Mug']);

    $sizeOption = $productType->options()->create([
        'name' => 'Size',
        'is_required' => true,
        'sort_order' => 1,
    ]);
    $sizeOption->values()->create(['value' => 'Small', 'sort_order' => 0]);

    $optionsData = [
        ['name' => 'Size', 'values' => ['Small']],
    ];

    // Generate variants first time
    $firstGeneration = GenerateProductVariants::make()->handle($idea, $productType, $optionsData);
    expect($firstGeneration)->toHaveCount(1);

    // Try to generate the same variants again
    $secondGeneration = GenerateProductVariants::make()->handle($idea, $productType, $optionsData);
    expect($secondGeneration)->toHaveCount(0); // Should not create duplicates

    // Verify total variants in database
    $totalVariants = ProductVariant::where('idea_id', $idea->id)
        ->where('product_type_id', $productType->id)
        ->count();
    expect($totalVariants)->toBe(1);
});

it('tests variant attributes methods', function () {
    // Create a variant with option values
    $idea = Idea::factory()->create();
    $productType = ProductType::factory()->create();

    $colorOption = $productType->options()->create(['name' => 'Color', 'sort_order' => 1]);
    $redValue = $colorOption->values()->create(['value' => 'Red', 'sort_order' => 0]);

    $sizeOption = $productType->options()->create(['name' => 'Size', 'sort_order' => 2]);
    $largeValue = $sizeOption->values()->create(['value' => 'Large', 'sort_order' => 0]);

    $variant = ProductVariant::create([
        'idea_id' => $idea->id,
        'product_type_id' => $productType->id,
    ]);

    $variant->optionValues()->attach([$redValue->id, $largeValue->id]);
    $variant->load('optionValues.option');

    // Test attributesLines method
    $lines = $variant->attributesLines();
    expect($lines)->toContain('Color: Red');
    expect($lines)->toContain('Size: Large');

    // Test getAttributesData method
    $attributesData = $variant->getAttributesData();
    expect($attributesData)->toBe(['Color' => 'Red', 'Size' => 'Large']);
});
