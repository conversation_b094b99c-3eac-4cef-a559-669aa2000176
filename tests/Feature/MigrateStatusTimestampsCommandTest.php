<?php

use App\Console\Commands\MigrateStatusTimestamps;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\LineItem;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Activitylog\Models\Activity;

uses(RefreshDatabase::class);

describe('MigrateStatusTimestamps Command', function () {
    beforeEach(function () {
        // Set up activity log connection to use the default database for testing
        config(['activitylog.database_connection' => 'sqlite']);
    });

    it('migrates order status timestamps from activity logs', function () {
        $order = Order::factory()->create(['status' => OrderStatus::New]);

        // Create an activity log entry for status change
        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Order::class,
            'subject_id' => $order->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'processing'],
                'old' => ['status' => 'new'],
            ],
            'created_at' => now()->subHour(),
        ]);

        // Ensure timestamp is not set initially
        expect($order->fresh()->processing_at)->toBeNull();

        // Run the migration command
        $this->artisan(MigrateStatusTimestamps::class, ['--force' => true])
            ->assertExitCode(0);

        // Check that timestamp was set
        $order->refresh();
        expect($order->processing_at)->not->toBeNull();
        expect($order->processing_at->diffInHours(now()))->toBeLessThan(2);
    });

    it('migrates line item status timestamps from activity logs', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::New]);

        // Create an activity log entry for status change
        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => LineItem::class,
            'subject_id' => $lineItem->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'in-production'],
                'old' => ['status' => 'new'],
            ],
            'created_at' => now()->subHour(),
        ]);

        // Ensure timestamp is not set initially
        expect($lineItem->fresh()->in_production_at)->toBeNull();

        // Run the migration command
        $this->artisan(MigrateStatusTimestamps::class, ['--force' => true])
            ->assertExitCode(0);

        // Check that timestamp was set
        $lineItem->refresh();
        expect($lineItem->in_production_at)->not->toBeNull();
        expect($lineItem->in_production_at->diffInHours(now()))->toBeLessThan(2);
    });

    it('skips records that already have timestamps set', function () {
        $order = Order::factory()->create([
            'status' => OrderStatus::Processing,
            'processing_at' => now()->subDay(),
        ]);

        $originalTimestamp = $order->processing_at;

        // Create an activity log entry
        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Order::class,
            'subject_id' => $order->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'processing'],
                'old' => ['status' => 'new'],
            ],
            'created_at' => now()->subHour(),
        ]);

        // Run the migration command
        $this->artisan(MigrateStatusTimestamps::class, ['--force' => true])
            ->assertExitCode(0);

        // Check that timestamp was not changed
        $order->refresh();
        expect($order->processing_at->equalTo($originalTimestamp))->toBeTrue();
    });

    it('handles dry run mode correctly', function () {
        $order = Order::factory()->create(['status' => OrderStatus::New]);

        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Order::class,
            'subject_id' => $order->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'processing'],
                'old' => ['status' => 'new'],
            ],
            'created_at' => now()->subHour(),
        ]);

        // Run in dry-run mode
        $this->artisan(MigrateStatusTimestamps::class, ['--dry-run' => true])
            ->assertExitCode(0);

        // Check that timestamp was NOT set
        expect($order->fresh()->processing_at)->toBeNull();
    });

    it('ignores excluded statuses for orders', function () {
        $order = Order::factory()->create(['status' => OrderStatus::Draft]);

        // Create activity logs for excluded statuses
        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Order::class,
            'subject_id' => $order->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'new'],
                'old' => ['status' => 'draft'],
            ],
            'created_at' => now()->subHour(),
        ]);

        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Order::class,
            'subject_id' => $order->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'draft'],
                'old' => ['status' => 'new'],
            ],
            'created_at' => now()->subMinutes(30),
        ]);

        // Run the migration command
        $this->artisan(MigrateStatusTimestamps::class, ['--force' => true])
            ->assertExitCode(0);

        // Check that no timestamps were set (excluded statuses)
        $order->refresh();
        expect($order->processing_at)->toBeNull();
        expect($order->partial_shipped_at)->toBeNull();
        expect($order->shipped_at)->toBeNull();
        expect($order->cancelled_at)->toBeNull();
    });

    it('ignores excluded statuses for line items', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::InProduction]);

        // Create activity log for excluded status
        Activity::create([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => LineItem::class,
            'subject_id' => $lineItem->id,
            'causer_type' => null,
            'causer_id' => null,
            'properties' => [
                'attributes' => ['status' => 'new'],
                'old' => ['status' => 'in-production'],
            ],
            'created_at' => now()->subHour(),
        ]);

        // Run the migration command
        $this->artisan(MigrateStatusTimestamps::class, ['--force' => true])
            ->assertExitCode(0);

        // Check that no timestamps were set (excluded status)
        $lineItem->refresh();
        expect($lineItem->in_production_at)->toBeNull();
        expect($lineItem->packing_at)->toBeNull();
        expect($lineItem->packed_at)->toBeNull();
        expect($lineItem->shipped_at)->toBeNull();
        expect($lineItem->cancelled_at)->toBeNull();
    });
});
