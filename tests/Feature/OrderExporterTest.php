<?php

use App\Filament\Backoffice\Exports\LineItemsExporter;
use Filament\Actions\Exports\ExportColumn;

test('getColumns returns all expected export columns with correct labels', function () {
    $columns = LineItemsExporter::getColumns();

    expect($columns)->toHaveCount(35);

    $expectedColumns = [
        ['name' => 'order.order_date', 'label' => 'Order date'],
        ['name' => 'order.number_portal', 'label' => 'Order TDA number'],
        ['name' => 'number_portal', 'label' => 'Line item code'],
        ['name' => 'order.order_number', 'label' => 'Order number'],
        ['name' => 'shop_name', 'label' => 'Shop name'],
        ['name' => 'order.shippingAddress.name', 'label' => 'Shipping name'],
        ['name' => 'order.shippingAddress.line_one', 'label' => 'Shipping address 1'],
        ['name' => 'order.shippingAddress.line_two', 'label' => 'Shipping address 2'],
        ['name' => 'order.shippingAddress.city', 'label' => 'Shipping city'],
        ['name' => 'order.shippingAddress.state', 'label' => 'Shipping state'],
        ['name' => 'order.shippingAddress.zip', 'label' => 'Shipping zip'],
        ['name' => 'order.shippingAddress.country', 'label' => 'Shipping country'],
        ['name' => 'tracking_number', 'label' => 'Tracking number'],
        ['name' => 'product_name', 'label' => 'Product name'],
        ['name' => 'sku', 'label' => 'SKU'],
        ['name' => 'product_url', 'label' => 'Product url'],
        ['name' => 'product_image_url', 'label' => 'Product image URL'],
        ['name' => 'custom_image', 'label' => 'Image'],
        ['name' => 'quantity', 'label' => 'Quantity'],
        ['name' => 'currency_code', 'label' => 'Currency code'],
        ['name' => 'price', 'label' => 'Price'],
        ['name' => 'notes', 'label' => 'Notes'],
        ['name' => 'tax_label', 'label' => 'Tax/VAT Label'],
        ['name' => 'tax_message', 'label' => 'Tax/VAT Message'],
        ['name' => 'gift_message', 'label' => 'Gift Message'],
        ['name' => 'seller_note', 'label' => 'Seller notes'],
        ['name' => 'attributes_string', 'label' => 'Product attributes'],
        ['name' => 'product_type', 'label' => 'Type of product'],
        ['name' => 'size', 'label' => 'Size'],
        ['name' => 'color', 'label' => 'Color'],
        ['name' => 'personalization', 'label' => 'Personalization'],
        ['name' => 'custom_status', 'label' => 'Status'],
        ['name' => 'suggest_supplier', 'label' => 'Suggested supplier'],
        ['name' => 'suggest_design_file_url', 'label' => 'Suggested design file URL'],
        ['name' => 'order.seller_confirmed_at', 'label' => 'Confirmed at'],
    ];

    foreach ($expectedColumns as $index => $expectedColumn) {
        expect($columns[$index])
            ->toBeInstanceOf(ExportColumn::class)
            ->and($columns[$index]->getName())->toBe($expectedColumn['name'])
            ->and($columns[$index]->getLabel())->toBe($expectedColumn['label']);
    }
});

test('all columns are ExportColumn instances', function () {
    $columns = LineItemsExporter::getColumns();

    foreach ($columns as $column) {
        expect($column)->toBeInstanceOf(ExportColumn::class);
    }
});

test('columns have unique names', function () {
    $columns = LineItemsExporter::getColumns();
    $columnNames = collect($columns)->map(fn ($column) => $column->getName())->toArray();

    expect($columnNames)->toHaveCount(count(array_unique($columnNames)));
});

test('columns have non-empty labels', function () {
    $columns = LineItemsExporter::getColumns();

    foreach ($columns as $column) {
        expect($column->getLabel())->not->toBeEmpty();
    }
});
