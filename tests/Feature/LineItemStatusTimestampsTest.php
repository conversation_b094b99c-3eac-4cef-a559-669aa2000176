<?php

use App\Enums\LineItemStatus;
use App\Models\LineItem;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('LineItem Status Timestamps', function () {
    it('sets in_production_at when line item status changes to in-production', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::New]);
        expect($lineItem->in_production_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::InProduction]);

        expect($lineItem->fresh()->in_production_at)->not->toBeNull();
        expect($lineItem->fresh()->in_production_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets packing_at when line item status changes to packing', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::InProduction]);
        expect($lineItem->packing_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Packing]);

        expect($lineItem->fresh()->packing_at)->not->toBeNull();
        expect($lineItem->fresh()->packing_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets packed_at when line item status changes to packed', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::Packing]);
        expect($lineItem->packed_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Packed]);

        expect($lineItem->fresh()->packed_at)->not->toBeNull();
        expect($lineItem->fresh()->packed_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets shipped_at when line item status changes to shipped', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::Packed]);
        expect($lineItem->shipped_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Shipped]);

        expect($lineItem->fresh()->shipped_at)->not->toBeNull();
        expect($lineItem->fresh()->shipped_at)->toBeInstanceOf(Carbon::class);
    });

    it('sets cancelled_at when line item status changes to cancelled', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::InProduction]);
        expect($lineItem->cancelled_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Cancelled]);

        expect($lineItem->fresh()->cancelled_at)->not->toBeNull();
        expect($lineItem->fresh()->cancelled_at)->toBeInstanceOf(Carbon::class);
    });

    it('does not set timestamp when line item status changes to new', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::InProduction]);

        $lineItem->update(['status' => LineItemStatus::New]);

        $lineItem = $lineItem->fresh();
        expect($lineItem->in_production_at)->toBeNull();
        expect($lineItem->packing_at)->toBeNull();
        expect($lineItem->packed_at)->toBeNull();
        expect($lineItem->shipped_at)->toBeNull();
        expect($lineItem->cancelled_at)->toBeNull();
    });

    it('does not update timestamp when status does not change', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::InProduction]);
        $lineItem->update(['status' => LineItemStatus::InProduction]);
        $originalTimestamp = $lineItem->fresh()->in_production_at;

        // Update other fields but not status
        $lineItem->update(['seller_note' => 'Updated note']);

        expect($lineItem->fresh()->in_production_at)->toEqual($originalTimestamp);
    });

    it('updates timestamp when status transitions back to a previous state', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::New]);

        $lineItem->update(['status' => LineItemStatus::InProduction]);
        $firstTimestamp = $lineItem->fresh()->in_production_at;

        // Change to different status and back
        $lineItem->update(['status' => LineItemStatus::Packing]);

        // Small delay to ensure timestamp difference
        // Simulate time passing to ensure timestamp difference
        Carbon::setTestNow($firstTimestamp->copy()->addSecond());

        $lineItem->update(['status' => LineItemStatus::InProduction]);
        $secondTimestamp = $lineItem->fresh()->in_production_at;

        expect($secondTimestamp)->not->toEqual($firstTimestamp);
        expect($secondTimestamp->isAfter($firstTimestamp))->toBeTrue();
    });

    it('can set multiple status timestamps for different transitions', function () {
        $lineItem = LineItem::factory()->create(['status' => LineItemStatus::New]);

        $lineItem->update(['status' => LineItemStatus::InProduction]);
        expect($lineItem->fresh()->in_production_at)->not->toBeNull();
        expect($lineItem->fresh()->packing_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Packing]);
        expect($lineItem->fresh()->in_production_at)->not->toBeNull();
        expect($lineItem->fresh()->packing_at)->not->toBeNull();
        expect($lineItem->fresh()->packed_at)->toBeNull();

        $lineItem->update(['status' => LineItemStatus::Packed]);
        expect($lineItem->fresh()->in_production_at)->not->toBeNull();
        expect($lineItem->fresh()->packing_at)->not->toBeNull();
        expect($lineItem->fresh()->packed_at)->not->toBeNull();
        expect($lineItem->fresh()->shipped_at)->toBeNull();
    });
});
