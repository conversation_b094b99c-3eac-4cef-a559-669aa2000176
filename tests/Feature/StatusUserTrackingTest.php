<?php

declare(strict_types=1);

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\User;

it('sets user tracking columns on order status change', function () {
    $user = User::factory()->create();
    $order = Order::factory()->create(['status' => OrderStatus::Draft]);

    $this->actingAs($user);
    $order->status = OrderStatus::Processing;
    $order->save();
    $order->refresh();

    expect($order->to_processing_by_id)->toBe($user->id);
    expect($order->processing_at)->not->toBeNull();

    $order->status = OrderStatus::Shipped;
    $order->save();
    $order->refresh();

    expect($order->to_shipped_by_id)->toBe($user->id);
    expect($order->shipped_at)->not->toBeNull();
});

it('sets user tracking columns on line item status change', function () {
    $user = User::factory()->create();
    $lineItem = LineItem::factory()->create(['status' => LineItemStatus::New]);

    $this->actingAs($user);
    $lineItem->status = LineItemStatus::InProduction;
    $lineItem->save();
    $lineItem->refresh();

    expect($lineItem->to_in_production_by_id)->toBe($user->id);
    expect($lineItem->in_production_at)->not->toBeNull();

    $lineItem->status = LineItemStatus::Packed;
    $lineItem->save();
    $lineItem->refresh();

    expect($lineItem->to_packed_by_id)->toBe($user->id);
    expect($lineItem->packed_at)->not->toBeNull();
});
