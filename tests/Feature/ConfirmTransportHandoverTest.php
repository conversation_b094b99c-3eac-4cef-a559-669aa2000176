<?php

use App\Actions\ConfirmTransportHandover;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\ShipmentStatus;
use App\Enums\TransportHandoverStatus;
use App\Models\Carrier;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Shipment;
use App\Models\ThirdPartyLogistic;
use App\Models\TransportHandover;

test('can confirm transport handover and update shipments and line items', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 1',
        'code' => 'TEST1',
    ]);
    $carrier = Carrier::factory()->create();

    // Create orders and line items
    $order1 = Order::factory()->create();
    $order2 = Order::factory()->create();

    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order1->id,
        'status' => LineItemStatus::Packed,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'status' => LineItemStatus::Packed,
    ]);

    // Create shipments with TDA packer
    $shipment1 = Shipment::factory()->create([
        'tracking_number' => 'TDA123456789',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
        'shipped_at' => null,
    ]);

    $shipment2 = Shipment::factory()->create([
        'tracking_number' => 'TDA987654321',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
        'shipped_at' => null,
    ]);

    // Associate line items with shipments
    $lineItem1->update(['shipment_id' => $shipment1->id]);
    $lineItem2->update(['shipment_id' => $shipment2->id]);

    // Create transport handover
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA123456789\nTDA987654321",
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify transport handover status was updated
    $transportHandover->refresh();
    expect($transportHandover)
        ->status->toBe(TransportHandoverStatus::Confirmed)
        ->confirmed_at->not->toBeNull();

    // Verify shipments were updated to PickedUp status and third party logistics
    $shipment1->refresh();
    $shipment2->refresh();

    expect($shipment1)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->shipped_at->not->toBeNull()
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);

    expect($shipment2)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->shipped_at->not->toBeNull()
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);

    // Verify line items were updated to Shipped status
    $lineItem1->refresh();
    $lineItem2->refresh();

    expect($lineItem1->status)->toBe(LineItemStatus::Shipped);
    expect($lineItem2->status)->toBe(LineItemStatus::Shipped);

    // Verify order statuses were updated
    $order1->refresh();
    $order2->refresh();

    // Since each order has only one line item that is now shipped,
    // the orders should be marked as shipped
    expect($order1->status)->toBe(OrderStatus::Shipped);
    expect($order2->status)->toBe(OrderStatus::Shipped);
});

test('confirm transport handover only affects TDA packer shipments', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 2',
        'code' => 'TEST2',
    ]);
    $carrier = Carrier::factory()->create();

    // Create shipments with different packers
    $tdaShipment = Shipment::factory()->create([
        'tracking_number' => 'TDA123456789',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
        'shipped_at' => null,
        'third_party_logistic_id' => null, // Explicitly set to null initially
    ]);

    $supShipment = Shipment::factory()->create([
        'tracking_number' => 'SUP123456789',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'SUP',
        'shipped_at' => null,
        'third_party_logistic_id' => null, // Explicitly set to null
    ]);

    // Create transport handover with both tracking numbers
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA123456789\nSUP123456789",
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify only TDA shipment was updated
    $tdaShipment->refresh();
    $supShipment->refresh();

    expect($tdaShipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);
    expect($supShipment)
        ->status->toBe(ShipmentStatus::Pending) // Should remain unchanged
        ->third_party_logistic_id->toBeNull(); // Should remain unchanged
});

test('confirm transport handover handles comma-separated tracking numbers', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 3',
        'code' => 'TEST3',
    ]);
    $carrier = Carrier::factory()->create();

    $shipment1 = Shipment::factory()->create([
        'tracking_number' => 'TDA111111111',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
    ]);

    $shipment2 = Shipment::factory()->create([
        'tracking_number' => 'TDA222222222',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
    ]);

    // Create transport handover with comma-separated tracking numbers
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => 'TDA111111111, TDA222222222',
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify both shipments were updated
    $shipment1->refresh();
    $shipment2->refresh();

    expect($shipment1)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);
    expect($shipment2)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);
});

test('confirm transport handover ignores non-existent tracking numbers', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 4',
        'code' => 'TEST4',
    ]);
    $carrier = Carrier::factory()->create();

    $existingShipment = Shipment::factory()->create([
        'tracking_number' => 'TDA123456789',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
    ]);

    // Create transport handover with mix of existing and non-existing tracking numbers
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA123456789\nNONEXISTENT123\nALSOFAKE456",
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify transport handover was still confirmed
    $transportHandover->refresh();
    expect($transportHandover->status)->toBe(TransportHandoverStatus::Confirmed);

    // Verify existing shipment was updated
    $existingShipment->refresh();
    expect($existingShipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);

    // Verify no additional shipments were created
    expect(Shipment::count())->toBe(1);
});

test('getParsedTrackingNumbers method correctly parses different formats', function () {
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 5',
        'code' => 'TEST5',
    ]);

    // Test newline-separated tracking numbers
    $handover1 = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA123456789\nTDA987654321\nTDA555666777",
    ]);

    expect($handover1->getParsedTrackingNumbers())->toBe([
        'TDA123456789',
        'TDA987654321',
        'TDA555666777',
    ]);

    // Test comma-separated tracking numbers
    $handover2 = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => 'TDA111111111, TDA222222222, TDA333333333',
    ]);

    expect($handover2->getParsedTrackingNumbers())->toBe([
        'TDA111111111',
        'TDA222222222',
        'TDA333333333',
    ]);

    // Test mixed format with duplicates and empty lines
    $handover3 = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA444444444\n\nTDA555555555,TDA444444444\n  \nTDA666666666",
    ]);

    expect($handover3->getParsedTrackingNumbers())->toBe([
        'TDA444444444',
        'TDA555555555',
        'TDA666666666',
    ]);
});

test('confirm transport handover updates order status correctly for partial shipments', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 6',
        'code' => 'TEST6',
    ]);
    $carrier = Carrier::factory()->create();

    // Create an order with multiple line items
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    // Create line items - one will be shipped, one will remain in production
    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::Packed,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction, // This one won't be shipped
    ]);

    // Create shipment for only the first line item
    $shipment = Shipment::factory()->create([
        'tracking_number' => 'TDA123456789',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
        'shipped_at' => null,
    ]);

    // Associate only the first line item with the shipment
    $lineItem1->update(['shipment_id' => $shipment->id]);

    // Create transport handover
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => 'TDA123456789',
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify shipment was updated
    $shipment->refresh();
    expect($shipment)
        ->status->toBe(ShipmentStatus::PickedUp)
        ->third_party_logistic_id->toBe($thirdPartyLogistic->id);

    // Verify line items
    $lineItem1->refresh();
    $lineItem2->refresh();

    expect($lineItem1->status)->toBe(LineItemStatus::Shipped);
    expect($lineItem2->status)->toBe(LineItemStatus::InProduction); // Unchanged

    // Verify order status is updated to PartialShipped
    $order->refresh();
    expect($order->status)->toBe(OrderStatus::PartialShipped);
});

test('confirm transport handover updates multiple orders correctly', function () {
    // Create test data
    $thirdPartyLogistic = ThirdPartyLogistic::factory()->create([
        'name' => 'Test 3PL Provider 7',
        'code' => 'TEST7',
    ]);
    $carrier = Carrier::factory()->create();

    // Create multiple orders
    $order1 = Order::factory()->create(['status' => OrderStatus::Processing]);
    $order2 = Order::factory()->create(['status' => OrderStatus::Processing]);

    // Create line items for each order
    $lineItem1 = LineItem::factory()->create([
        'order_id' => $order1->id,
        'status' => LineItemStatus::Packed,
    ]);

    $lineItem2 = LineItem::factory()->create([
        'order_id' => $order2->id,
        'status' => LineItemStatus::Packed,
    ]);

    // Create shipments
    $shipment1 = Shipment::factory()->create([
        'tracking_number' => 'TDA111111111',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
    ]);

    $shipment2 = Shipment::factory()->create([
        'tracking_number' => 'TDA222222222',
        'carrier_id' => $carrier->id,
        'status' => ShipmentStatus::Pending,
        'packer' => 'TDA',
    ]);

    // Associate line items with shipments
    $lineItem1->update(['shipment_id' => $shipment1->id]);
    $lineItem2->update(['shipment_id' => $shipment2->id]);

    // Create transport handover
    $transportHandover = TransportHandover::factory()->create([
        'third_party_logistic_id' => $thirdPartyLogistic->id,
        'tracking_numbers' => "TDA111111111\nTDA222222222",
        'status' => TransportHandoverStatus::Draft,
    ]);

    // Confirm the transport handover
    ConfirmTransportHandover::make()->handle($transportHandover);

    // Verify both orders are updated to Shipped status
    $order1->refresh();
    $order2->refresh();

    expect($order1->status)->toBe(OrderStatus::Shipped);
    expect($order2->status)->toBe(OrderStatus::Shipped);

    // Verify shipments have third party logistics set
    $shipment1->refresh();
    $shipment2->refresh();

    expect($shipment1->third_party_logistic_id)->toBe($thirdPartyLogistic->id);
    expect($shipment2->third_party_logistic_id)->toBe($thirdPartyLogistic->id);
});
