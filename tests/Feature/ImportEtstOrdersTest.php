<?php

use App\Actions\ProcessOrdersImportRequest;
use App\Enums\Platform;
use App\Models\User;

use function Pest\Laravel\be;

test('guest user can not import etsy orders', function () {
    $this->postJson('/api/etsy/import-orders')
        ->assertUnauthorized();
});

test('authenticated user can import etsy orders', function () {
    $user = User::factory()->create();
    be($user, 'sanctum');
    $etsyData = json_decode(file_get_contents(__DIR__.'/../fixtures/esty-orders.json'), true);
    ProcessOrdersImportRequest::shouldRun()->once();

    $response = $this->postJson('/api/etsy/import-orders', $etsyData);

    $response->assertOk()
        ->assertJsonStructure([
            'orders_import_request' => ['id'],
        ]);
    expect($user->ordersImportRequest()->firstOrFail())
        ->raw_data->toEqual($etsyData)
        ->platform->toBe(Platform::Etsy);
});
