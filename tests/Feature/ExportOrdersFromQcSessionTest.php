<?php

use App\Actions\ExportOrdersFromQcSession;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\QcLineItemStatus;
use App\Enums\QcOrderCheckStatus;
use App\Enums\QcSessionStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\QcLineItemCheck;
use App\Models\QcOrderCheck;
use App\Models\QcSession;
use App\Models\ShippingAddress;
use App\Models\User;

test('can export CSV from completed QC session', function () {
    // Create a user
    $user = User::factory()->create();

    // Create a completed QC session
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
        'started_at' => now()->subHour(),
        'completed_at' => now(),
    ]);

    // Create an order with line items and shipping address
    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '20240630E0001',
        'order_number' => 'ETSY123456',
    ]);

    $shippingAddress = ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'John Doe',
        'phone' => '+1234567890',
        'email' => '<EMAIL>',
        'line_one' => '123 Main St',
        'line_two' => 'Apt 4B',
        'city' => 'New York',
        'state' => 'NY',
        'zip' => '10001',
        'country' => 'US',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000101',
        'product_name' => 'Test Product',
        'sku' => 'TEST-SKU-001',
        'quantity' => 2,
        'price' => 2500, // $25.00 in cents
        'status' => LineItemStatus::InProduction,
    ]);

    // Create QC order check
    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
        'status' => QcOrderCheckStatus::Completed,
        'confirmed_at' => now(),
    ]);

    // Create QC line item check
    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
        'checked_at' => now(),
    ]);

    // Execute the export action
    $filePath = ExportOrdersFromQcSession::make()->handle($qcSession);

    // Assert file path is returned
    expect($filePath)->toBeString();
    expect($filePath)->toContain('qc_export_'.$qcSession->id);
    expect($filePath)->toContain('.csv');

    // Assert file exists in storage
    expect(\Illuminate\Support\Facades\Storage::disk('public')->exists($filePath))->toBeTrue();
});

test('export CSV contains correct headers', function () {
    // Create minimal test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create();
    $lineItem = LineItem::factory()->create(['order_id' => $order->id]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Get CSV data for testing
    $csvData = ExportOrdersFromQcSession::make()->handle($qcSession, true);
    $headers = $csvData[0]; // First row contains headers

    // Expected headers
    $expectedHeaders = [
        'Order TDA Number',
        'Order Number',
        'Line item code',
        'Shipping Name',
        'Shipping Phone',
        'Shipping Email',
        'Shipping Address 1',
        'Shipping Address 2',
        'Shipping City',
        'Shipping State',
        'Shipping Zip',
        'Shipping Country',
        'Notes (Platform)',
        'Tax/VAT Label',
        'Tax/VAT Message',
        'Gift Message',
        'Size',
        'Type of Product',
        'Quantity',
        'Weight (g)',
        'Height (cm)',
        'Length (cm)',
        'Width (cm)',
        'Item Name',
        'Item SKU',
        'Item HsCode',
        'Item Quantity',
        'Item Price',
        'Service (US Standard,US Premium,Outside US)',
        'Buy with Insurance',
        'IOSS number',
        'EORI number',
        'VAT number',
        'Chinese name',
        'Unit Weight',
    ];

    expect($headers)->toBe($expectedHeaders);
});

test('export CSV contains correct data', function () {
    // Create test data with specific values
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'number_portal' => '20240630E0001',
        'order_number' => 'ETSY123456',
    ]);

    $shippingAddress = ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'John Doe',
        'phone' => '+1234567890',
        'email' => '<EMAIL>',
        'line_one' => '123 Main St',
        'line_two' => 'Apt 4B',
        'city' => 'New York',
        'state' => 'NY',
        'zip' => '10001',
        'country' => 'US',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000101',
        'product_name' => 'Test Product',
        'sku' => 'TEST-SKU-001',
        'quantity' => 2,
        'price' => 2500, // $25.00 in cents
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Get CSV data for testing
    $csvData = ExportOrdersFromQcSession::make()->handle($qcSession, true);
    $dataRow = $csvData[1]; // Second row contains the actual data

    // Verify specific data fields
    expect($dataRow[0])->toBe($order->number_portal); // Order TDA Number
    expect($dataRow[1])->toBe($order->order_number); // Order Number
    expect($dataRow[2])->toBe($lineItem->number_portal); // Line item code
    expect($dataRow[3])->toBe('John Doe'); // Shipping Name
    expect($dataRow[4])->toBe('+1234567890'); // Shipping Phone
    expect($dataRow[5])->toBe('<EMAIL>'); // Shipping Email
    expect($dataRow[6])->toBe('123 Main St'); // Shipping Address 1
    expect($dataRow[7])->toBe('Apt 4B'); // Shipping Address 2
    expect($dataRow[8])->toBe('New York'); // Shipping City
    expect($dataRow[9])->toBe('NY'); // Shipping State
    expect($dataRow[10])->toBe('10001'); // Shipping Zip
    expect($dataRow[11])->toBe('US'); // Shipping Country
    expect($dataRow[12])->toBe($lineItem->compositeNotes()); // Notes (Platform) - blank as requested
    expect($dataRow[13])->toBe($order->getTaxLabel() ?? ''); // Tax/VAT Label
    expect($dataRow[14])->toBe($order->getTaxMessage() ?? ''); // Tax/VAT Message
    expect($dataRow[15])->toBe($order->gift_message ?? ''); // Gift Message
    expect($dataRow[16])->toBe($lineItem->size ?? ''); // Size
    expect($dataRow[17])->toBe($lineItem->product_type ?? ''); // Type of Product
    expect($dataRow[18])->toBe($lineItem->quantity); // Quantity
    expect($dataRow[19])->toBe(''); // Weight (g) - blank for manual filling
    expect($dataRow[20])->toBe(''); // Height (cm) - blank for manual filling
    expect($dataRow[21])->toBe(''); // Length (cm) - blank for manual filling
    expect($dataRow[22])->toBe(''); // Width (cm) - blank for manual filling
    expect($dataRow[23])->toBe(''); // Item Name
    expect($dataRow[24])->toBe(''); // Item SKU
    expect($dataRow[25])->toBe(''); // Item HsCode - blank for manual filling
    expect($dataRow[26])->toBe(''); // Item Quantity
    expect($dataRow[27])->toBe(''); // Item Price (converted from cents)
});

test('export creates file with correct content', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'number_portal' => '20240630E0001',
    ]);

    ShippingAddress::factory()->create([
        'order_id' => $order->id,
        'name' => 'John Doe',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'number_portal' => '20240630E000101',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Execute the export action
    $filePath = ExportOrdersFromQcSession::make()->handle($qcSession);

    // Read the file content
    $fileContent = \Illuminate\Support\Facades\Storage::disk('public')->get($filePath);

    // Verify file contains expected data
    expect($fileContent)->toContain('Order TDA Number');
    expect($fileContent)->toContain('20240630E0001');
    expect($fileContent)->toContain('20240630E000101');
    expect($fileContent)->toContain('John Doe');

    // Clean up
    \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath);
});

test('export generates CSV file correctly', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '20240630E0001',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction, // Start with InProduction status
        'number_portal' => '20240630E000101',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
        'status' => QcOrderCheckStatus::Completed,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Verify initial status
    expect($lineItem->fresh()->status)->toBe(LineItemStatus::InProduction);

    // Execute the export action
    $filePath = ExportOrdersFromQcSession::make()->handle($qcSession);

    // Verify file was created
    expect($filePath)->toBeString();
    expect(\Illuminate\Support\Facades\Storage::disk('public')->exists($filePath))->toBeTrue();

    // Verify line item status was NOT updated by export (this is now done by FinishQcSession)
    expect($lineItem->fresh()->status)->toBe(LineItemStatus::InProduction);

    // Clean up
    \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath);
});

test('multiple exports work correctly', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '20240630E0002',
    ]);

    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::Packing, // Already in Packing status
        'number_portal' => '20240630E000201',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
        'status' => QcOrderCheckStatus::Completed,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $lineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    // Verify initial status
    expect($lineItem->fresh()->status)->toBe(LineItemStatus::Packing);

    // Execute the export action multiple times
    $filePath1 = ExportOrdersFromQcSession::make()->handle($qcSession);
    $filePath2 = ExportOrdersFromQcSession::make()->handle($qcSession);

    // Verify files were created
    expect($filePath1)->toBeString();
    expect($filePath2)->toBeString();
    expect(\Illuminate\Support\Facades\Storage::disk('public')->exists($filePath1))->toBeTrue();
    expect(\Illuminate\Support\Facades\Storage::disk('public')->exists($filePath2))->toBeTrue();

    // Verify line item status remained unchanged (export doesn't change status anymore)
    expect($lineItem->fresh()->status)->toBe(LineItemStatus::Packing);

    // Clean up
    \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath1);
    \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath2);
});

test('export only includes ready line items in CSV, missing items are excluded', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '20240630E0003',
    ]);

    // Create two line items - one ready, one missing
    $readyLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction,
        'number_portal' => '20240630E000301',
    ]);

    $missingLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction,
        'number_portal' => '20240630E000302',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
        'status' => QcOrderCheckStatus::Completed,
    ]);

    // Create QC line item checks with different statuses
    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $readyLineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $missingLineItem->id,
        'status' => QcLineItemStatus::Missing,
    ]);

    // Verify initial statuses
    expect($readyLineItem->fresh()->status)->toBe(LineItemStatus::InProduction);
    expect($missingLineItem->fresh()->status)->toBe(LineItemStatus::InProduction);

    // Execute the export action
    $filePath = ExportOrdersFromQcSession::make()->handle($qcSession);

    // Verify file was created
    expect($filePath)->toBeString();
    expect(\Illuminate\Support\Facades\Storage::disk('public')->exists($filePath))->toBeTrue();

    // Verify line item statuses were NOT updated by export (this is now done by FinishQcSession)
    expect($readyLineItem->fresh()->status)->toBe(LineItemStatus::InProduction);
    expect($missingLineItem->fresh()->status)->toBe(LineItemStatus::InProduction);

    // Verify CSV content only includes ready line item
    $csvData = ExportOrdersFromQcSession::make()->handle($qcSession, true);
    expect(count($csvData))->toBe(2); // Header + 1 data row (only ready item)
    expect($csvData[1][2])->toBe($readyLineItem->number_portal); // Line item code

    // Clean up
    \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath);
});

test('export only includes ready line items, excludes missing items', function () {
    // Create test data
    $user = User::factory()->create();
    $qcSession = QcSession::factory()->create([
        'user_id' => $user->id,
        'status' => QcSessionStatus::Completed,
    ]);

    $order = Order::factory()->create([
        'status' => OrderStatus::Processing,
        'number_portal' => '20240630E0004',
    ]);

    // Create two line items - one ready, one missing
    $readyLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction,
        'number_portal' => '20240630E000401',
    ]);

    $missingLineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction,
        'number_portal' => '20240630E000402',
    ]);

    $orderCheck = QcOrderCheck::factory()->create([
        'qc_session_id' => $qcSession->id,
        'order_id' => $order->id,
        'status' => QcOrderCheckStatus::Completed,
    ]);

    // Create QC line item checks with different statuses
    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $readyLineItem->id,
        'status' => QcLineItemStatus::Ready,
    ]);

    QcLineItemCheck::factory()->create([
        'qc_order_check_id' => $orderCheck->id,
        'line_item_id' => $missingLineItem->id,
        'status' => QcLineItemStatus::Missing,
    ]);

    // Get CSV data for testing
    $csvData = ExportOrdersFromQcSession::make()->handle($qcSession, true);

    // Should have header row + 1 data row (only the ready item)
    expect(count($csvData))->toBe(2);

    // Verify only the ready line item is included
    $dataRow = $csvData[1];
    expect($dataRow[2])->toBe($readyLineItem->number_portal); // Line item code

    // Verify the missing line item is not included by checking all data rows
    $allLineItemCodes = collect($csvData)->skip(1)->pluck(1)->toArray();
    expect($allLineItemCodes)->not->toContain($missingLineItem->number_portal);
});
