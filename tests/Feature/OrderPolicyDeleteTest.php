<?php

use App\Enums\OrderStatus;
use App\Enums\UserRole;
use App\Models\Order;
use App\Models\User;
use App\Policies\OrderPolicy;

beforeEach(function () {
    $this->policy = new OrderPolicy;
});

describe('OrderPolicy delete method', function () {
    it('allows sellers to delete their own draft orders', function () {
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $seller->id,
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($seller, $order))->toBeTrue();
    });

    it('allows backup sellers to delete draft orders assigned to them', function () {
        $backupSeller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'backup_seller_id' => $backupSeller->id,
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($backupSeller, $order))->toBeTrue();
    });

    it('allows managers to delete any draft orders', function () {
        $manager = User::factory()->create(['role' => UserRole::Manager]);
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $seller->id,
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($manager, $order))->toBeTrue();
    });

    it('denies deletion of non-draft orders even for managers', function () {
        $manager = User::factory()->create(['role' => UserRole::Manager]);
        $order = Order::factory()->create([
            'status' => OrderStatus::Processing,
        ]);

        expect($this->policy->delete($manager, $order))->toBeFalse();
    });

    it('denies deletion of non-draft orders for sellers', function () {
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $seller->id,
            'status' => OrderStatus::Processing,
        ]);

        expect($this->policy->delete($seller, $order))->toBeFalse();
    });

    it('denies sellers from deleting draft orders that are not theirs', function () {
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $anotherSeller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $anotherSeller->id,
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($seller, $order))->toBeFalse();
    });

    it('denies operators from deleting draft orders', function () {
        $operator = User::factory()->create(['role' => UserRole::Operator]);
        $order = Order::factory()->create([
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($operator, $order))->toBeFalse();
    });

    it('allows sellers to delete orders where they are both seller and backup seller', function () {
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $seller->id,
            'backup_seller_id' => $seller->id,
            'status' => OrderStatus::Draft,
        ]);

        expect($this->policy->delete($seller, $order))->toBeTrue();
    });

    it('denies deletion when order is in cancelled status', function () {
        $seller = User::factory()->create(['role' => UserRole::Seller]);
        $order = Order::factory()->create([
            'seller_id' => $seller->id,
            'status' => OrderStatus::Cancelled,
        ]);

        expect($this->policy->delete($seller, $order))->toBeFalse();
    });

    it('denies deletion when order is in shipped status', function () {
        $manager = User::factory()->create(['role' => UserRole::Manager]);
        $order = Order::factory()->create([
            'status' => OrderStatus::Shipped,
        ]);

        expect($this->policy->delete($manager, $order))->toBeFalse();
    });
});
