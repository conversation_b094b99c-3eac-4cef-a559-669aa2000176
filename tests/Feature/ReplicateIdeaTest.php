<?php

use App\Actions\ReplicateIdea;
use App\Enums\DesignStatus;
use App\Models\Design;
use App\Models\Idea;
use App\Models\ProductType;
use App\Models\ProductTypeOption;
use App\Models\ProductTypeOptionValue;
use App\Models\Tag;
use App\Models\User;

beforeEach()->skip();

it('can replicate an idea with copy suffix', function () {
    $idea = Idea::factory()->create(['name' => 'Original Idea']);

    $replica = ReplicateIdea::run($idea);

    expect($replica)
        ->not->toBeNull()
        ->and($replica->name)->toBe('Original Idea (Copy)')
        ->and($replica->description)->toBe($idea->description)
        ->and($replica->id)->not->toBe($idea->id);

    expect(Idea::count())->toBe(2);
});

it('can replicate an idea with designs and product types', function () {
    $idea = Idea::factory()->create(['name' => 'Complex Idea']);
    $user = User::factory()->create();

    // Create product types with options
    $productType1 = ProductType::factory()->create(['name' => 'T-Shirt']);
    $option1 = ProductTypeOption::create([
        'product_type_id' => $productType1->id,
        'name' => 'Size',
        'is_required' => true,
        'sort_order' => 1,
    ]);
    ProductTypeOptionValue::create([
        'product_type_option_id' => $option1->id,
        'value' => 'Small',
        'sort_order' => 1,
    ]);

    $productType2 = ProductType::factory()->create(['name' => 'Mug']);
    $option2 = ProductTypeOption::create([
        'product_type_id' => $productType2->id,
        'name' => 'Color',
        'is_required' => false,
        'sort_order' => 1,
    ]);
    ProductTypeOptionValue::create([
        'product_type_option_id' => $option2->id,
        'value' => 'Blue',
        'sort_order' => 1,
    ]);

    // Create designs (product types attached to idea)
    $design1 = Design::create([
        'idea_id' => $idea->id,
        'product_type_id' => $productType1->id,
        'status' => DesignStatus::Processing,
        'designer_id' => $user->id,
        'source_file_urls' => 'https://example.com/design1.svg',
        'score' => 4.5,
    ]);

    $design2 = Design::create([
        'idea_id' => $idea->id,
        'product_type_id' => $productType2->id,
        'status' => DesignStatus::Approved,
        'designer_id' => $user->id,
        'source_file_urls' => 'https://example.com/design2.svg',
        'score' => 3.8,
    ]);

    $replica = ReplicateIdea::run($idea);

    // Check replica was created
    expect($replica->name)->toBe('Complex Idea (Copy)');
    expect(Idea::count())->toBe(2);

    // Check designs were replicated
    expect($replica->designs)->toHaveCount(2);
    expect(Design::count())->toBe(4);

    // Check product types were NOT replicated, only attached (same count)
    expect(ProductType::count())->toBe(2);

    // Check replicated designs have correct properties
    $replicaDesigns = $replica->designs;

    foreach ($replicaDesigns as $replicaDesign) {
        expect($replicaDesign->status)->toBe(DesignStatus::Todo);
        expect($replicaDesign->designer_id)->toBeNull();
        expect($replicaDesign->idea_id)->toBe($replica->id);
        expect($replicaDesign->product_type_id)->toBeIn([$productType1->id, $productType2->id]); // Should use existing product types
    }

    // Verify original designs are unchanged
    $originalDesigns = $idea->designs;
    expect($originalDesigns->first()->status)->toBe(DesignStatus::Processing);
    expect($originalDesigns->last()->status)->toBe(DesignStatus::Approved);
    expect($originalDesigns->first()->designer_id)->toBe($user->id);
    expect($originalDesigns->last()->designer_id)->toBe($user->id);
});

it('preserves design properties except status, user_id, and source_file_urls', function () {
    $idea = Idea::factory()->create(['name' => 'Test Idea']);
    $user = User::factory()->create();
    $productType = ProductType::factory()->create(['name' => 'Test Product']);

    $design = Design::create([
        'idea_id' => $idea->id,
        'product_type_id' => $productType->id,
        'status' => DesignStatus::Approved,
        'user_id' => $user->id,
        'source_file_urls' => 'https://example.com/special-design.svg',
        'score' => 4.9,
    ]);

    $replica = ReplicateIdea::run($idea);

    $replicaDesign = $replica->designs->first();

    expect($replicaDesign)
        ->and($replicaDesign->source_file_urls)->toBeNull()
        ->and($replicaDesign->score)->toBe(4.9)
        ->and($replicaDesign->status)->toBe(DesignStatus::Todo)
        ->and($replicaDesign->user_id)->toBeNull();
});

it('creates completely independent replicas', function () {
    $idea = Idea::factory()->create(['name' => 'Independence Test']);
    $productType = ProductType::factory()->create(['name' => 'Test Product']);

    $option = ProductTypeOption::create([
        'product_type_id' => $productType->id,
        'name' => 'Test Option',
        'is_required' => true,
        'sort_order' => 1,
    ]);

    $value = ProductTypeOptionValue::create([
        'product_type_option_id' => $option->id,
        'value' => 'Test Value',
        'sort_order' => 1,
    ]);

    $design = Design::create([
        'idea_id' => $idea->id,
        'product_type_id' => $productType->id,
        'status' => DesignStatus::Processing,
        'user_id' => User::factory()->create()->id,
    ]);

    $replica = ReplicateIdea::run($idea);

    // Verify ideas have different IDs
    expect($replica->id)->not->toBe($idea->id);

    // Verify designs have different IDs
    $replicaDesign = $replica->designs->first();
    expect($replicaDesign->id)->not->toBe($design->id);

    // Verify product types are the SAME (not replicated, just attached)
    expect($replicaDesign->product_type_id)->toBe($productType->id);

    // Verify that modifying replica doesn't affect original idea
    $replica->update(['name' => 'Modified Replica']);
    expect($idea->fresh()->name)->toBe('Independence Test');

    // Verify that the same product type is shared
    expect($replicaDesign->productType->id)->toBe($productType->id);
    expect($replicaDesign->productType->name)->toBe('Test Product');
});
it('handles ideas with no designs', function () {
    $idea = Idea::factory()->create(['name' => 'No Designs Idea']);

    $replica = ReplicateIdea::run($idea);

    expect($replica)
        ->not->toBeNull()
        ->and($replica->name)->toBe('No Designs Idea (Copy)')
        ->and($replica->id)->not->toBe($idea->id);

    expect($replica->designs)->toHaveCount(0);
    expect(Idea::count())->toBe(2);
    expect(Design::count())->toBe(0);
});

it('replicates tags associated with the idea', function () {
    $idea = Idea::factory()->create(['name' => 'Tagged Idea']);

    // Create some tags
    $tag1 = Tag::create(['name' => 'summer']);
    $tag2 = Tag::create(['name' => 'nature']);
    $tag3 = Tag::create(['name' => 'minimal']);

    // Attach tags to the idea
    $idea->tags()->attach([$tag1->id, $tag2->id, $tag3->id]);

    $replica = ReplicateIdea::run($idea);

    // Check that replica has the same tags
    expect($replica->tags)->toHaveCount(3);
    expect($replica->tags->pluck('name')->sort()->values()->toArray())
        ->toBe(['minimal', 'nature', 'summer']);

    // Verify tags are the same instances (not duplicated)
    expect($replica->tags->pluck('id')->sort()->values()->toArray())
        ->toBe([$tag1->id, $tag2->id, $tag3->id]);

    // Verify total tag count hasn't increased
    expect(Tag::count())->toBe(3);
});

it('handles ideas with no tags', function () {
    $idea = Idea::factory()->create(['name' => 'No Tags Idea']);

    $replica = ReplicateIdea::run($idea);

    expect($replica->tags)->toHaveCount(0);
    expect($replica->name)->toBe('No Tags Idea (Copy)');
});
