<?php

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Livewire\ActivityTimeline;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\User;
use Livewire\Livewire;
use Spatie\Activitylog\Models\Activity;

beforeEach(function () {
    $this->user = User::factory()->create(['role' => 'operator']);
    $this->be($this->user);

    // Enable activity logging for these tests
    activity()->enableLogging();
});

test('activity timeline component can be mounted with an order', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    Livewire::test(ActivityTimeline::class, ['record' => $order])
        ->assertStatus(200);
});

test('activity timeline shows order creation activity', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    // The order creation should have been logged automatically
    $activities = Activity::on(config('activitylog.database_connection'))
        ->where('subject_type', Order::class)
        ->where('subject_id', $order->id)
        ->get();

    expect($activities->count())->toBeGreaterThanOrEqual(1);
    expect($activities->where('description', 'created'))->toHaveCount(1);
});

test('activity timeline shows order update activity', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    // Update the order to trigger activity logging
    $order->update(['status' => OrderStatus::Shipped]);

    $activities = Activity::on(config('activitylog.database_connection'))
        ->where('subject_type', Order::class)
        ->where('subject_id', $order->id)
        ->orderBy('created_at', 'desc')
        ->get();

    expect($activities->count())->toBeGreaterThanOrEqual(2);

    $updateActivity = $activities->where('description', 'updated')->first();
    expect($updateActivity)->not->toBeNull();

    // Debug what's in the changes array
    $changes = $updateActivity->changes;
    expect($changes)->toHaveKey('attributes');

    if (isset($changes['attributes']['status'])) {
        expect($changes['attributes']['status'])->toBe(OrderStatus::Shipped->value);
        expect($changes['old']['status'])->toBe(OrderStatus::Processing->value);
    }
});

test('activity timeline shows only order activities not line items', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);
    $lineItem = LineItem::factory()->create([
        'order_id' => $order->id,
        'status' => LineItemStatus::InProduction,
    ]);

    // Update the line item to trigger activity logging
    $lineItem->update(['status' => LineItemStatus::Packing]);

    $component = Livewire::test(ActivityTimeline::class, ['record' => $order]);

    // Should include only order activities, not line item activities
    $activities = $component->get('activities');

    // Check that we have only order activities
    $orderActivities = $activities->where('subject_type', Order::class);
    $lineItemActivities = $activities->where('subject_type', LineItem::class);

    expect($orderActivities->count())->toBeGreaterThanOrEqual(1);
    expect($lineItemActivities->count())->toBe(0); // No line item activities should be included
    expect($activities->count())->toBe($orderActivities->count()); // Only order activities
});

test('activity timeline formats compact values correctly', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    $component = new ActivityTimeline;
    $component->mount($order);

    // Test compact formatting
    expect($component->formatValueCompact(null))->toBe('');
    expect($component->formatValueCompact(''))->toBe('');
    expect($component->formatValueCompact(true))->toBe('true');
    expect($component->formatValueCompact(false))->toBe('false');
    expect($component->formatValueCompact('short text'))->toBe('short text');
});

test('activity timeline shows human readable model names', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    $component = new ActivityTimeline;
    $component->mount($order);

    expect($component->getHumanReadableModelName(Order::class))->toBe('Order');
    expect($component->getHumanReadableModelName(LineItem::class))->toBe('Line Item');
});

test('activity timeline shows human readable actions', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    $component = new ActivityTimeline;
    $component->mount($order);

    expect($component->getHumanReadableAction('created'))->toBe('created');
    expect($component->getHumanReadableAction('updated'))->toBe('updated');
    expect($component->getHumanReadableAction('deleted'))->toBe('deleted');
});

test('activity timeline filters out updated_at field', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    // Update the order to trigger activity logging with updated_at
    $order->update(['status' => OrderStatus::Shipped]);

    $component = Livewire::test(ActivityTimeline::class, ['record' => $order]);

    // Get the activities and check that updated_at is not displayed
    $activities = $component->get('activities');
    $updateActivity = $activities->where('description', 'updated')->first();

    expect($updateActivity)->not->toBeNull();

    // The changes should include status but not updated_at
    $attributes = $updateActivity->changes['attributes'] ?? [];
    expect($attributes)->toHaveKey('status');

    // Even if updated_at is in the raw changes, it should be filtered out in the view
    // We can't easily test the view filtering here, but we can verify the data structure
    expect($updateActivity->changes)->toHaveKey('attributes');
});

test('activity timeline shows correct icons and colors', function () {
    $order = Order::factory()->create(['status' => OrderStatus::Processing]);

    $component = new ActivityTimeline;
    $component->mount($order);

    // Test icon methods
    expect($component->getEventIcon('created'))->toBe('heroicon-s-plus');
    expect($component->getEventIcon('updated'))->toBe('heroicon-s-pencil');
    expect($component->getEventIcon('deleted'))->toBe('heroicon-s-trash');

    // Test icon colors
    expect($component->getEventIconColor('created'))->toBe('text-green-600');
    expect($component->getEventIconColor('updated'))->toBe('text-blue-600');
    expect($component->getEventIconColor('deleted'))->toBe('text-red-600');
});
