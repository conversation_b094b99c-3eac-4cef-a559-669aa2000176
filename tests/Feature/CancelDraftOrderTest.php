<?php

use App\Actions\CancelDraftOrder;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;

test('seller can cancel his draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create();

    CancelDraftOrder::make()->handle($seller, $order);

    expect($order->refresh())
        ->status->toBe(OrderStatus::Cancelled);
});

test('backup seller can cancel a draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $backupSeller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create([
        'backup_seller_id' => $backupSeller->id,
    ]);

    CancelDraftOrder::make()->handle($backupSeller, $order);

    expect($order->refresh())
        ->status->toBe(OrderStatus::Cancelled);
});

test('seller cannot cancel a non-draft order', function () {
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->create([
        'status' => OrderStatus::New,
    ]);

    CancelDraftOrder::make()->handle($seller, $order);
})->throws(AuthorizationException::class);

test('seller cannot cancel another seller\'s draft order', function () {
    $seller1 = User::factory()->asSeller()->create();
    $seller2 = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller1, 'seller')->draft()->create();

    CancelDraftOrder::make()->handle($seller2, $order);
})->throws(AuthorizationException::class);

test('operator cannot cancel a draft order', function () {
    $operator = User::factory()->asOperator()->create();
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create();

    CancelDraftOrder::make()->handle($operator, $order);
})->throws(AuthorizationException::class);

test('manager can cancel a draft order', function () {
    $manager = User::factory()->asManager()->create();
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->draft()->create();

    CancelDraftOrder::make()->handle($manager, $order);

    expect($order->refresh())
        ->status->toBe(OrderStatus::Cancelled);
});

test('manager cannot cancel a non-draft order', function () {
    $manager = User::factory()->asManager()->create();
    $seller = User::factory()->asSeller()->create();
    $order = Order::factory()->for($seller, 'seller')->create([
        'status' => OrderStatus::New,
    ]);

    CancelDraftOrder::make()->handle($manager, $order);
})->throws(AuthorizationException::class);
