<?php

use App\Models\Idea;
use App\Models\ProductType;

test('can create idea', function () {
    $idea = Idea::create([
        'name' => 'Test Idea',
        'description' => 'A test idea',
    ]);

    expect($idea->name)->toBe('Test Idea');
    expect($idea->uid)->toStartWith('U');
    expect($idea->uid)->toHaveLength(9);
});

test('uid is auto-generated and unique', function () {
    $idea1 = Idea::create(['name' => 'Idea 1']);
    $idea2 = Idea::create(['name' => 'Idea 2']);

    expect($idea1->uid)->toStartWith('U');
    expect($idea2->uid)->toStartWith('U');
    expect($idea1->uid)->not->toBe($idea2->uid);
    expect($idea1->uid)->toBe('U00000001');
    expect($idea2->uid)->toBe('U00000002');
});

test('can attach product types to idea', function () {
    $idea = Idea::create([
        'name' => 'Test Idea',
    ]);

    $productType = ProductType::create([
        'name' => 'Test Product Type',
        'is_active' => true,
    ]);

    $idea->productTypes()->attach($productType);

    expect($idea->productTypes)->toHaveCount(1);
    expect($idea->productTypes->first()->name)->toBe('Test Product Type');
});

test('can create parent-child idea relationships', function () {
    $parentIdea = Idea::create([
        'name' => 'Parent Idea',
    ]);

    $childIdea = Idea::create([
        'name' => 'Child Idea',
        'parent_id' => $parentIdea->id,
    ]);

    expect($parentIdea->isParent())->toBeTrue();
    expect($childIdea->isChild())->toBeTrue();
    expect($parentIdea->children)->toHaveCount(1);
    expect($childIdea->parent->name)->toBe('Parent Idea');
});
