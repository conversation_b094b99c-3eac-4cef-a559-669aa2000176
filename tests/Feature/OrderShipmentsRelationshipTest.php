<?php

use App\Models\LineItem;
use App\Models\Order;
use App\Models\Shipment;

test('order can access shipments through line items', function () {
    // Create an order with line items
    $order = Order::factory()->create();
    $lineItem1 = LineItem::factory()->create(['order_id' => $order->id]);
    $lineItem2 = LineItem::factory()->create(['order_id' => $order->id]);
    $lineItem3 = LineItem::factory()->create(['order_id' => $order->id]);

    // Create shipments
    $shipment1 = Shipment::factory()->create();
    $shipment2 = Shipment::factory()->create();

    // Assign line items to shipments
    $lineItem1->update(['shipment_id' => $shipment1->id]);
    $lineItem2->update(['shipment_id' => $shipment1->id]); // Same shipment as lineItem1
    $lineItem3->update(['shipment_id' => $shipment2->id]); // Different shipment

    // Test the relationship
    $shipments = $order->shipments;

    expect($shipments)->toHaveCount(2)
        ->and($shipments->pluck('id')->toArray())->toContain($shipment1->id, $shipment2->id);
});

test('order returns empty collection when no line items have shipments', function () {
    // Create an order with line items but no shipments
    $order = Order::factory()->create();
    LineItem::factory()->count(3)->create(['order_id' => $order->id, 'shipment_id' => null]);

    // Test the relationship
    $shipments = $order->shipments;

    expect($shipments)->toHaveCount(0);
});

test('order returns distinct shipments when multiple line items share same shipment', function () {
    // Create an order with multiple line items sharing the same shipment
    $order = Order::factory()->create();
    $shipment = Shipment::factory()->create();

    // Create 3 line items all assigned to the same shipment
    LineItem::factory()->count(3)->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
    ]);

    // Test the relationship
    $shipments = $order->shipments;

    expect($shipments)->toHaveCount(1)
        ->and($shipments->first()->id)->toBe($shipment->id);
});

test('shipment can access orders through line items', function () {
    // Create orders
    $order1 = Order::factory()->create();
    $order2 = Order::factory()->create();

    // Create a shipment
    $shipment = Shipment::factory()->create();

    // Create line items linking orders to the shipment
    LineItem::factory()->create(['order_id' => $order1->id, 'shipment_id' => $shipment->id]);
    LineItem::factory()->create(['order_id' => $order1->id, 'shipment_id' => $shipment->id]); // Same order
    LineItem::factory()->create(['order_id' => $order2->id, 'shipment_id' => $shipment->id]); // Different order

    // Test the relationship
    $orders = $shipment->orders;

    expect($orders)->toHaveCount(2)
        ->and($orders->pluck('id')->toArray())->toContain($order1->id, $order2->id);
});

test('shipment returns distinct orders when multiple line items from same order', function () {
    // Create an order
    $order = Order::factory()->create();
    $shipment = Shipment::factory()->create();

    // Create multiple line items from the same order assigned to the shipment
    LineItem::factory()->count(3)->create([
        'order_id' => $order->id,
        'shipment_id' => $shipment->id,
    ]);

    // Test the relationship
    $orders = $shipment->orders;

    expect($orders)->toHaveCount(1)
        ->and($orders->first()->id)->toBe($order->id);
});
