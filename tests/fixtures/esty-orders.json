{"orders_search": {"type": "Orders_OrdersCollection", "total_count": 4, "total_search_hit_count": 4, "refund_window_period": 180, "order_ids": ["3704407994", "3712658771", "3712690035", "3712820793"], "shipment_ids": [], "buyers": [{"type": "Etsy_Order_Buyer_UserBuyer", "email": "<EMAIL>", "is_frozen": false, "is_banned": false, "buyer_id": 314128737, "name": "<PERSON>", "username": "6dndildv", "is_repeat_buyer": false, "avatar_url": "https://i.etsystatic.com/site-assets/images/avatars/default_avatar.png?width=75", "profile_url": "https://www.etsy.com/people/6dndildv", "convo_history": {"type": "Etsy_Order_Buyer_ConvoHistory", "convo_count": 0, "url": "/conversations/with/6dndildv"}}, {"type": "Etsy_Order_Buyer_UserBuyer", "email": "micha<PERSON><EMAIL>", "is_frozen": false, "is_banned": false, "buyer_id": 557259742, "name": "<PERSON>", "username": "u3h44bm5ztyro02z", "is_repeat_buyer": false, "avatar_url": "https://i.etsystatic.com/site-assets/images/avatars/default_avatar.png?width=75", "profile_url": "https://www.etsy.com/people/u3h44bm5ztyro02z", "convo_history": {"type": "Etsy_Order_Buyer_ConvoHistory", "convo_count": 0, "url": "/conversations/with/u3h44bm5ztyro02z"}}, {"type": "Etsy_Order_Buyer_UserBuyer", "email": "<EMAIL>", "is_frozen": false, "is_banned": false, "buyer_id": 1099062876, "name": "<PERSON>", "username": "s0rqmdj4g7nuo1zl", "is_repeat_buyer": false, "avatar_url": "https://i.etsystatic.com/site-assets/images/avatars/default_avatar.png?width=75", "profile_url": "https://www.etsy.com/people/s0rqmdj4g7nuo1zl", "convo_history": {"type": "Etsy_Order_Buyer_ConvoHistory", "convo_count": 0, "url": "/conversations/with/s0rqmdj4g7nuo1zl"}}, {"type": "Etsy_Order_Buyer_UserBuyer", "email": "<EMAIL>", "is_frozen": false, "is_banned": false, "buyer_id": 794400771, "name": "Sign in with Apple user", "username": "ekexjykd4nohq5n7", "is_repeat_buyer": false, "avatar_url": "https://i.etsystatic.com/site-assets/images/avatars/default_avatar.png?width=75", "profile_url": "https://www.etsy.com/people/ekexjykd4nohq5n7", "convo_history": {"type": "Etsy_Order_Buyer_ConvoHistory", "convo_count": 0, "url": "/conversations/with/ekexjykd4nohq5n7"}}], "order_errors": [], "order_groups": [{"type": "Common_Order_Group", "display_name": "Dispatch by 01 Jul, 2025", "position": 0, "uid": "d5999ddd1e8af934e5d991962cf4e4a5", "order_ids": [3704407994, 3712658771, 3712690035, 3712820793]}], "orders": [{"type": "EtsyRetail_Order", "order_id": 3704407994, "order_date": 1749551726, "business_id": 56065751, "channel": 1, "order_state_id": "1324011363366", "is_gift": false, "is_gift_wrapped": false, "gift_message": null, "gift_buyer_first_name": null, "has_gift_teaser": false, "order_url": "https://www.etsy.com/your/orders/3704407994?order_id=3704407994", "buyer_id": 314128737, "has_unread_message": false, "has_ads_attribution": false, "uses_pretty_pricing": false, "has_any_queued_label_purchases": false, "is_tracking_required": false, "is_no_sunday_usps_eligible": false, "is_download_only": false, "has_digital_fulfill_later": false, "is_order_price_high_purchase_protection_seller_education": false, "is_order_price_low_purchase_protection_seller_education": true, "receipt_type": 0, "is_canceled": false, "has_sdt_referral": false, "transaction_ids": ["4612992444"], "actions": [{"type": "Common_Order_Action", "name": "ChangeState", "action_type": "ChangeState", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkComplete", "action_type": "MarkComplete", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddPrivateNote", "action_type": "AddPrivateNote", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddTracking", "action_type": "Invalid", "invalid_reasons_map": {"ADD_TRACKING_ERROR_KEY_NOT_COMPLETE": "Can't add tracking to an order that is not complete"}}, {"type": "Common_Order_Action", "name": "MarkAsGift", "action_type": "MarkAsGift", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_PAID_ALREADY_PAID": "Order is already marked as paid", "MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "PrintPackingSlip", "action_type": "PrintPackingSlip", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintReceipt", "action_type": "PrintReceipt", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintExistingShippingLabels", "action_type": "Invalid", "invalid_reasons_map": {"PRINT_SHIPPING_LABEL_ERROR_KEY_CANNOT_PRINT_NO_ACTIVE_LABELS": "Order has no active labels to print"}}, {"type": "Common_Order_Action", "name": "Refund", "action_type": "Refund", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "ReportNonPayment", "action_type": "ReportNonPayment", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "TrackShipments", "action_type": "TrackShipments", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsGift", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_GIFT_NOT_GIFT": "Order is not marked as a gift"}}, {"type": "Common_Order_Action", "name": "UndoMarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "UpdateScheduledToShipDate", "action_type": "UpdateScheduledToShipDate", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ActivateListing", "action_type": "Transaction_ActivateListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_Cancel", "action_type": "Transaction_Cancel", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_CopyListing", "action_type": "Transaction_CopyListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RenewListing", "action_type": "Transaction_RenewListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ReportFeedback", "action_type": "Transaction_ReportFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RespondToFeedback", "action_type": "Transaction_RespondToFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PurchaseReturnLabel", "action_type": "Invalid", "invalid_reasons_map": {"return_label_order_not_onboarded": "Can't create return label for this order"}}, {"type": "Common_Order_Action", "name": "PurchaseShippingLabel", "action_type": "PurchaseShippingLabel", "invalid_reasons_map": []}], "payment": {"type": "Etsy_Order_Payment", "payment_date": 1749551735, "payment_method": "cc", "seller_payment_method": "cc", "is_fully_paid": true, "is_flagged_for_manual_review": false, "is_in_person_payment": false, "is_partially_refunded": false, "is_fully_refunded": false, "is_manual_payment_method": false, "is_etsy_payment": true, "is_reserved": false, "is_shop_location_in_india": false, "reserve_percentage": 0.3, "sellermarketing_coupons": [{"type": "Etsy_SellerMarketingCoupon_PercentDiscount", "coupon_id": 1382691621513, "code": "SUMMER55", "end_date": 1750114799, "percentage": 55}], "cost_breakdown": {"type": "Etsy_Order_Payment_CostBreakdown", "should_show_vat_euro_notice": false, "should_show_vat_uk_notice": false, "should_show_gst_sg_notice": false, "should_show_canada_tax_paid_notice": false, "should_show_canada_zero_tax_rated_notice": false, "should_show_canada_post_etsy_rating_tooltip": false, "should_show_insurance_message": false, "feature_flag_enabled_for_buyer_promise_insurance_message": false, "should_show_refund_reason": false, "should_show_norway_voec_notice": false, "should_show_loyalty_free_shipping_attribution": false, "should_show_buyer_fee_line": false, "should_show_swiss_vat_notice": false, "has_etsy_shipping_label": false, "package_ids": ["************"], "total_cost": {"type": "Common_Money", "value": 1386, "currency_code": "GBP", "formatted_value": "£13.86"}, "buyer_cost": {"type": "Common_Money", "value": 1386, "currency_code": "GBP", "formatted_value": "£13.86"}, "items_cost": {"type": "Common_Money", "value": 2080, "currency_code": "GBP", "formatted_value": "£20.80"}, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "shipping_discount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "loyalty_shipping_benefit": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "refund": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_total_cost": {"type": "Common_Money", "value": 1386, "currency_code": "GBP", "formatted_value": "£13.86"}, "discounted_items_cost": {"type": "Common_Money", "value": 936, "currency_code": "GBP", "formatted_value": "£9.36"}, "gift_wrap_cost": null, "total_euro_cost": {"type": "Common_Money", "value": 1717, "currency_code": "EUR", "formatted_value": "€17.17"}, "euro_consignment_cost": {"type": "Common_Money", "value": 1160, "currency_code": "EUR", "formatted_value": "€11.60"}, "tax_rule_consignment_cost": {"type": "Common_Money", "value": 0, "currency_code": "USD", "formatted_value": "US$0.00"}, "total_canadian_provincial_tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "CAD", "formatted_value": "CA$0.00"}, "buyer_tax_amount": null, "discount": {"type": "Common_Money", "value": 1144, "currency_code": "GBP", "formatted_value": "£11.44"}, "tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "buyer_fees_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "payment_method_data": {"type": "Etsy_Order_Payment_CreditCardData"}, "etsy_coupon": null, "coupon": null, "reserve_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "tax": {"type": "Etsy_Order_Tax", "order_tax_details_list": []}, "transactions": [{"type": "Etsy_Order_Transaction", "quantity": 1, "transaction_id": 4612992444, "listing_id": 4295256528, "is_in_person_quicksale_listing": false, "is_personalizable": true, "is_digital_fulfill_later": false, "is_download": true, "usd_price": 2943, "listing_tariff_code": null, "was_singapore_gst_collected": false, "cost": {"type": "Common_Money", "value": 2080, "currency_code": "GBP", "formatted_value": null}, "status": {"type": "Etsy_Order_Transaction_Status", "is_cancelled": false, "is_downloaded": false, "has_pending_cancellation": false}, "decorator": null, "product": {"type": "Common_Product", "product_id": "4612992444", "product_identifier": "SCC01-P101446-Z", "title": "<PERSON>a Leaf Stained Glass Suncatcher – Tropical Window Hanging Decor, Jungle Plant Lover Gift, Modern Boho Sun Catcher for Home", "image_url_75x75": "https://i.etsystatic.com/56065751/r/il/bf35ae/6858957817/il_75x75.6858957817_njj2.jpg", "is_digital": false, "is_sold_out": false, "product_refund_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "review": null, "variations": [{"type": "Etsy_Order_Transaction_Variation_Standard", "variation_id": "4612992444_1327024034795", "property_id": "513", "value_id": "1327024034795", "order": 1, "property": "Material &amp; Size:", "has_variation_price": false, "value": "ACRYLIC - 4 inches"}]}], "fulfillment": {"type": "Etsy_Order_Fulfillment", "can_mark_shipped": true, "was_shipped": false, "is_complete": false, "expected_ship_date": 1751410740, "actual_ship_date": null, "expected_or_actual_ship_date": 1751410740, "completed_date": 0, "days_from_due_date": 21, "has_shipping_upgrade": false, "is_fully_digital": false, "is_overdue": false, "is_international": false, "shipping_method": "Standard Delivery", "recommended_package_ids": ["************"], "is_fully_or_pending_cancellation": false, "has_import_tax": false, "import_tax_label": null, "to_address": {"type": "Common_Order_Fulfillment_Address", "name": "<PERSON>", "first_line": "20 Priestden Road", "second_line": null, "city": "St Andrews", "state": "Fife", "zip": "KY16 8DJ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "from_address": {"type": "Common_Order_Fulfillment_Address", "name": "LOVETIESFASHION LTD", "first_line": "71-75 Shelton Street, Covent Garden", "second_line": null, "city": "London", "state": null, "zip": "WC2H 9JQ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "shipments": [], "digital": {"type": "Etsy_Order_Fulfillment_Digital", "downloads_url": "https://www.etsy.com/your/orders/3704407994/downloads", "has_download": false, "is_download_only": false, "files": []}, "status": {"type": "Etsy_Order_Fulfillment_Status", "cancellation_status": {"type": "Etsy_Order_Fulfillment_CancellationStatus", "summary": null, "is_fully_cancelled": false, "is_partially_cancelled": false, "has_pending_cancellation": null}, "digital_status": null, "in_person_status": null, "physical_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus", "estimated_delivery_date": null, "shipping_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus_NotShipped", "expected_ship_date": 1751410740, "show_shipping_in_future_notification": false, "min_expected_ship_date": 1750201140, "ship_by_dates_eligibility": "not_eligible"}}}, "verified_address": null, "shipping_label_eligibility": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility", "is_eligible": true, "user": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_User", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, "receipt": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD", "carriers": [{"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 34, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 344, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}]}}}, "notes": {"type": "Etsy_Order_Notes", "note_from_buyer": "Thank you for your order! I hope you love it as much as I loved making it for you.", "private_order_notes": []}, "user_cases": []}, {"type": "EtsyRetail_Order", "order_id": 3712658771, "order_date": 1749555933, "business_id": 56065751, "channel": 1, "order_state_id": "1324011363366", "is_gift": false, "is_gift_wrapped": false, "gift_message": null, "gift_buyer_first_name": null, "has_gift_teaser": false, "order_url": "https://www.etsy.com/your/orders/3712658771?order_id=3712658771", "buyer_id": 557259742, "has_unread_message": false, "has_ads_attribution": false, "uses_pretty_pricing": false, "has_any_queued_label_purchases": false, "is_tracking_required": false, "is_no_sunday_usps_eligible": false, "is_download_only": false, "has_digital_fulfill_later": false, "is_order_price_high_purchase_protection_seller_education": false, "is_order_price_low_purchase_protection_seller_education": true, "receipt_type": 0, "is_canceled": false, "has_sdt_referral": false, "transaction_ids": ["4629405203"], "actions": [{"type": "Common_Order_Action", "name": "ChangeState", "action_type": "ChangeState", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkComplete", "action_type": "MarkComplete", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddPrivateNote", "action_type": "AddPrivateNote", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddTracking", "action_type": "Invalid", "invalid_reasons_map": {"ADD_TRACKING_ERROR_KEY_NOT_COMPLETE": "Can't add tracking to an order that is not complete"}}, {"type": "Common_Order_Action", "name": "MarkAsGift", "action_type": "MarkAsGift", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_PAID_ALREADY_PAID": "Order is already marked as paid", "MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "PrintPackingSlip", "action_type": "PrintPackingSlip", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintReceipt", "action_type": "PrintReceipt", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintExistingShippingLabels", "action_type": "Invalid", "invalid_reasons_map": {"PRINT_SHIPPING_LABEL_ERROR_KEY_CANNOT_PRINT_NO_ACTIVE_LABELS": "Order has no active labels to print"}}, {"type": "Common_Order_Action", "name": "Refund", "action_type": "Refund", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "ReportNonPayment", "action_type": "ReportNonPayment", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "TrackShipments", "action_type": "TrackShipments", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsGift", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_GIFT_NOT_GIFT": "Order is not marked as a gift"}}, {"type": "Common_Order_Action", "name": "UndoMarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "UpdateScheduledToShipDate", "action_type": "UpdateScheduledToShipDate", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ActivateListing", "action_type": "Transaction_ActivateListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_Cancel", "action_type": "Transaction_Cancel", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_CopyListing", "action_type": "Transaction_CopyListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RenewListing", "action_type": "Transaction_RenewListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ReportFeedback", "action_type": "Transaction_ReportFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RespondToFeedback", "action_type": "Transaction_RespondToFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PurchaseReturnLabel", "action_type": "Invalid", "invalid_reasons_map": {"return_label_order_not_onboarded": "Can't create return label for this order"}}, {"type": "Common_Order_Action", "name": "PurchaseShippingLabel", "action_type": "PurchaseShippingLabel", "invalid_reasons_map": []}], "payment": {"type": "Etsy_Order_Payment", "payment_date": 1749555940, "payment_method": "cc", "seller_payment_method": "cc", "is_fully_paid": true, "is_flagged_for_manual_review": false, "is_in_person_payment": false, "is_partially_refunded": false, "is_fully_refunded": false, "is_manual_payment_method": false, "is_etsy_payment": true, "is_reserved": false, "is_shop_location_in_india": false, "reserve_percentage": 0.3, "sellermarketing_coupons": [{"type": "Etsy_SellerMarketingCoupon_PercentDiscount", "coupon_id": 1382691621513, "code": "SUMMER55", "end_date": 1750114799, "percentage": 55}], "cost_breakdown": {"type": "Etsy_Order_Payment_CostBreakdown", "should_show_vat_euro_notice": false, "should_show_vat_uk_notice": false, "should_show_gst_sg_notice": false, "should_show_canada_tax_paid_notice": false, "should_show_canada_zero_tax_rated_notice": false, "should_show_canada_post_etsy_rating_tooltip": false, "should_show_insurance_message": false, "feature_flag_enabled_for_buyer_promise_insurance_message": false, "should_show_refund_reason": false, "should_show_norway_voec_notice": false, "should_show_loyalty_free_shipping_attribution": false, "should_show_buyer_fee_line": false, "should_show_swiss_vat_notice": false, "has_etsy_shipping_label": false, "package_ids": ["************"], "total_cost": {"type": "Common_Money", "value": 1845, "currency_code": "GBP", "formatted_value": "£18.45"}, "buyer_cost": {"type": "Common_Money", "value": 1845, "currency_code": "GBP", "formatted_value": "£18.45"}, "items_cost": {"type": "Common_Money", "value": 3100, "currency_code": "GBP", "formatted_value": "£31.00"}, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "shipping_discount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "loyalty_shipping_benefit": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "refund": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_total_cost": {"type": "Common_Money", "value": 1845, "currency_code": "GBP", "formatted_value": "£18.45"}, "discounted_items_cost": {"type": "Common_Money", "value": 1395, "currency_code": "GBP", "formatted_value": "£13.95"}, "gift_wrap_cost": null, "total_euro_cost": {"type": "Common_Money", "value": 2285, "currency_code": "EUR", "formatted_value": "€22.85"}, "euro_consignment_cost": {"type": "Common_Money", "value": 1728, "currency_code": "EUR", "formatted_value": "€17.28"}, "tax_rule_consignment_cost": {"type": "Common_Money", "value": 0, "currency_code": "USD", "formatted_value": "US$0.00"}, "total_canadian_provincial_tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "CAD", "formatted_value": "CA$0.00"}, "buyer_tax_amount": null, "discount": {"type": "Common_Money", "value": 1705, "currency_code": "GBP", "formatted_value": "£17.05"}, "tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "buyer_fees_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "payment_method_data": {"type": "Etsy_Order_Payment_CreditCardData"}, "etsy_coupon": null, "coupon": null, "reserve_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "tax": {"type": "Etsy_Order_Tax", "order_tax_details_list": []}, "transactions": [{"type": "Etsy_Order_Transaction", "quantity": 1, "transaction_id": 4629405203, "listing_id": 1885734646, "is_in_person_quicksale_listing": false, "is_personalizable": false, "is_digital_fulfill_later": false, "is_download": false, "usd_price": 4387, "listing_tariff_code": null, "was_singapore_gst_collected": false, "cost": {"type": "Common_Money", "value": 3100, "currency_code": "GBP", "formatted_value": null}, "status": {"type": "Etsy_Order_Transaction_Status", "is_cancelled": false, "is_downloaded": false, "has_pending_cancellation": false}, "decorator": null, "product": {"type": "Common_Product", "product_id": "4629405203", "product_identifier": "EVG-A166-Q", "title": "Horse Acrylic Suncatcher, Elegant Window Decor for Equestrian Enthusiasts, Equestrian Horse Light Catcher, Perfect Gift for Horse Lovers.", "image_url_75x75": "https://i.etsystatic.com/56065751/r/il/2cb0b2/6776525634/il_75x75.6776525634_mrhq.jpg", "is_digital": false, "is_sold_out": false, "product_refund_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "review": null, "variations": [{"type": "Etsy_Order_Transaction_Variation_Standard", "variation_id": "4629405203_59942002874", "property_id": "513", "value_id": "59942002874", "order": 1, "property": "Material &amp; Size:", "has_variation_price": false, "value": "6 inches"}]}], "fulfillment": {"type": "Etsy_Order_Fulfillment", "can_mark_shipped": true, "was_shipped": false, "is_complete": false, "expected_ship_date": 1751410740, "actual_ship_date": null, "expected_or_actual_ship_date": 1751410740, "completed_date": 0, "days_from_due_date": 21, "has_shipping_upgrade": false, "is_fully_digital": false, "is_overdue": false, "is_international": false, "shipping_method": "Standard Delivery", "recommended_package_ids": ["************"], "is_fully_or_pending_cancellation": false, "has_import_tax": false, "import_tax_label": null, "to_address": {"type": "Common_Order_Fulfillment_Address", "name": "<PERSON>", "first_line": "19 Low Town", "second_line": null, "city": "<PERSON>", "state": "Scotland", "zip": "FK8 3PX", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "from_address": {"type": "Common_Order_Fulfillment_Address", "name": "LOVETIESFASHION LTD", "first_line": "71-75 Shelton Street, Covent Garden", "second_line": null, "city": "London", "state": null, "zip": "WC2H 9JQ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "shipments": [], "digital": {"type": "Etsy_Order_Fulfillment_Digital", "downloads_url": "https://www.etsy.com/your/orders/3712658771/downloads", "has_download": false, "is_download_only": false, "files": []}, "status": {"type": "Etsy_Order_Fulfillment_Status", "cancellation_status": {"type": "Etsy_Order_Fulfillment_CancellationStatus", "summary": null, "is_fully_cancelled": false, "is_partially_cancelled": false, "has_pending_cancellation": null}, "digital_status": null, "in_person_status": null, "physical_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus", "estimated_delivery_date": null, "shipping_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus_NotShipped", "expected_ship_date": 1751410740, "show_shipping_in_future_notification": false, "min_expected_ship_date": 1750201140, "ship_by_dates_eligibility": "not_eligible"}}}, "verified_address": null, "shipping_label_eligibility": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility", "is_eligible": true, "user": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_User", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, "receipt": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD", "carriers": [{"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 34, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 344, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}]}}}, "notes": {"type": "Etsy_Order_Notes", "note_from_buyer": null, "private_order_notes": []}, "user_cases": []}, {"type": "EtsyRetail_Order", "order_id": 3712690035, "order_date": 1749559193, "business_id": 56065751, "channel": 1, "order_state_id": "1324011363366", "is_gift": true, "is_gift_wrapped": false, "gift_message": null, "gift_buyer_first_name": null, "has_gift_teaser": false, "order_url": "https://www.etsy.com/your/orders/3712690035?order_id=3712690035", "buyer_id": 1099062876, "has_unread_message": false, "has_ads_attribution": false, "uses_pretty_pricing": false, "has_any_queued_label_purchases": false, "is_tracking_required": false, "is_no_sunday_usps_eligible": false, "is_download_only": false, "has_digital_fulfill_later": false, "is_order_price_high_purchase_protection_seller_education": false, "is_order_price_low_purchase_protection_seller_education": true, "receipt_type": 0, "is_canceled": false, "has_sdt_referral": false, "transaction_ids": ["4613069566"], "actions": [{"type": "Common_Order_Action", "name": "ChangeState", "action_type": "ChangeState", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkComplete", "action_type": "MarkComplete", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddPrivateNote", "action_type": "AddPrivateNote", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddTracking", "action_type": "Invalid", "invalid_reasons_map": {"ADD_TRACKING_ERROR_KEY_NOT_COMPLETE": "Can't add tracking to an order that is not complete"}}, {"type": "Common_Order_Action", "name": "MarkAsGift", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_GIFT_ALREADY_GIFT": "Order is already marked as a gift"}}, {"type": "Common_Order_Action", "name": "MarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_PAID_ALREADY_PAID": "Order is already marked as paid", "MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "PrintPackingSlip", "action_type": "PrintPackingSlip", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintReceipt", "action_type": "PrintReceipt", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintExistingShippingLabels", "action_type": "Invalid", "invalid_reasons_map": {"PRINT_SHIPPING_LABEL_ERROR_KEY_CANNOT_PRINT_NO_ACTIVE_LABELS": "Order has no active labels to print"}}, {"type": "Common_Order_Action", "name": "Refund", "action_type": "Refund", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "ReportNonPayment", "action_type": "Invalid", "invalid_reasons_map": {"REPORT_NON_PAYMENT_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "TrackShipments", "action_type": "TrackShipments", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsGift", "action_type": "UndoMarkAsGift", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "UpdateScheduledToShipDate", "action_type": "UpdateScheduledToShipDate", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ActivateListing", "action_type": "Transaction_ActivateListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_Cancel", "action_type": "Transaction_Cancel", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_CopyListing", "action_type": "Transaction_CopyListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RenewListing", "action_type": "Transaction_RenewListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ReportFeedback", "action_type": "Transaction_ReportFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RespondToFeedback", "action_type": "Transaction_RespondToFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PurchaseReturnLabel", "action_type": "Invalid", "invalid_reasons_map": {"return_label_order_not_onboarded": "Can't create return label for this order"}}, {"type": "Common_Order_Action", "name": "PurchaseShippingLabel", "action_type": "PurchaseShippingLabel", "invalid_reasons_map": []}], "payment": {"type": "Etsy_Order_Payment", "payment_date": 1749559200, "payment_method": "dc_paypal", "seller_payment_method": "cc", "is_fully_paid": true, "is_flagged_for_manual_review": false, "is_in_person_payment": false, "is_partially_refunded": false, "is_fully_refunded": false, "is_manual_payment_method": false, "is_etsy_payment": true, "is_reserved": false, "is_shop_location_in_india": false, "reserve_percentage": 0.3, "sellermarketing_coupons": [{"type": "Etsy_SellerMarketingCoupon_PercentDiscount", "coupon_id": 1382691621513, "code": "SUMMER55", "end_date": 1750114799, "percentage": 55}], "cost_breakdown": {"type": "Etsy_Order_Payment_CostBreakdown", "should_show_vat_euro_notice": false, "should_show_vat_uk_notice": false, "should_show_gst_sg_notice": false, "should_show_canada_tax_paid_notice": false, "should_show_canada_zero_tax_rated_notice": false, "should_show_canada_post_etsy_rating_tooltip": false, "should_show_insurance_message": false, "feature_flag_enabled_for_buyer_promise_insurance_message": false, "should_show_refund_reason": false, "should_show_norway_voec_notice": false, "should_show_loyalty_free_shipping_attribution": false, "should_show_buyer_fee_line": false, "should_show_swiss_vat_notice": false, "has_etsy_shipping_label": false, "package_ids": ["************"], "total_cost": {"type": "Common_Money", "value": 2146, "currency_code": "GBP", "formatted_value": "£21.46"}, "buyer_cost": {"type": "Common_Money", "value": 2146, "currency_code": "GBP", "formatted_value": "£21.46"}, "items_cost": {"type": "Common_Money", "value": 3770, "currency_code": "GBP", "formatted_value": "£37.70"}, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "shipping_discount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "loyalty_shipping_benefit": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "refund": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_total_cost": {"type": "Common_Money", "value": 2146, "currency_code": "GBP", "formatted_value": "£21.46"}, "discounted_items_cost": {"type": "Common_Money", "value": 1696, "currency_code": "GBP", "formatted_value": "£16.96"}, "gift_wrap_cost": null, "total_euro_cost": {"type": "Common_Money", "value": 2658, "currency_code": "EUR", "formatted_value": "€26.58"}, "euro_consignment_cost": {"type": "Common_Money", "value": 2101, "currency_code": "EUR", "formatted_value": "€21.01"}, "tax_rule_consignment_cost": {"type": "Common_Money", "value": 0, "currency_code": "USD", "formatted_value": "US$0.00"}, "total_canadian_provincial_tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "CAD", "formatted_value": "CA$0.00"}, "buyer_tax_amount": null, "discount": {"type": "Common_Money", "value": 2074, "currency_code": "GBP", "formatted_value": "£20.74"}, "tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "buyer_fees_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "payment_method_data": null, "etsy_coupon": null, "coupon": null, "reserve_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "tax": {"type": "Etsy_Order_Tax", "order_tax_details_list": []}, "transactions": [{"type": "Etsy_Order_Transaction", "quantity": 1, "transaction_id": 4613069566, "listing_id": 4295256528, "is_in_person_quicksale_listing": false, "is_personalizable": true, "is_digital_fulfill_later": false, "is_download": true, "usd_price": 5335, "listing_tariff_code": null, "was_singapore_gst_collected": false, "cost": {"type": "Common_Money", "value": 3770, "currency_code": "GBP", "formatted_value": null}, "status": {"type": "Etsy_Order_Transaction_Status", "is_cancelled": false, "is_downloaded": false, "has_pending_cancellation": false}, "decorator": null, "product": {"type": "Common_Product", "product_id": "4613069566", "product_identifier": "SCC01-P101446-Z", "title": "<PERSON>a Leaf Stained Glass Suncatcher – Tropical Window Hanging Decor, Jungle Plant Lover Gift, Modern Boho Sun Catcher for Home", "image_url_75x75": "https://i.etsystatic.com/56065751/r/il/bf35ae/6858957817/il_75x75.6858957817_njj2.jpg", "is_digital": false, "is_sold_out": false, "product_refund_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "review": null, "variations": [{"type": "Etsy_Order_Transaction_Variation_Standard", "variation_id": "4613069566_1327066139794", "property_id": "513", "value_id": "1327066139794", "order": 1, "property": "Material &amp; Size:", "has_variation_price": false, "value": "ACRYLIC - 8 inches"}]}], "fulfillment": {"type": "Etsy_Order_Fulfillment", "can_mark_shipped": true, "was_shipped": false, "is_complete": false, "expected_ship_date": 1751410740, "actual_ship_date": null, "expected_or_actual_ship_date": 1751410740, "completed_date": 0, "days_from_due_date": 21, "has_shipping_upgrade": false, "is_fully_digital": false, "is_overdue": false, "is_international": false, "shipping_method": "Standard Delivery", "recommended_package_ids": ["************"], "is_fully_or_pending_cancellation": false, "has_import_tax": false, "import_tax_label": null, "to_address": {"type": "Common_Order_Fulfillment_Address", "name": "<PERSON>", "first_line": "University Of East Anglia Norwich Research Park", "second_line": "Earlham Road", "city": "Norwich", "state": null, "zip": "NR4 7TJ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "from_address": {"type": "Common_Order_Fulfillment_Address", "name": "LOVETIESFASHION LTD", "first_line": "71-75 Shelton Street, Covent Garden", "second_line": null, "city": "London", "state": null, "zip": "WC2H 9JQ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "shipments": [], "digital": {"type": "Etsy_Order_Fulfillment_Digital", "downloads_url": "https://www.etsy.com/your/orders/3712690035/downloads", "has_download": false, "is_download_only": false, "files": []}, "status": {"type": "Etsy_Order_Fulfillment_Status", "cancellation_status": {"type": "Etsy_Order_Fulfillment_CancellationStatus", "summary": null, "is_fully_cancelled": false, "is_partially_cancelled": false, "has_pending_cancellation": null}, "digital_status": null, "in_person_status": null, "physical_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus", "estimated_delivery_date": null, "shipping_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus_NotShipped", "expected_ship_date": 1751410740, "show_shipping_in_future_notification": false, "min_expected_ship_date": 1750201140, "ship_by_dates_eligibility": "not_eligible"}}}, "verified_address": null, "shipping_label_eligibility": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility", "is_eligible": true, "user": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_User", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, "receipt": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD", "carriers": [{"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 34, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 344, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}]}}}, "notes": {"type": "Etsy_Order_Notes", "note_from_buyer": null, "private_order_notes": []}, "user_cases": []}, {"type": "EtsyRetail_Order", "order_id": 3712820793, "order_date": 1749568717, "business_id": 56065751, "channel": 1, "order_state_id": "1324011363366", "is_gift": true, "is_gift_wrapped": false, "gift_message": null, "gift_buyer_first_name": null, "has_gift_teaser": false, "order_url": "https://www.etsy.com/your/orders/3712820793?order_id=3712820793", "buyer_id": 794400771, "has_unread_message": false, "has_ads_attribution": true, "uses_pretty_pricing": false, "has_any_queued_label_purchases": false, "is_tracking_required": false, "is_no_sunday_usps_eligible": false, "is_download_only": false, "has_digital_fulfill_later": false, "is_order_price_high_purchase_protection_seller_education": false, "is_order_price_low_purchase_protection_seller_education": true, "receipt_type": 0, "is_canceled": false, "has_sdt_referral": false, "transaction_ids": ["4629609827"], "actions": [{"type": "Common_Order_Action", "name": "ChangeState", "action_type": "ChangeState", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "MarkComplete", "action_type": "MarkComplete", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddPrivateNote", "action_type": "AddPrivateNote", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "AddTracking", "action_type": "Invalid", "invalid_reasons_map": {"ADD_TRACKING_ERROR_KEY_NOT_COMPLETE": "Can't add tracking to an order that is not complete"}}, {"type": "Common_Order_Action", "name": "MarkAsGift", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_GIFT_ALREADY_GIFT": "Order is already marked as a gift"}}, {"type": "Common_Order_Action", "name": "MarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"MARK_AS_PAID_ALREADY_PAID": "Order is already marked as paid", "MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "PrintPackingSlip", "action_type": "PrintPackingSlip", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintReceipt", "action_type": "PrintReceipt", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PrintExistingShippingLabels", "action_type": "Invalid", "invalid_reasons_map": {"PRINT_SHIPPING_LABEL_ERROR_KEY_CANNOT_PRINT_NO_ACTIVE_LABELS": "Order has no active labels to print"}}, {"type": "Common_Order_Action", "name": "Refund", "action_type": "Refund", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "ReportNonPayment", "action_type": "Invalid", "invalid_reasons_map": {"REPORT_NON_PAYMENT_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "TrackShipments", "action_type": "TrackShipments", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsGift", "action_type": "UndoMarkAsGift", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "UndoMarkAsPaid", "action_type": "Invalid", "invalid_reasons_map": {"UNDO_MARK_AS_PAID_INVALID_PAYMENT_METHOD": "Invalid payment method"}}, {"type": "Common_Order_Action", "name": "UpdateScheduledToShipDate", "action_type": "UpdateScheduledToShipDate", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ActivateListing", "action_type": "Transaction_ActivateListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_Cancel", "action_type": "Transaction_Cancel", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_CopyListing", "action_type": "Transaction_CopyListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RenewListing", "action_type": "Transaction_RenewListing", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_ReportFeedback", "action_type": "Transaction_ReportFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "Transaction_RespondToFeedback", "action_type": "Transaction_RespondToFeedback", "invalid_reasons_map": []}, {"type": "Common_Order_Action", "name": "PurchaseReturnLabel", "action_type": "Invalid", "invalid_reasons_map": {"return_label_order_not_onboarded": "Can't create return label for this order"}}, {"type": "Common_Order_Action", "name": "PurchaseShippingLabel", "action_type": "PurchaseShippingLabel", "invalid_reasons_map": []}], "payment": {"type": "Etsy_Order_Payment", "payment_date": 1749568723, "payment_method": "apple_pay", "seller_payment_method": "cc", "is_fully_paid": true, "is_flagged_for_manual_review": false, "is_in_person_payment": false, "is_partially_refunded": false, "is_fully_refunded": false, "is_manual_payment_method": false, "is_etsy_payment": true, "is_reserved": false, "is_shop_location_in_india": false, "reserve_percentage": 0.3, "sellermarketing_coupons": [{"type": "Etsy_SellerMarketingCoupon_PercentDiscount", "coupon_id": 1382691621513, "code": "SUMMER55", "end_date": 1750114799, "percentage": 55}], "cost_breakdown": {"type": "Etsy_Order_Payment_CostBreakdown", "should_show_vat_euro_notice": false, "should_show_vat_uk_notice": false, "should_show_gst_sg_notice": false, "should_show_canada_tax_paid_notice": false, "should_show_canada_zero_tax_rated_notice": false, "should_show_canada_post_etsy_rating_tooltip": false, "should_show_insurance_message": false, "feature_flag_enabled_for_buyer_promise_insurance_message": false, "should_show_refund_reason": false, "should_show_norway_voec_notice": false, "should_show_loyalty_free_shipping_attribution": false, "should_show_buyer_fee_line": false, "should_show_swiss_vat_notice": false, "has_etsy_shipping_label": false, "package_ids": ["************"], "total_cost": {"type": "Common_Money", "value": 1521, "currency_code": "GBP", "formatted_value": "£15.21"}, "buyer_cost": {"type": "Common_Money", "value": 1521, "currency_code": "GBP", "formatted_value": "£15.21"}, "items_cost": {"type": "Common_Money", "value": 2380, "currency_code": "GBP", "formatted_value": "£23.80"}, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "shipping_discount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "loyalty_shipping_benefit": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "refund": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "adjusted_total_cost": {"type": "Common_Money", "value": 1521, "currency_code": "GBP", "formatted_value": "£15.21"}, "discounted_items_cost": {"type": "Common_Money", "value": 1071, "currency_code": "GBP", "formatted_value": "£10.71"}, "gift_wrap_cost": null, "total_euro_cost": {"type": "Common_Money", "value": 1883, "currency_code": "EUR", "formatted_value": "€18.83"}, "euro_consignment_cost": {"type": "Common_Money", "value": 1326, "currency_code": "EUR", "formatted_value": "€13.26"}, "tax_rule_consignment_cost": {"type": "Common_Money", "value": 0, "currency_code": "USD", "formatted_value": "US$0.00"}, "total_canadian_provincial_tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "CAD", "formatted_value": "CA$0.00"}, "buyer_tax_amount": null, "discount": {"type": "Common_Money", "value": 1309, "currency_code": "GBP", "formatted_value": "£13.09"}, "tax_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "buyer_fees_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "payment_method_data": null, "etsy_coupon": null, "coupon": null, "reserve_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "tax": {"type": "Etsy_Order_Tax", "order_tax_details_list": []}, "transactions": [{"type": "Etsy_Order_Transaction", "quantity": 1, "transaction_id": 4629609827, "listing_id": 4296308321, "is_in_person_quicksale_listing": false, "is_personalizable": false, "is_digital_fulfill_later": false, "is_download": false, "usd_price": 3368, "listing_tariff_code": null, "was_singapore_gst_collected": false, "cost": {"type": "Common_Money", "value": 2380, "currency_code": "GBP", "formatted_value": null}, "status": {"type": "Etsy_Order_Transaction_Status", "is_cancelled": false, "is_downloaded": false, "has_pending_cancellation": false}, "decorator": null, "product": {"type": "Common_Product", "product_id": "4629609827", "product_identifier": "SCC01-P101639-N", "title": "Colorful Butterfly Stained Glass Suncatcher with Flowers – Bright Floral Window Hanging Art for Garden Lovers, Spring Decor", "image_url_75x75": "https://i.etsystatic.com/56065751/r/il/8355ec/6864182547/il_75x75.6864182547_l1rb.jpg", "is_digital": false, "is_sold_out": false, "product_refund_amount": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}}, "review": null, "variations": [{"type": "Etsy_Order_Transaction_Variation_Standard", "variation_id": "4629609827_1327024034989", "property_id": "513", "value_id": "1327024034989", "order": 1, "property": "Material &amp; Size:", "has_variation_price": false, "value": "ACRYLIC - 5 inches"}]}], "fulfillment": {"type": "Etsy_Order_Fulfillment", "can_mark_shipped": true, "was_shipped": false, "is_complete": false, "expected_ship_date": 1751410740, "actual_ship_date": null, "expected_or_actual_ship_date": 1751410740, "completed_date": 0, "days_from_due_date": 21, "has_shipping_upgrade": false, "is_fully_digital": false, "is_overdue": false, "is_international": false, "shipping_method": "Standard Delivery", "recommended_package_ids": ["************"], "is_fully_or_pending_cancellation": false, "has_import_tax": false, "import_tax_label": null, "to_address": {"type": "Common_Order_Fulfillment_Address", "name": "<PERSON><PERSON>", "first_line": "17 Mallard Close", "second_line": null, "city": "<PERSON><PERSON><PERSON>", "state": "Hampshire", "zip": "SO51 7DD", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "from_address": {"type": "Common_Order_Fulfillment_Address", "name": "LOVETIESFASHION LTD", "first_line": "71-75 Shelton Street, Covent Garden", "second_line": null, "city": "London", "state": null, "zip": "WC2H 9JQ", "country": "United Kingdom", "country_id": 105, "phone": null, "is_usps_verified": false, "verification_state": 0, "delivery_point": null, "carrier_route_code": null}, "shipments": [], "digital": {"type": "Etsy_Order_Fulfillment_Digital", "downloads_url": "https://www.etsy.com/your/orders/3712820793/downloads", "has_download": false, "is_download_only": false, "files": []}, "status": {"type": "Etsy_Order_Fulfillment_Status", "cancellation_status": {"type": "Etsy_Order_Fulfillment_CancellationStatus", "summary": null, "is_fully_cancelled": false, "is_partially_cancelled": false, "has_pending_cancellation": null}, "digital_status": null, "in_person_status": null, "physical_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus", "estimated_delivery_date": "18 Jun-07 Jul", "shipping_status": {"type": "Etsy_Order_Fulfillment_PhysicalStatus_NotShipped", "expected_ship_date": 1751410740, "show_shipping_in_future_notification": false, "min_expected_ship_date": 1750201140, "ship_by_dates_eligibility": "not_eligible"}}}, "verified_address": null, "shipping_label_eligibility": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility", "is_eligible": true, "user": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_User", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, "receipt": {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt", "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD", "carriers": [{"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 34, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}, {"type": "Common_Order_Fulfillment_ShippingLabelEligibility_Receipt_Carrier", "carrier_id": 344, "is_eligible": true, "state": "LABEL_ELIGIBILITY_GOOD"}]}}}, "notes": {"type": "Etsy_Order_Notes", "note_from_buyer": null, "private_order_notes": []}, "user_cases": []}], "order_states": [{"type": "Common_OrderState", "order_state_id": 1324011363366, "client_id": null, "position": 0, "name": "New", "state_type": "New", "order_count": 4, "actions": ["UpdatePosition"]}], "shipping_labels": [], "packages": [{"type": "Etsy_Order_Fulfillment_Package", "package_name": null, "package_dimensions": null, "package_id": "************", "is_manual_package": true, "is_calculated_package": false, "is_free_shipping": false, "shipping_method_name": "Standard Delivery", "carrier_id": 0, "mail_class": null, "package_type": null, "package_length": 0, "package_width": 0, "package_height": 0, "dimension_units": null, "weight": 0, "weight_units": null, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "handling_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "materials_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "total_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "package_items": [{"type": "Etsy_Order_Fulfillment_PackageItem", "package_item_id": "************_package_item_for_transaction_4629609827", "transaction_id": "4629609827", "quantity": 1}]}, {"type": "Etsy_Order_Fulfillment_Package", "package_name": null, "package_dimensions": null, "package_id": "************", "is_manual_package": true, "is_calculated_package": false, "is_free_shipping": false, "shipping_method_name": "Standard Delivery", "carrier_id": 0, "mail_class": null, "package_type": null, "package_length": 0, "package_width": 0, "package_height": 0, "dimension_units": null, "weight": 0, "weight_units": null, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "handling_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "materials_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "total_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "package_items": [{"type": "Etsy_Order_Fulfillment_PackageItem", "package_item_id": "************_package_item_for_transaction_4613069566", "transaction_id": "4613069566", "quantity": 1}]}, {"type": "Etsy_Order_Fulfillment_Package", "package_name": null, "package_dimensions": null, "package_id": "************", "is_manual_package": true, "is_calculated_package": false, "is_free_shipping": false, "shipping_method_name": "Standard Delivery", "carrier_id": 0, "mail_class": null, "package_type": null, "package_length": 0, "package_width": 0, "package_height": 0, "dimension_units": null, "weight": 0, "weight_units": null, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "handling_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "materials_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "total_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "package_items": [{"type": "Etsy_Order_Fulfillment_PackageItem", "package_item_id": "************_package_item_for_transaction_4629405203", "transaction_id": "4629405203", "quantity": 1}]}, {"type": "Etsy_Order_Fulfillment_Package", "package_name": null, "package_dimensions": null, "package_id": "************", "is_manual_package": true, "is_calculated_package": false, "is_free_shipping": false, "shipping_method_name": "Standard Delivery", "carrier_id": 0, "mail_class": null, "package_type": null, "package_length": 0, "package_width": 0, "package_height": 0, "dimension_units": null, "weight": 0, "weight_units": null, "shipping_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "handling_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "materials_cost": {"type": "Common_Money", "value": 0, "currency_code": "GBP", "formatted_value": "£0.00"}, "total_cost": {"type": "Common_Money", "value": 450, "currency_code": "GBP", "formatted_value": "£4.50"}, "package_items": [{"type": "Etsy_Order_Fulfillment_PackageItem", "package_item_id": "************_package_item_for_transaction_4612992444", "transaction_id": "4612992444", "quantity": 1}]}], "no_new_orders_banner": {"type": "Common_Order_Fulfillment_NoNewOrdersBanner", "should_show_accuracy_variant": true, "should_show_more_sales_variant": false, "action_url": "/your/shops/me/tools/shipping-profiles"}, "transactions": [], "shipping_label_eligibilities": []}}