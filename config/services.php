<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'tda_passport' => [
        'host' => env('TDA_PASSPORT_HOST', 'https://passport.tdagroup.online'),
        'client_id' => env('TDA_PASSPORT_CLIENT_ID', '9f57c149-9e95-46f3-b898-845abad8634e'),
        'client_secret' => env('TDA_PASSPORT_CLIENT_SECRET', 'XMdSsCAoeGnPUI6Ps6BkPWeR1OJEDGoroGKFgnEf'),
        'redirect' => 'auth/callback',
    ],
];
