<?php

use App\Models\OrdersImportRequest;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

require_once __DIR__.'/schedule.php';

if (file_exists(__DIR__.'/console-dev.php')) {
    require_once __DIR__.'/console-dev.php';
}

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('calculate-missing-hash', function () {
    OrdersImportRequest::query()
        ->whereNull('hash')
        ->eachById(function (OrdersImportRequest $importRequest) {
            $hash = md5(json_encode($importRequest->raw_data));
            $importRequest->update(['hash' => $hash]);
        });
});
