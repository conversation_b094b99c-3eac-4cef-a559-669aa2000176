<?php

use App\Enums\UserRole;
use App\Http\Controllers\TransportHandoverPrintController;
use App\Livewire\Login;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Laravel\Socialite\Facades\Socialite;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/transport-handover/{transportHandover}/print', [TransportHandoverPrintController::class, 'show'])
    ->name('transport-handover.print');

Route::get('/login', Login::class);

Route::prefix('auth')->group(function () {
    Route::get('/login', function () {
        return Socialite::driver('tda_passport')
            ->with([
                'redirect_uri' => route('auth.callback'),
            ])
            ->redirect();
    })->name('auth.login');

    Route::get('/callback', function () {
        $user = Socialite::driver('tda_passport')
            ->with([
                'redirect_uri' => route('auth.callback'),
            ])
            ->user();

        $staff = User::firstOrCreate([
            'email' => $user->getEmail(),
        ], [
            'role' => UserRole::Seller,
            'name' => $user->getName(),
            'password' => Str::random(7),
        ]);

        Auth::login($staff);

        if ($staff->isSeller()) {
            return redirect()->to('/seller');
        }

        if ($staff->isDesigner()) {
            return redirect()->to('/designer');
        }

        return redirect()->to('/backoffice');
    })->name('auth.callback');
});

Route::get('/admin', function (Request $request) {
    return response(
        <<<TEXT
What the fuck 🙃
Your ip: {$request->ip()}
TEXT
    )->header('Content-Type', 'text/plain');
});
