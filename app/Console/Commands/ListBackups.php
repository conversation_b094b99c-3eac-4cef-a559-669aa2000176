<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ListBackups extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:list-backups';

    /**
     * The console command description.
     */
    protected $description = 'List all database backups with size and date information';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $backupDisk = Storage::disk('backup');

        if (! $backupDisk->exists('.')) {
            $this->warn('Backup directory does not exist.');

            return Command::SUCCESS;
        }

        $files = $backupDisk->files();
        $backupFiles = collect($files)->filter(fn ($file) => str_ends_with($file, '.sql'));

        if ($backupFiles->isEmpty()) {
            $this->info('No backup files found.');

            return Command::SUCCESS;
        }

        $backups = $backupFiles->map(function ($file) use ($backupDisk) {
            return [
                'filename' => $file,
                'size' => $this->formatBytes($backupDisk->size($file)),
                'created_at' => $this->formatDate($backupDisk->lastModified($file)),
                'timestamp' => $backupDisk->lastModified($file),
            ];
        })->sortByDesc('timestamp')->values();

        $this->table(
            ['Filename', 'Size', 'Created At'],
            $backups->map(fn ($backup) => [
                $backup['filename'],
                $backup['size'],
                $backup['created_at'],
            ])
        );

        $this->newLine();
        $this->info("Total backups: {$backups->count()}");
        $totalSize = $backups->sum(fn ($backup) => $this->parseSize($backup['size']));
        $this->info("Total size: {$this->formatBytes($totalSize)}");

        return Command::SUCCESS;
    }

    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));

        return round($bytes / (1024 ** $factor), 2).' '.$units[$factor];
    }

    private function formatDate(int $timestamp): string
    {
        return date('Y-m-d H:i:s', $timestamp);
    }

    private function parseSize(string $formattedSize): int
    {
        preg_match('/^([\d.]+)\s*([A-Z]+)$/', $formattedSize, $matches);

        if (! $matches) {
            return 0;
        }

        $size = (float) $matches[1];
        $unit = $matches[2];

        $units = ['B' => 0, 'KB' => 1, 'MB' => 2, 'GB' => 3, 'TB' => 4];
        $factor = $units[$unit] ?? 0;

        return (int) ($size * (1024 ** $factor));
    }
}
