<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupBackups extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:cleanup-backups {--keep=10 : Number of recent backup files to keep}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old database backup files, keeping only the most recent ones';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $keepCount = (int) $this->option('keep');

        if ($keepCount < 1) {
            $this->error('The --keep option must be at least 1.');

            return Command::FAILURE;
        }

        $backupDisk = Storage::disk('backup');

        if (! $backupDisk->exists('.')) {
            $this->warn('Backup directory does not exist.');

            return Command::SUCCESS;
        }

        $files = $backupDisk->files();
        $backupFiles = collect($files)->filter(fn ($file) => str_ends_with($file, '.sql'));

        if ($backupFiles->isEmpty()) {
            $this->info('No backup files found.');

            return Command::SUCCESS;
        }

        $totalFiles = $backupFiles->count();
        $this->info("Found {$totalFiles} backup files.");

        if ($totalFiles <= $keepCount) {
            $this->info("All files are within the keep limit ({$keepCount}). Nothing to clean up.");

            return Command::SUCCESS;
        }

        $sortedFiles = $backupFiles->map(function ($file) use ($backupDisk) {
            return [
                'filename' => $file,
                'timestamp' => $backupDisk->lastModified($file),
                'size' => $backupDisk->size($file),
            ];
        })->sortByDesc('timestamp')->values();

        $filesToKeep = $sortedFiles->take($keepCount);
        $filesToDelete = $sortedFiles->skip($keepCount);

        if ($filesToDelete->isEmpty()) {
            $this->info('No files to delete.');

            return Command::SUCCESS;
        }

        $this->info("Keeping {$keepCount} most recent files:");
        foreach ($filesToKeep as $file) {
            $this->line("  ✓ {$file['filename']} ({$this->formatBytes($file['size'])})");
        }

        $this->newLine();
        $deleteCount = $filesToDelete->count();
        $totalDeleteSize = $filesToDelete->sum('size');

        $this->warn("Will delete {$deleteCount} old backup files ({$this->formatBytes($totalDeleteSize)}):");
        foreach ($filesToDelete as $file) {
            $this->line("  ✗ {$file['filename']} ({$this->formatBytes($file['size'])})");
        }

        if (! $this->input->isInteractive() || $this->confirm('Do you want to proceed with the cleanup?', true)) {
            $deletedCount = 0;
            $deletedSize = 0;

            foreach ($filesToDelete as $file) {
                try {
                    $backupDisk->delete($file['filename']);
                    $deletedCount++;
                    $deletedSize += $file['size'];
                    $this->line("Deleted: {$file['filename']}");
                } catch (\Exception $e) {
                    $this->error("Failed to delete {$file['filename']}: ".$e->getMessage());
                }
            }

            $this->newLine();
            $this->info('Cleanup completed successfully!');
            $this->info("Deleted {$deletedCount} files, freed {$this->formatBytes($deletedSize)} of space.");
        } else {
            $this->info('Cleanup cancelled.');
        }

        return Command::SUCCESS;
    }

    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));

        return round($bytes / (1024 ** $factor), 2).' '.$units[$factor];
    }
}
