<?php

namespace App\Console\Commands;

use App\Enums\LineItemStatus;
use App\Models\LineItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateShippedLineItemTimestamps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:shipped-line-item-timestamps
                            {--dry-run : Show what would be updated without actually updating}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate shipped_at timestamps for line items that were bulk updated from their shipment relations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        $this->info('🚢 Migrating shipped_at timestamps for line items from shipment relations...');
        $this->newLine();

        if ($isDryRun) {
            $this->warn('🧪 DRY RUN MODE - No data will be modified');
            $this->newLine();
        }

        // Find shipped line items that don't have shipped_at timestamp but have a shipment with shipped_at
        $lineItemsQuery = LineItem::query()
            ->with('shipment')
            ->where('status', LineItemStatus::Shipped)
            ->whereNull('shipped_at')
            ->whereHas('shipment', function ($query) {
                $query->whereNotNull('shipped_at');
            });

        $totalCount = $lineItemsQuery->count();

        if ($totalCount === 0) {
            $this->info('✅ No line items need timestamp migration from shipments.');

            return 0;
        }

        $this->info("📊 Found {$totalCount} shipped line items missing shipped_at timestamps");
        $this->newLine();

        if (! $isDryRun && ! $isForced) {
            if (! $this->confirm("Do you want to migrate timestamps for {$totalCount} line items?")) {
                $this->info('❌ Migration cancelled.');

                return 1;
            }
            $this->newLine();
        }

        $updatedCount = 0;
        $skippedCount = 0;

        // Process in chunks for memory efficiency
        $lineItemsQuery->chunk(100, function ($lineItems) use ($isDryRun, &$updatedCount, &$skippedCount) {
            foreach ($lineItems as $lineItem) {
                $shipment = $lineItem->shipment;

                if (! $shipment || ! $shipment->shipped_at) {
                    $skippedCount++;

                    continue;
                }

                if ($isDryRun) {
                    $this->line("  Would update LineItem #{$lineItem->id} - shipped_at = {$shipment->shipped_at}");
                } else {
                    // Use DB query to avoid triggering model events and trait
                    DB::table('line_items')
                        ->where('id', $lineItem->id)
                        ->update([
                            'shipped_at' => $shipment->shipped_at,
                        ]);
                    $updatedCount++;
                }
            }
        });

        if ($isDryRun) {
            $this->info("  ✅ Would update: {$totalCount} line items");
        } else {
            $this->info("  ✅ LineItems: {$updatedCount} updated, {$skippedCount} skipped");
        }

        $this->newLine();
        $this->info('✅ Shipped line item timestamp migration completed!');

        return 0;
    }
}
