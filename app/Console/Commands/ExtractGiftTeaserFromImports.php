<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\OrdersImportRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class ExtractGiftTeaserFromImports extends Command
{
    protected $signature = 'imports:gift-teaser 
                            {--request_id= : Update by orders_import_request_id} 
                            {--dry : Only view, do not update DB}';

    protected $description = 'Extract order_id and has_gift_teaser from orders_import_requests.raw_data and update orders.has_gift_teaser';

    public function handle(): int
    {
        $query = OrdersImportRequest::query();

        if ($id = $this->option('request_id')) {
            $query->whereKey($id);
        }

        $rows = [];
        $countRequests = 0;

        $this->info('Scanning orders_import_requests ...');
        $query->orderBy('id')->chunkById(100, function ($chunk) use (&$rows, &$countRequests) {
            foreach ($chunk as $importRequest) {
                $countRequests++;

                $raw = $importRequest->raw_data;
                if (! is_array($raw)) {
                    $decoded = json_decode($raw ?? '', true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $raw = $decoded;
                    } else {
                        $this->warn("ImportRequest #{$importRequest->id} raw_data is not valid JSON, skipped.");

                        continue;
                    }
                }

                $orders = Arr::get($raw, 'orders_search.orders', []);
                if (! is_array($orders)) {
                    $this->warn("ImportRequest #{$importRequest->id} has no valid orders array, skipped.");

                    continue;
                }

                foreach ($orders as $rawOrder) {
                    $orderId = Arr::get($rawOrder, 'order_id');
                    $hasGiftTeaser = Arr::get($rawOrder, 'has_gift_teaser');

                    $rows[] = [
                        'import_request_id' => $importRequest->id,
                        'order_id' => $orderId,
                        'has_gift_teaser' => is_bool($hasGiftTeaser)
                            ? ($hasGiftTeaser ? 1 : 0)
                            : (is_null($hasGiftTeaser) ? 0 : (int) $hasGiftTeaser),
                    ];
                }
            }
        });

        if (empty($rows)) {
            $this->info('No data found.');

            return self::SUCCESS;
        }

        $this->table(['import_request_id', 'order_id', 'has_gift_teaser'], $rows);
        $this->info("Total import requests scanned: {$countRequests}");
        $this->info('Total orders found in raw: '.count($rows));

        // dry-run
        if ($this->option('dry')) {
            $this->warn('Dry run: Skipping database updates.');

            return self::SUCCESS;
        }

        $updated = 0;
        $missing = 0;

        foreach ($rows as $r) {
            $orderId = $r['order_id'];

            if (empty($orderId)) {
                $missing++;

                continue;
            }

            $query = Order::query()->where('order_number', $orderId);

            $rowCount = (clone $query)->count();

            if ($rowCount === 0) {
                $missing++;
                $this->warn("Order not found for order_number={$orderId}");

                continue;
            }

            (clone $query)->update([
                'has_gift_teaser' => (int) $r['has_gift_teaser'],
            ]);

            $updated += $rowCount;
        }

        $this->info("Updated orders: {$updated}");
        if ($missing > 0) {
            $this->warn("Not found / skipped: {$missing}");
        }

        return self::SUCCESS;
    }
}
