<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\Supplier;
use Illuminate\Console\Command;

class MakeDummySuppliersShipmentFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:dummy-suppliers-shipment-file
                            {--count=10 : Number of line items to generate}
                            {--filename=dummy-suppliers-shipment.csv : Output filename}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a dummy suppliers shipment file for testing purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = (int) $this->option('count');
        $filename = $this->option('filename');
        $output = storage_path('app');
        $filePath = $output.'/'.$filename;

        $handle = fopen($filePath, 'w');

        if (! $handle) {
            $this->error("Could not create file: {$filePath}");

            return 1;
        }

        // Write CSV headers
        $headers = [
            'order_tda_number',
            'line_item_code',
            'product_name',
            'sku',
            'link_design',
            'supplier',
            'supplier_order_number',
        ];

        fputcsv($handle, $headers);

        // Get suppliers for random selection
        $suppliers = Supplier::all();

        if ($suppliers->isEmpty()) {
            $this->error('No suppliers found in database. Please run the supplier seeder first.');
            fclose($handle);

            return 1;
        }

        $lineItemsGenerated = 0;
        $this->info('Generating dummy suppliers shipment data...');

        // Get orders with line items
        Order::query()
            ->where('status', OrderStatus::New)
            ->with(['lineItems'])
            ->each(function (Order $order) use ($handle, $suppliers, &$lineItemsGenerated, $count) {
                if ($lineItemsGenerated >= $count) {
                    return false; // Stop iteration
                }

                foreach ($order->lineItems as $lineItem) {
                    if ($lineItemsGenerated >= $count) {
                        break;
                    }

                    // Generate dummy data for this line item
                    $row = [
                        'order_tda_number' => $order->number_portal,
                        'line_item_code' => $lineItem->number_portal,
                        'product_name' => $lineItem->product_name,
                        'sku' => $lineItem->sku,
                        'link_design' => 'https://example.com/designs/'.fake()->uuid().'.png',
                        'supplier' => $suppliers->random()->code,
                        'supplier_order_number' => 'SO-'.fake()->unique()->numerify('######'),
                    ];

                    fputcsv($handle, $row);
                    $lineItemsGenerated++;
                }
            });

        fclose($handle);

        if ($lineItemsGenerated === 0) {
            $this->warn('No line items found for orders with status "New". Make sure you have orders with line items in the database.');

            return 1;
        }

        $this->info("Successfully generated {$lineItemsGenerated} dummy supplier shipment records.");
        $this->info("File saved to: {$filePath}");
        $this->line('');
        $this->info('You can now test the import functionality with:');
        $this->line("php artisan tinker --execute=\"\\App\\Actions\\ImportLineItemsSuppliers::make()->validate('{$filePath}')\"");
        $this->line('');
        $this->info('Or use the Filament admin panel to import the file.');

        return 0;
    }
}
