<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class RefreshApp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:refresh';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh the app';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $password = $this->ask('password?');

        if (Hash::check($password, '$2y$12$Zq..NNanJeQVlMmink6o1Ok6fzCs35nE1j7ios2zI5j0.AswN78XC')) {
            $this->call('migrate:fresh', [
                '--seed' => true,
                '--force' => true,
            ]);
            $this->call('app:refresh-activity-log');
        } else {
            $this->warn('Wrong password');
        }
    }
}
