<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class MigrateOrderStatusUserIds extends Command
{
    protected $signature = 'migrate:order-status-user-ids {--dry-run : Show what would be updated without actually updating} {--force : Skip confirmation prompts}';

    protected $description = 'Migrate user IDs for order status changes from activity logs';

    private array $orderStatusUserMapping = [
        'processing' => 'to_processing_by_id',
        'partial-shipped' => 'to_partial_shipped_by_id',
        'shipped' => 'to_shipped_by_id',
        'cancelled' => 'to_cancelled_by_id',
    ];

    public function handle()
    {
        if (! $this->option('force') && ! $this->option('dry-run')) {
            if (! $this->confirm('This will update user IDs for order status changes. Do you want to continue?')) {
                $this->info('Migration cancelled.');

                return 0;
            }
        }

        $isDryRun = $this->option('dry-run');
        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting order status user ID migration...');
        $this->migrateOrderUserIds($isDryRun);
        $this->info('✅ Order status user ID migration completed!');

        return 0;
    }

    private function migrateOrderUserIds(bool $isDryRun): void
    {
        $this->info('📋 Processing Order status user IDs...');
        $connection = config('activitylog.database_connection', 'default');
        $activities = Activity::on($connection)
            ->where('subject_type', Order::class)
            ->where('description', 'updated')
            ->where(function ($query) {
                foreach (array_keys($this->orderStatusUserMapping) as $status) {
                    $query->orWhereJsonContains('properties->attributes->status', $status);
                }
            })
            ->orderBy('created_at')
            ->get();

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($activities as $activity) {
            $order = Order::find($activity->subject_id);
            if (! $order) {
                continue;
            }
            $newStatus = $activity->properties['attributes']['status'] ?? null;
            if (! $newStatus || ! isset($this->orderStatusUserMapping[$newStatus])) {
                continue;
            }
            $userColumn = $this->orderStatusUserMapping[$newStatus];
            $userId = $activity->causer_id ?? null;
            if (! $userId || $order->{$userColumn}) {
                $skippedCount++;

                continue;
            }
            if ($isDryRun) {
                $this->line("  Would update Order #{$order->id} - {$userColumn} = {$userId}");
                $updatedCount++;
            } else {
                DB::table('orders')
                    ->where('id', $order->id)
                    ->update([
                        $userColumn => $userId,
                    ]);
                $updatedCount++;
            }
        }
        $this->info("  ✅ Orders: {$updatedCount} updated, {$skippedCount} skipped");
    }
}
