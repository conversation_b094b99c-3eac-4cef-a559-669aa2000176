<?php

namespace App\Console\Commands;

use App\Enums\LineItemStatus;
use App\Models\LineItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class MigrateShippedLineItemUserIds extends Command
{
    protected $signature = 'migrate:shipped-line-item-user-ids
                            {--dry-run : Show what would be updated without actually updating}
                            {--force : Skip confirmation prompts}';

    protected $description = 'Migrate to_shipped_by_id for line items from shipment creation logs';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        $this->info('🚢 Migrating to_shipped_by_id for line items from shipment creation logs...');
        $this->newLine();

        if ($isDryRun) {
            $this->warn('🧪 DRY RUN MODE - No data will be modified');
            $this->newLine();
        }

        // Find shipped line items that don't have to_shipped_by_id but have a shipment
        $lineItemsQuery = LineItem::query()
            ->with('shipment')
            ->where('status', LineItemStatus::Shipped)
            ->whereNull('to_shipped_by_id')
            ->whereHas('shipment');

        $totalCount = $lineItemsQuery->count();

        if ($totalCount === 0) {
            $this->info('✅ No line items need user ID migration from shipments.');

            return 0;
        }

        $this->info("📊 Found {$totalCount} shipped line items missing to_shipped_by_id");
        $this->newLine();

        if (! $isDryRun && ! $isForced) {
            if (! $this->confirm("Do you want to migrate user IDs for {$totalCount} line items?")) {
                $this->info('❌ Migration cancelled.');

                return 1;
            }
            $this->newLine();
        }

        $updatedCount = 0;
        $skippedCount = 0;
        $connection = config('activitylog.database_connection', 'default');

        // Process in chunks for memory efficiency
        $lineItemsQuery->chunkById(100, function ($lineItems) use ($isDryRun, &$updatedCount, &$skippedCount, $connection) {
            foreach ($lineItems as $lineItem) {
                $shipment = $lineItem->shipment;

                if (! $shipment) {
                    $skippedCount++;

                    continue;
                }

                // Find the activity log entry for shipment creation
                $shipmentActivity = Activity::on($connection)
                    ->where('subject_type', 'App\Models\Shipment')
                    ->where('subject_id', $shipment->id)
                    ->where('description', 'created')
                    ->first();

                $userId = $shipmentActivity?->causer_id;

                if (! $userId) {
                    $skippedCount++;

                    continue;
                }

                if ($isDryRun) {
                    $this->line("  Would update LineItem #{$lineItem->id} - to_shipped_by_id = {$userId}");
                    $updatedCount++;
                } else {
                    // Use DB query to avoid triggering model events and trait
                    DB::table('line_items')
                        ->where('id', $lineItem->id)
                        ->update([
                            'to_shipped_by_id' => $userId,
                        ]);
                    $updatedCount++;
                }
            }
        });

        if ($isDryRun) {
            $this->info("  ✅ Would update: {$updatedCount} line items, {$skippedCount} skipped");
        } else {
            $this->info("  ✅ LineItems: {$updatedCount} updated, {$skippedCount} skipped");
        }

        $this->newLine();
        $this->info('✅ Shipped line item user ID migration completed!');

        return 0;
    }
}
