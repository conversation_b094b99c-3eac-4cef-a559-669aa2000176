<?php

namespace App\Console\Commands;

use App\Actions\GenerateInternalOrderNumber;
use App\Models\Order;
use Illuminate\Console\Command;

class FillMissingOrderNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:fill-missing-numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill missing order numbers in the orders table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Order::query()
            ->whereNull('number_portal')
            ->with('lineItems')
            ->each(function (Order $order) {
                $this->line("Processing order ID: {$order->id}");
                GenerateInternalOrderNumber::make()->handle($order);
            });
    }
}
