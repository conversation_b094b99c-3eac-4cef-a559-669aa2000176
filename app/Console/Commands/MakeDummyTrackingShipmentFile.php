<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Models\Carrier;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class MakeDummyTrackingShipmentFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:dummy-tracking-shipment-file
                            {--count=10 : Number of line items to generate}
                            {--filename=dummy-tracking-shipment.csv : Output filename}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a dummy tracking shipment file for testing purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = (int) $this->option('count');
        $filename = $this->option('filename');
        $output = storage_path('app');
        $filePath = $output.'/'.$filename;

        $handle = fopen($filePath, 'w');

        if (! $handle) {
            $this->error("Could not create file: {$filePath}");

            return 1;
        }

        // Write CSV headers based on the example provided
        $headers = [
            'Order TDA Number',
            'Line Item Code',
            'Tracking Code',
            'Carrier',
            'Weight (g)',
            'Height (cm)',
            'Length (cm)',
            'Width (cm)',
            'Shipping fee',
            'Production cost',
            'Packaging fee',
            'Packer',
        ];

        fputcsv($handle, $headers);

        // Get carriers for random selection
        $carriers = Carrier::all();

        if ($carriers->isEmpty()) {
            $this->error('No carriers found in database. Please run the carrier seeder first.');
            fclose($handle);

            return 1;
        }

        $lineItemsGenerated = 0;
        $trackingNumbers = []; // Store tracking numbers to reuse across orders
        $this->info('Generating dummy tracking shipment data...');

        // Get orders with line items (processing or new status)
        $orders = Order::query()
            ->whereIn('status', [OrderStatus::Processing, OrderStatus::PartialShipped])
            ->with(['lineItems'])
            ->get();

        foreach ($orders as $order) {
            if ($lineItemsGenerated >= $count) {
                break;
            }

            // Group line items by tracking number (simulate multiple items in one shipment)
            $lineItemGroups = $order->lineItems->chunk(rand(1, 3));

            foreach ($lineItemGroups as $group) {
                // Sometimes reuse existing tracking number (30% chance) or create new one
                if (! empty($trackingNumbers) && rand(1, 100) <= 30) {
                    // Reuse existing tracking number from another order
                    $trackingData = $trackingNumbers[array_rand($trackingNumbers)];
                    $trackingNumber = $trackingData['tracking'];
                    $carrier = $trackingData['carrier'];
                    $shippingFee = $trackingData['shipping_fee'];
                } else {
                    // Generate new tracking number
                    $carrier = $carriers->random();
                    $trackingNumber = $this->generateTrackingNumber($carrier);
                    $shippingFee = round(mt_rand(300, 1500) / 100, 2); // Random float between 3.00 and 15.00

                    // Store for potential reuse by other orders
                    $trackingNumbers[] = [
                        'tracking' => $trackingNumber,
                        'carrier' => $carrier,
                        'shipping_fee' => $shippingFee,
                    ];
                }

                foreach ($group as $lineItem) {
                    if ($lineItemsGenerated >= $count) {
                        break 2;
                    }

                    // Generate dummy data for this line item
                    $row = [
                        'Order TDA Number' => $order->number_portal,
                        'Line Item Code' => $lineItem->number_portal,
                        'Tracking Code' => $trackingNumber,
                        'Carrier' => $carrier->name,
                        'Weight (g)' => rand(1, 100) <= 80 ? rand(50, 500) : null, // 80% chance to have weight
                        'Height (cm)' => rand(1, 100) <= 70 ? rand(2, 20) : null, // 70% chance to have height
                        'Length (cm)' => rand(1, 100) <= 70 ? rand(10, 30) : null, // 70% chance to have length
                        'Width (cm)' => rand(1, 100) <= 70 ? rand(10, 30) : null, // 70% chance to have width
                        'Shipping fee' => number_format($shippingFee, 2, ',', ''),
                        'Production cost' => number_format(round(mt_rand(500, 5000) / 100, 2), 2, ',', ''), // Random between 5.00 and 50.00
                        'Packaging fee' => number_format(round(mt_rand(100, 500) / 100, 2), 2, ',', ''), // Random between 1.00 and 5.00
                        'Packer' => Arr::random(['TDA', 'SUP']),
                    ];

                    fputcsv($handle, $row);
                    $lineItemsGenerated++;
                }
            }
        }

        fclose($handle);

        if ($lineItemsGenerated === 0) {
            $this->warn('No line items found for orders with status "New" or "Processing". Make sure you have orders with line items in the database.');

            return 1;
        }

        $this->info("Successfully generated {$lineItemsGenerated} dummy tracking shipment records.");
        $this->info("File saved to: {$filePath}");
        $this->line('');
        $this->info('You can now test the import functionality with the generated file.');

        return 0;
    }

    /**
     * Generate a realistic tracking number based on carrier
     */
    private function generateTrackingNumber(Carrier $carrier): string
    {
        return match (strtoupper($carrier->name)) {
            'USPS' => '9214490324919300821'.str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT),
            'UPS' => '1Z'.strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6)).str_pad(rand(0, 99999999), 8, '0', STR_PAD_LEFT),
            'FEDEX' => str_pad(rand(1000000000000000, 9999999999999999), 16, '0', STR_PAD_LEFT),
            'DHL' => str_pad(rand(1000000000, 9999999999), 10, '0', STR_PAD_LEFT),
            'YUN' => 'YT'.str_pad(rand(1000000000000000, 9999999999999999), 16, '0', STR_PAD_LEFT),
            'CHINA POST' => 'C'.strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8)).'CN',
            'SF EXPRESS' => 'SF'.str_pad(rand(100000000000, 999999999999), 12, '0', STR_PAD_LEFT),
            default => 'TRK-'.strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8)).'-'.str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT),
        };
    }
}
