<?php

namespace App\Console\Commands;

use App\Actions\DownloadDesignMedia;
use App\Filament\Backoffice\Resources\Designs\DownloadMediaAction;
use Illuminate\Console\Command;

class CleanupMediaZipFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:cleanup-media-zip-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup old media zip files when user download';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DownloadDesignMedia::make()->cleanupOldDownloads();
    }
}
