<?php

namespace App\Console\Commands;

use App\Models\LineItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class MigrateLineItemStatusUserIds extends Command
{
    protected $signature = 'migrate:line-item-status-user-ids {--dry-run : Show what would be updated without actually updating} {--force : Skip confirmation prompts}';

    protected $description = 'Migrate user IDs for line item status changes from activity logs';

    private array $lineItemStatusUserMapping = [
        'in-production' => 'to_in_production_by_id',
        'packing' => 'to_packing_by_id',
        'packed' => 'to_packed_by_id',
        'shipped' => 'to_shipped_by_id',
        'cancelled' => 'to_cancelled_by_id',
    ];

    public function handle()
    {
        if (! $this->option('force') && ! $this->option('dry-run')) {
            if (! $this->confirm('This will update user IDs for line item status changes. Do you want to continue?')) {
                $this->info('Migration cancelled.');

                return 0;
            }
        }

        $isDryRun = $this->option('dry-run');
        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting line item status user ID migration...');
        $this->migrateLineItemUserIds($isDryRun);
        $this->info('✅ Line item status user ID migration completed!');

        return 0;
    }

    private function migrateLineItemUserIds(bool $isDryRun): void
    {
        $this->info('📦 Processing LineItem status user IDs...');
        $connection = config('activitylog.database_connection', 'default');
        $activities = Activity::on($connection)
            ->where('subject_type', LineItem::class)
            ->where('description', 'updated')
            ->where(function ($query) {
                foreach (array_keys($this->lineItemStatusUserMapping) as $status) {
                    $query->orWhereJsonContains('properties->attributes->status', $status);
                }
            })
            ->orderBy('created_at')
            ->get();

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($activities as $activity) {
            $lineItem = LineItem::find($activity->subject_id);
            if (! $lineItem) {
                continue;
            }
            $newStatus = $activity->properties['attributes']['status'] ?? null;
            if (! $newStatus || ! isset($this->lineItemStatusUserMapping[$newStatus])) {
                continue;
            }
            $userColumn = $this->lineItemStatusUserMapping[$newStatus];
            $userId = $activity->causer_id ?? null;
            if (! $userId || $lineItem->{$userColumn}) {
                $skippedCount++;

                continue;
            }
            if ($isDryRun) {
                $this->line("  Would update LineItem #{$lineItem->id} - {$userColumn} = {$userId}");
                $updatedCount++;
            } else {
                DB::table('line_items')
                    ->where('id', $lineItem->id)
                    ->update([
                        $userColumn => $userId,
                    ]);
                $updatedCount++;
            }
        }
        $this->info("  ✅ LineItems: {$updatedCount} updated, {$skippedCount} skipped");
    }
}
