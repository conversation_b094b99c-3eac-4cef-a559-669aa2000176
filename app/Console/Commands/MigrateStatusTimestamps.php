<?php

namespace App\Console\Commands;

use App\Models\LineItem;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class MigrateStatusTimestamps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:status-timestamps
                            {--dry-run : Show what would be updated without actually updating}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate status timestamps for existing orders and line items from activity logs';

    private array $orderStatusMapping = [
        'processing' => 'processing_at',
        'partial-shipped' => 'partial_shipped_at',
        'shipped' => 'shipped_at',
        'cancelled' => 'cancelled_at',
    ];

    private array $lineItemStatusMapping = [
        'in-production' => 'in_production_at',
        'packing' => 'packing_at',
        'packed' => 'packed_at',
        'shipped' => 'shipped_at',
        'cancelled' => 'cancelled_at',
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (! $this->option('force') && ! $this->option('dry-run')) {
            if (! $this->confirm('This will update status timestamps for existing orders and line items. Do you want to continue?')) {
                $this->info('Migration cancelled.');

                return 0;
            }
        }

        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting status timestamp migration...');

        // Migrate Order timestamps
        $this->migrateOrderTimestamps($isDryRun);

        // Migrate LineItem timestamps
        $this->migrateLineItemTimestamps($isDryRun);

        $this->info('✅ Status timestamp migration completed!');

        return 0;
    }

    private function migrateOrderTimestamps(bool $isDryRun): void
    {
        $this->info('📋 Processing Order status timestamps...');

        $connection = config('activitylog.database_connection', 'default');

        // Get activities for Order model with status changes
        $activities = Activity::on($connection)
            ->where('subject_type', Order::class)
            ->where('description', 'updated')
            ->where(function ($query) {
                foreach (array_keys($this->orderStatusMapping) as $status) {
                    $query->orWhereJsonContains('properties->attributes->status', $status);
                }
            })
            ->orderBy('created_at')
            ->get();

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($activities as $activity) {
            $order = Order::find($activity->subject_id);

            if (! $order) {
                continue;
            }

            $newStatus = $activity->properties['attributes']['status'] ?? null;

            if (! $newStatus || ! isset($this->orderStatusMapping[$newStatus])) {
                continue;
            }

            $timestampColumn = $this->orderStatusMapping[$newStatus];

            // Skip if timestamp is already set
            if ($order->{$timestampColumn}) {
                $skippedCount++;

                continue;
            }

            if ($isDryRun) {
                $this->line("  Would update Order #{$order->id} - {$timestampColumn} = {$activity->created_at}");
                $updatedCount++;
            } else {
                // Use DB query to avoid triggering model events/traits
                DB::table('orders')
                    ->where('id', $order->id)
                    ->update([
                        $timestampColumn => $activity->created_at,
                    ]);
                $updatedCount++;
            }
        }

        $this->info("  ✅ Orders: {$updatedCount} updated, {$skippedCount} skipped");
    }

    private function migrateLineItemTimestamps(bool $isDryRun): void
    {
        $this->info('📦 Processing LineItem status timestamps...');

        $connection = config('activitylog.database_connection', 'default');

        // Get activities for LineItem model with status changes
        $activities = Activity::on($connection)
            ->where('subject_type', LineItem::class)
            ->where('description', 'updated')
            ->where(function ($query) {
                foreach (array_keys($this->lineItemStatusMapping) as $status) {
                    $query->orWhereJsonContains('properties->attributes->status', $status);
                }
            })
            ->orderBy('created_at')
            ->get();

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($activities as $activity) {
            $lineItem = LineItem::find($activity->subject_id);

            if (! $lineItem) {
                continue;
            }

            $newStatus = $activity->properties['attributes']['status'] ?? null;

            if (! $newStatus || ! isset($this->lineItemStatusMapping[$newStatus])) {
                continue;
            }

            $timestampColumn = $this->lineItemStatusMapping[$newStatus];

            // Skip if timestamp is already set
            if ($lineItem->{$timestampColumn}) {
                $skippedCount++;

                continue;
            }

            if ($isDryRun) {
                $this->line("  Would update LineItem #{$lineItem->id} - {$timestampColumn} = {$activity->created_at}");
                $updatedCount++;
            } else {
                // Use DB query to avoid triggering model events/traits
                DB::table('line_items')
                    ->where('id', $lineItem->id)
                    ->update([
                        $timestampColumn => $activity->created_at,
                    ]);
                $updatedCount++;
            }
        }

        $this->info("  ✅ LineItems: {$updatedCount} updated, {$skippedCount} skipped");
    }
}
