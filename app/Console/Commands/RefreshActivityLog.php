<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Activitylog\Models\Activity;

class RefreshActivityLog extends Command
{
    protected $signature = 'app:refresh-activity-log';

    protected $description = 'Refresh the activity_log table';

    public function handle()
    {
        Activity::query()->truncate();

        $this->info('Activity log refreshed');
    }
}
