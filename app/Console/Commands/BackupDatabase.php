<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;

class BackupDatabase extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:backup-database';

    /**
     * The console command description.
     */
    protected $description = 'Backup the database to the backup disk';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $connectionName = Config::get('database.default');
        $connection = Config::get("database.connections.{$connectionName}");

        if (! $connection) {
            $this->error("Database connection '{$connectionName}' not found.");

            return Command::FAILURE;
        }

        $filename = $this->generateBackupFilename($connectionName);
        $zipFilename = $this->generateZipFilename($filename);
        $backupPath = storage_path("app/temp/{$filename}");
        $zipPath = storage_path("app/temp/{$zipFilename}");

        try {
            $this->info("Starting database backup for connection: {$connectionName}");

            $this->createBackupFile($connection, $backupPath);
            $this->compressBackup($backupPath, $zipPath);
            $this->storeBackupToDisk($zipPath, $zipFilename);
            $this->cleanupTempFiles($backupPath, $zipPath);

            $this->info("Database backup completed successfully: {$zipFilename}");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Backup failed: '.$e->getMessage());
            $this->cleanupTempFiles($backupPath, $zipPath ?? null);

            return Command::FAILURE;
        }
    }

    private function generateBackupFilename(string $connectionName): string
    {
        $environment = app()->environment();
        $timestamp = now()->format('Y-m-d_H-i-s');

        return "genesis_{$environment}_{$connectionName}_{$timestamp}.sql";
    }

    private function generateZipFilename(string $sqlFilename): string
    {
        return str_replace('.sql', '.zip', $sqlFilename);
    }

    private function createBackupFile(array $connection, string $backupPath): void
    {
        $this->ensureTempDirectoryExists();
        $this->createMysqlBackup($connection, $backupPath);
    }

    private function createMysqlBackup(array $connection, string $backupPath): void
    {
        $command = sprintf(
            'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($connection['host']),
            escapeshellarg($connection['port'] ?? 3306),
            escapeshellarg($connection['username']),
            escapeshellarg($connection['password']),
            escapeshellarg($connection['database']),
            escapeshellarg($backupPath)
        );

        $result = Process::run($command);

        if ($result->failed()) {
            throw new \Exception('MySQL backup failed: '.$result->errorOutput());
        }
    }

    private function compressBackup(string $backupPath, string $zipPath): void
    {
        if (! file_exists($backupPath)) {
            throw new \Exception("Backup file not found: {$backupPath}");
        }

        $zip = new \ZipArchive;
        $result = $zip->open($zipPath, \ZipArchive::CREATE);

        if ($result !== true) {
            throw new \Exception("Failed to create zip file: {$zipPath}");
        }

        $zip->addFile($backupPath, basename($backupPath));
        $zip->close();

        if (! file_exists($zipPath)) {
            throw new \Exception("Failed to create compressed backup: {$zipPath}");
        }
    }

    private function storeBackupToDisk(string $filePath, string $filename): void
    {
        $backupDisk = Storage::disk('backup');

        if (! file_exists($filePath)) {
            throw new \Exception("Backup file not found: {$filePath}");
        }

        $backupDisk->put($filename, file_get_contents($filePath));
    }

    private function cleanupTempFiles(?string ...$filePaths): void
    {
        foreach ($filePaths as $filePath) {
            if ($filePath && file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }

    private function ensureTempDirectoryExists(): void
    {
        $tempDir = storage_path('app/temp');

        if (! is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
    }
}
