<?php

namespace App\Console\Commands;

use App\Models\EtsyShop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SyncEtsyShops extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'etsy:sync-shops {--force : Force sync even if no changes detected}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync EtsyShop data from API daily';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting EtsyShop sync...');

        try {
            // Fetch data from API
            $response = Http::timeout(30)->get('https://etspy.tdagroup.online/api/shops');

            if (! $response->successful()) {
                $this->error('Failed to fetch data from API. Status: '.$response->status());

                return Command::FAILURE;
            }

            $apiData = $response->json();

            if (empty($apiData)) {
                $this->error('No data received from API');

                return Command::FAILURE;
            }

            $this->info('Fetched '.count($apiData).' shops from API');

            // Filter out shops with blank id_etsy
            $validShops = collect($apiData)->filter(function ($shop) {
                return ! empty($shop['id_etsy']);
            });

            $this->info('Found '.$validShops->count().' shops with valid id_etsy');

            // Sync data
            $stats = $this->syncShops($validShops);

            $this->info('Sync completed successfully!');
            $this->table(
                ['Action', 'Count'],
                [
                    ['Created', $stats['created']],
                    ['Updated', $stats['updated']],
                    ['Skipped', $stats['skipped']],
                    ['Total Processed', $stats['total']],
                ]
            );

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Sync failed: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * Sync shops data with database
     */
    private function syncShops($shops)
    {
        $stats = [
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'total' => 0,
        ];

        $progressBar = $this->output->createProgressBar($shops->count());
        $progressBar->start();

        foreach ($shops as $shopData) {
            $stats['total']++;

            // Find existing shop by id_etsy
            $existingShop = EtsyShop::where('id_etsy', $shopData['id_etsy'])->first();

            if ($existingShop) {
                // Check if username has changed
                if ($existingShop->username !== $shopData['username']) {
                    $existingShop->update(['username' => $shopData['username']]);
                    $stats['updated']++;
                } else {
                    $stats['skipped']++;
                }
            } else {
                // Create new shop
                EtsyShop::create([
                    'id_etsy' => $shopData['id_etsy'],
                    'username' => $shopData['username'],
                ]);
                $stats['created']++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        return $stats;
    }
}
