<?php

namespace App\Models\Traits;

use App\Models\Comment;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait Commentable
{
    /**
     * Get all the model's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable')
            ->with('user')
            ->orderBy('created_at', 'desc');
    }

    /**
     * Add a comment to this model.
     */
    public function addComment(string $content, int $userId): Comment
    {
        return $this->comments()->create([
            'content' => $content,
            'user_id' => $userId,
        ]);
    }
}
