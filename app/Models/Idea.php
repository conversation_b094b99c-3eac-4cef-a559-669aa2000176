<?php

namespace App\Models;

use App\Actions\GenerateIdeaUid;
use App\Enums\IdeaType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Idea extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\IdeaFactory> */
    use HasFactory;

    use HasTags;
    use InteractsWithMedia;
    use LogsChange;
    use SoftDeletes;

    protected $table = 'ideas';

    public function casts(): array
    {
        return [
            'is_trademark'           => 'boolean',
            'is_personalized'        => 'boolean',
            'is_made_by_ai'          => 'boolean',
            'made_by_ai_description' => 'array',
            'type'                   => IdeaType::class,
        ];
    }

    protected static function booted(): void
    {
        static::created(function ($idea) {
            if (blank($idea->uid)) {
                $idea->update([
                    'uid' => GenerateIdeaUid::make()->handle($idea),
                ]);
            }
        });
    }

    public function delete()
    {
        $this->productVariants()->delete();

        parent::delete();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function productTypes(): BelongsToMany
    {
        return $this->belongsToMany(ProductType::class, 'designs')
            ->using(Design::class);
    }

    public function productVariants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function designs(): HasMany
    {
        return $this->hasMany(Design::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Idea::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Idea::class, 'parent_id');
    }

    public function isChildIdea(): bool
    {
        return !blank($this->parent_id);
    }

    public function isSubIdea(): bool
    {
        return $this->isChildIdea();
    }

    public function listingUrls(): HasMany
    {
        return $this->hasMany(ListingUrl::class);
    }

    public function isParent(): bool
    {
        return is_null($this->parent_id);
    }

    public function isChild(): bool
    {
        return !is_null($this->parent_id);
    }

    public function isSimple(): bool
    {
        return $this->type === IdeaType::Simple;
    }

    public function isGrouped(): bool
    {
        return $this->type === IdeaType::Grouped;
    }
}
