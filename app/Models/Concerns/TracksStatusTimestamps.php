<?php

namespace App\Models\Concerns;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

trait TracksStatusTimestamps
{
    public static function bootTracksStatusTimestamps(): void
    {
        static::updating(function (Model $model) {
            $model->handleStatusTimestamp();
        });
    }

    protected function handleStatusTimestamp(): void
    {
        if (! $this->isDirty('status')) {
            return;
        }

        $newStatus = $this->getAttribute('status');
        $statusMapping = $this->getStatusTimestampMapping();
        $userMapping = method_exists($this, 'getStatusUserMapping') ? $this->getStatusUserMapping() : [];

        if (isset($statusMapping[$newStatus->value])) {
            $timestampColumn = $statusMapping[$newStatus->value];
            $this->setAttribute($timestampColumn, Carbon::now());

            if (isset($userMapping[$newStatus->value])) {
                $userColumn = $userMapping[$newStatus->value];
                $userId = \Illuminate\Support\Facades\Auth::id();
                $this->setAttribute($userColumn, $userId);
            }
        }
    }

    abstract protected function getStatusTimestampMapping(): array;
}
