<?php

namespace App\Models;

use App\Enums\QcLineItemStatus;
use App\Enums\QcOrderCheckStatus;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QcOrderCheck extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'status' => QcOrderCheckStatus::class,
            'confirmed_at' => 'datetime',
        ];
    }

    public function qcSession(): BelongsTo
    {
        return $this->belongsTo(QcSession::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function lineItemChecks(): HasMany
    {
        return $this->hasMany(QcLineItemCheck::class);
    }

    #[Scope]
    public function processing(Builder $query): Builder
    {
        return $query->where('status', QcOrderCheckStatus::Processing);
    }

    public function isProcessing(): bool
    {
        return $this->status === QcOrderCheckStatus::Processing;
    }

    public function isCompleted(): bool
    {
        return $this->status === QcOrderCheckStatus::Completed;
    }

    public function hasAllLineItemsChecked(): bool
    {
        $totalLineItems = $this->order->lineItems()->count();
        $checkedLineItems = $this->lineItemChecks()->count();

        return $totalLineItems > 0 && $totalLineItems === $checkedLineItems;
    }

    public function hasMissingItems(): bool
    {
        return $this->lineItemChecks()
            ->where('status', QcLineItemStatus::Missing->value)
            ->exists();
    }

    /**
     * Scope to eager load line item check counts for performance optimization
     */
    public function scopeWithLineItemCheckCounts(Builder $query): Builder
    {
        return $query->withCount([
            'lineItemChecks',
            'lineItemChecks as missing_items_count' => function ($query) {
                $query->where('status', QcLineItemStatus::Missing->value);
            },
            'lineItemChecks as ready_items_count' => function ($query) {
                $query->where('status', QcLineItemStatus::Ready->value);
            },
        ]);
    }
}
