<?php

namespace App\Models;

use App\Actions\SplitTrackingNumbers;
use App\Enums\TransportHandoverStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class TransportHandover extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'status' => TransportHandoverStatus::class,
            'confirmed_at' => 'datetime',
        ];
    }

    public function thirdPartyLogistic(): BelongsTo
    {
        return $this->belongsTo(ThirdPartyLogistic::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function shipments(): BelongsToMany
    {
        return $this->belongsToMany(Shipment::class, 'handover_shipments');
    }

    public function isDraft(): bool
    {
        return $this->status === TransportHandoverStatus::Draft;
    }

    public function isConfirmed(): bool
    {
        return $this->status === TransportHandoverStatus::Confirmed;
    }

    public function getParsedTrackingNumbers(): array
    {
        return SplitTrackingNumbers::make()->handle($this->tracking_numbers ?? '');
    }
}
