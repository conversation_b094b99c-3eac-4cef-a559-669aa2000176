<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Uri;

class ListingUrl extends Model
{
    /** @use HasFactory<\Database\Factories\ListingUrlFactory> */
    use HasFactory;

    protected static function booted(): void
    {
        static::saving(function (ListingUrl $listingUrl) {
            $uri = Uri::of($listingUrl->link);
            $listingUrl->fill([
                'domain' => $uri->host(),
                'link_without_query_string' => $uri->scheme().'://'.$uri->host().$uri->path(),
            ]);
        });
    }

    public function idea()
    {
        return $this->belongsTo(Idea::class);
    }

    public function design()
    {
        return $this->belongsTo(Design::class);
    }
}
