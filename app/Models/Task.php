<?php

namespace App\Models;

use App\Contracts\HasComments;
use App\Enums\TaskStatus;
use App\Models\Traits\Commentable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Task extends Model implements HasComments
{
    /** @use HasFactory<\Database\Factories\TaskFactory> */
    use HasFactory;
    use Commentable;

    public function casts(): array
    {
        return [
            'status'                => TaskStatus::class,
            'meta_data'             => 'array',
            'approved_at'           => 'datetime',
            'revision_requested_at' => 'datetime',
            'processing_at'         => 'datetime',
            'submitted_at'          => 'datetime',
        ];
    }

    public function designs(): MorphToMany
    {
        return $this->morphedByMany(Design::class, 'taskable');
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    public function isTodo(): bool
    {
        return $this->status === TaskStatus::Todo;
    }

    public function isProcessing(): bool
    {
        return $this->status === TaskStatus::Processing;
    }

    public function isNeedRevision(): bool
    {
        return $this->status === TaskStatus::NeedRevision;
    }

    public function isSubmitted(): bool
    {
        return $this->status === TaskStatus::Submitted;
    }

    public function isApproved(): bool
    {
        return $this->status === TaskStatus::Approved;
    }
}
