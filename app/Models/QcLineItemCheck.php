<?php

namespace App\Models;

use App\Enums\QcLineItemStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QcLineItemCheck extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'status' => QcLineItemStatus::class,
            'checked_at' => 'datetime',
        ];
    }

    public function qcOrderCheck(): BelongsTo
    {
        return $this->belongsTo(QcOrderCheck::class);
    }

    public function lineItem(): BelongsTo
    {
        return $this->belongsTo(LineItem::class);
    }

    public function isReady(): bool
    {
        return $this->status === QcLineItemStatus::Ready;
    }

    public function isMissing(): bool
    {
        return $this->status === QcLineItemStatus::Missing;
    }
}
