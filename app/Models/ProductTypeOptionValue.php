<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ProductTypeOptionValue extends Model
{
    protected $fillable = [
        'product_type_option_id',
        'value',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'sort_order' => 'integer',
        ];
    }

    public function option(): BelongsTo
    {
        return $this->belongsTo(ProductTypeOption::class, 'product_type_option_id');
    }

    public function productVariants(): BelongsToMany
    {
        return $this->belongsToMany(
            ProductVariant::class,
            'product_variant_option_values',
            'product_type_option_value_id',
            'product_variant_id'
        )->withTimestamps();
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
