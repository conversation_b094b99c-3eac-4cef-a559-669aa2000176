<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ThirdPartyLogistic extends Model
{
    /** @use HasFactory<\Database\Factories\ThirdPartyLogisticFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
    ];

    public function shipments(): HasMany
    {
        return $this->hasMany(Shipment::class);
    }
}
