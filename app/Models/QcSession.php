<?php

namespace App\Models;

use App\Enums\QcSessionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QcSession extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'status' => QcSessionStatus::class,
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function orderChecks(): HasMany
    {
        return $this->hasMany(QcOrderCheck::class);
    }

    public function isNew(): bool
    {
        return $this->status === QcSessionStatus::New;
    }

    public function isProcessing(): bool
    {
        return $this->status === QcSessionStatus::Processing;
    }

    public function isCompleted(): bool
    {
        return $this->status === QcSessionStatus::Completed;
    }

    public function isCancelled(): bool
    {
        return $this->status === QcSessionStatus::Cancelled;
    }

    public function isActive(): bool
    {
        return in_array($this->status, [QcSessionStatus::New, QcSessionStatus::Processing]);
    }
}
