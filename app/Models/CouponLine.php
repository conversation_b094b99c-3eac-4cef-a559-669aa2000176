<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CouponLine extends Model
{
    /** @use HasFactory<\Database\Factories\CouponLineFactory> */
    use HasFactory;

    protected function casts(): array
    {
        return [
            'end_date' => 'datetime',
        ];
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
