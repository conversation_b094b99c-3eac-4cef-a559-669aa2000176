<?php

namespace App\Models;

use App\Enums\DesignStatus;
use App\Models\Traits\Taskable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Design extends Pivot implements HasMedia
{
    /** @use HasFactory<\Database\Factories\DesignFactory> */
    use HasFactory;

    use InteractsWithMedia;
    use LogsChange;
    use Taskable;

    protected $table = 'designs';

    public $incrementing = true;

    public $timestamps = true;

    public function casts(): array
    {
        return [
            'status'            => DesignStatus::class,
            'score'             => 'float',
            'design_by_company' => 'boolean',
        ];
    }

    public function idea(): BelongsTo
    {
        return $this->belongsTo(Idea::class);
    }

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    public function designer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'designer_id');
    }

    /**
     * Dummy relationship for the RelatedDesignsRelationManager
     * The actual query is handled in the relation manager
     */
    public function relatedDesigns(): HasMany
    {
        return $this->hasMany(Design::class);
    }

    public function tasks(): MorphToMany
    {
        return $this->morphToMany(Task::class, 'taskable');
    }

    public function latestTask(): MorphOne
    {
        return $this->morphOne(Task::class, 'taskable')
            ->latestOfMany('created_at');
    }

    public function designByCompany(): bool
    {
        return $this->design_by_company;
    }
}
