<?php

namespace App\Models;

use App\Actions\GetTaxLabelAndMessage;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\Platform;
use App\Enums\QcOrderCheckStatus;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    /** @use HasFactory<\Database\Factories\OrderFactory> */
    use HasFactory;

    use LogsChange;

    protected function casts(): array
    {
        return [
            'platform' => Platform::class,
            'status' => OrderStatus::class,
            'is_gift' => 'boolean',
            'is_gift_wrapped' => 'boolean',
            'cost_breakdown' => 'array',
            'order_date' => 'datetime',
            'date_paid' => 'datetime',
            'expected_ship_date' => 'datetime',
            'has_gift_teaser' => 'boolean',
        ];
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(LineItem::class)->chaperone();
    }

    public function billingAddress(): HasOne
    {
        return $this->hasOne(BillingAddress::class);
    }

    public function shippingAddress(): HasOne
    {
        return $this->hasOne(ShippingAddress::class);
    }

    public function couponLines(): HasMany
    {
        return $this->hasMany(CouponLine::class);
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function backupSeller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'backup_seller_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function shipments(): BelongsToMany
    {
        return $this->belongsToMany(Shipment::class, 'line_items', 'order_id', 'shipment_id')->distinct();
    }

    public function qcOrderCheck(): HasOne
    {
        return $this->hasOne(QcOrderCheck::class);
    }

    public function etsyShop(): BelongsTo
    {
        return $this->belongsTo(EtsyShop::class, 'shop_id_platform', 'id_etsy');
    }

    public function cancelRequests(): HasMany
    {
        return $this->hasMany(OrderCancelRequest::class);
    }

    public function latestPendingCancelRequest(): HasOne
    {
        return $this->hasOne(OrderCancelRequest::class)->pending()->latest();
    }

    #[Scope]
    protected function draft(Builder $query): Builder
    {
        return $query->where('status', OrderStatus::Draft);
    }

    #[Scope]
    protected function new(Builder $query): Builder
    {
        return $query->where('status', OrderStatus::New);
    }

    public function isDraft(): bool
    {
        return $this->status === OrderStatus::Draft;
    }

    public function isNew(): bool
    {
        return $this->status === OrderStatus::New;
    }

    public function paidUsingVnd(): bool
    {
        return strtolower($this->currency_code) === 'vnd';
    }

    public function isFullyShipped(): bool
    {
        $totalLineItems = $this->lineItems()->count();
        $shippedLineItems = $this->lineItems()->where('status', LineItemStatus::Shipped)->count();

        return $totalLineItems > 0 && $totalLineItems === $shippedLineItems;
    }

    public function isPartiallyShipped(): bool
    {
        $shippedLineItems = $this->lineItems()->where('status', LineItemStatus::Shipped)->count();

        return $shippedLineItems > 0 && ! $this->isFullyShipped();
    }

    public function isProcessing(): bool
    {
        return $this->status === OrderStatus::Processing;
    }

    public function canBeQualityControlled(): bool
    {
        return ($this->isProcessing() || $this->isPartiallyShipped()) && $this->hasInProductionLineItems();
    }

    public function isQualityChecked(): bool
    {
        return $this->qcOrderCheck()->where('status', QcOrderCheckStatus::Completed)->exists();
    }

    public function isQualityChecking(): bool
    {
        return $this->qcOrderCheck()->where('status', QcOrderCheckStatus::Processing)->exists();
    }

    public function fromEtsy(): bool
    {
        return $this->platform === Platform::Etsy;
    }

    public function getTaxLabel(): ?string
    {
        if (! $this->fromEtsy()) {
            return null;
        }

        return GetTaxLabelAndMessage::make()->handle($this->cost_breakdown)[0];
    }

    public function getTaxMessage(): ?string
    {
        if (! $this->fromEtsy()) {
            return null;
        }

        return GetTaxLabelAndMessage::make()->handle($this->cost_breakdown)[1];
    }

    public function shopName(): ?string
    {
        if ($this->fromEtsy() && $this->etsyShop) {
            return $this->etsyShop->username;
        }

        return $this->shop_id_platform;
    }

    public function hasInProductionLineItems(): bool
    {
        return $this->lineItems->contains(
            fn ($lineItem) => $lineItem->isInProduction()
        );
    }

    public function hasPendingCancelRequest(): bool
    {
        return $this->cancelRequests->contains(
            fn ($cancelRequest) => $cancelRequest->isPending()
        );
    }

    public function canBeCancelled(): bool
    {
        // Cannot cancel if already cancelled or shipped
        if (in_array($this->status, [OrderStatus::Cancelled, OrderStatus::Shipped])) {
            return false;
        }

        // Cannot cancel if already has a pending cancel request
        if ($this->hasPendingCancelRequest()) {
            return false;
        }

        return true;
    }

    public function getLatestCancelRequest(): ?OrderCancelRequest
    {
        return $this->cancelRequests()->latest()->first();
    }

    public function delete()
    {
        $this->lineItems()->delete();
        $this->billingAddress()->delete();
        $this->shippingAddress()->delete();
        $this->couponLines()->delete();

        parent::delete();
    }
}
