<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingAddress extends Model
{
    /** @use HasFactory<\Database\Factories\ShippingAddressFactory> */
    use HasFactory;

    use LogsChange;

    protected function casts(): array
    {
        return [
            'is_usps_verified' => 'boolean',
        ];
    }

    public function fullAddress(): Attribute
    {
        return Attribute::make(
            get: fn () => implode(', ', array_filter([
                $this->line_one,
                $this->line_two,
                $this->city,
                $this->state,
                $this->zip,
                $this->country,
            ])));
    }
}
