<?php

namespace App\Models;

use App\Enums\Platform;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OrdersImportRequest extends Model
{
    /** @use HasFactory<\Database\Factories\OrderImportRequestFactory> */
    use HasFactory;

    protected $casts = [
        'raw_data' => 'array',
        'processed_at' => 'datetime',
        'platform' => Platform::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function isFromEtsy(): bool
    {
        return $this->platform === Platform::Etsy;
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function markAsProcessed(): static
    {
        return tap($this)->update(['processed_at' => now()]);
    }
}
