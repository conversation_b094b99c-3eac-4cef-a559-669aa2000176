<?php

namespace App\Models;

use App\Enums\OrderCancelRequestStatus;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderCancelRequest extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'status' => OrderCancelRequestStatus::class,
            'processed_at' => 'datetime',
        ];
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public function isPending(): bool
    {
        return $this->status === OrderCancelRequestStatus::Pending;
    }

    public function isApproved(): bool
    {
        return $this->status === OrderCancelRequestStatus::Approved;
    }

    public function isRejected(): bool
    {
        return $this->status === OrderCancelRequestStatus::Rejected;
    }

    public function isProcessed(): bool
    {
        return $this->isApproved() || $this->isRejected();
    }

    #[Scope]
    protected function pending(Builder $query): Builder
    {
        return $query->where('status', OrderCancelRequestStatus::Pending);
    }

    #[Scope]
    protected function approved(Builder $query): Builder
    {
        return $query->where('status', OrderCancelRequestStatus::Approved);
    }

    #[Scope]
    protected function rejected(Builder $query): Builder
    {
        return $query->where('status', OrderCancelRequestStatus::Rejected);
    }

    #[Scope]
    protected function processed(Builder $query): Builder
    {
        return $query->whereIn('status', [OrderCancelRequestStatus::Approved, OrderCancelRequestStatus::Rejected]);
    }
}
