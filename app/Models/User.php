<?php

namespace App\Models;

use App\Enums\QcSessionStatus;
use App\Enums\UserRole;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        return match ($panel->getId()) {
            'seller' => $this->isSeller(),
            'designer' => $this->isDesigner(),
            'backoffice' => $this->isOperator() || $this->isManager(),
            default => false,
        };
    }

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => UserRole::class,
        ];
    }

    public function ordersImportRequest(): HasMany
    {
        return $this->hasMany(OrdersImportRequest::class);
    }

    public function importedOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'seller_id');
    }

    public function qcSessions(): HasMany
    {
        return $this->hasMany(QcSession::class);
    }

    public function hasActiveQcSession(): bool
    {
        return $this->qcSessions()->whereIn('status', [QcSessionStatus::New, QcSessionStatus::Processing])->exists();
    }

    public function cancelRequests(): HasMany
    {
        return $this->hasMany(OrderCancelRequest::class, 'seller_id');
    }

    public function processedCancelRequests(): HasMany
    {
        return $this->hasMany(OrderCancelRequest::class, 'processed_by');
    }

    public function ideas(): HasMany
    {
        return $this->hasMany(Idea::class);
    }

    #[Scope]
    protected function seller(Builder $query): Builder
    {
        return $query->where('role', UserRole::Seller);
    }

    public function isSeller(): bool
    {
        return $this->role === UserRole::Seller;
    }

    public function isDesigner(): bool
    {
        return $this->role === UserRole::Designer;
    }

    public function isOperator(): bool
    {
        return $this->role === UserRole::Operator;
    }

    public function isManager(): bool
    {
        return $this->role === UserRole::Manager;
    }

    public function isSuperAdmin(): bool
    {
        return in_array(
            $this->email,
            config('genesis.super_admins', [])
        );
    }

    public function isInchargeOfOrder(Order $order): bool
    {
        return $this->id === $order->seller_id || $this->id === $order->backup_seller_id;
    }
}
