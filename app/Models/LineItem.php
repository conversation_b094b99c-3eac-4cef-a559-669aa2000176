<?php

namespace App\Models;

use App\Enums\LineItemStatus;
use App\Enums\Platform;
use App\Enums\QcLineItemStatus;
use App\Models\Concerns\TracksStatusTimestamps;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class LineItem extends Model
{
    /** @use HasFactory<\Database\Factories\LineItemFactory> */
    use HasFactory;

    use LogsChange;
    use TracksStatusTimestamps;

    protected function casts(): array
    {
        return [
            'variations' => 'array',
            'meta_data' => 'array',
            'is_download' => 'boolean',
            'is_personalizable' => 'boolean',
            'status' => LineItemStatus::class,
            'price' => 'integer',
            'subtotal' => 'integer',
            'quantity' => 'integer',
            'in_production_at' => 'datetime',
            'packing_at' => 'datetime',
            'packed_at' => 'datetime',
            'shipped_at' => 'datetime',
            'cancelled_at' => 'datetime',
        ];
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function qcLineItemChecks(): HasMany
    {
        return $this->hasMany(QcLineItemCheck::class);
    }

    public function shippingAddress(): HasOneThrough
    {
        return $this->hasOneThrough(ShippingAddress::class, Order::class, 'id', 'order_id', 'order_id', 'id');
    }

    public function variationsLines(): array
    {
        return collect($this->variations ?? [])
            ->sortBy('order')
            ->map(fn ($item) => sprintf('%s: %s', html_entity_decode($item['label']), $item['value']))
            ->toArray();
    }

    public function productAttributes()
    {
        return collect($this->variationsLines())
            ->filter(fn ($item) => ! empty($item));
    }

    public function attributesString(): string
    {
        return collect($this->variationsLines())
            ->filter(fn ($item) => ! empty($item))
            ->implode(', ');
    }

    public function paidUsingVnd(): bool
    {
        return strtolower($this->currency_code) === 'vnd';
    }

    public function productUrl(): ?string
    {
        return match ($this->order->platform) {
            Platform::Etsy => 'https://www.etsy.com/listing/'.$this->etsy_listing_id,
            default => null
        };
    }

    public function size(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getSize(),
            set: fn ($value) => $this->setSize($value),
        );
    }

    public function color(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getColor(),
            set: fn ($value) => $this->setColor($value),
        );
    }

    /**
     * Find the index of an attribute in variations by matching possible label variations
     */
    private function findAttributeIndex(string $attributeType): int|false
    {
        $variations = $this->variations ?? [];

        $searchKeys = match ($attributeType) {
            'color' => ['color', 'color:'],
            'size' => ['size', 'size:'],
            'personalization' => ['personalization', 'personalisation', 'personalized'],
            default => [$attributeType]
        };

        return collect($variations)
            ->search(fn ($item) => collect($searchKeys)->contains(fn ($key) => str_starts_with(strtolower($item['label']), $key)));
    }

    /**
     * Set an attribute value in variations
     */
    private function setVariationAttribute(string $attributeType, ?string $value): static
    {
        $variations = $this->variations ?? [];
        $attributeIndex = $this->findAttributeIndex($attributeType);

        $labelMap = [
            'color' => 'Color',
            'size' => 'Size',
            'personalization' => 'Personalization',
        ];

        if ($attributeIndex !== false) {
            // Update existing attribute
            $variations[$attributeIndex]['value'] = $value;
        } else {
            // Add new attribute
            $variations[] = [
                'label' => $labelMap[$attributeType] ?? ucfirst($attributeType),
                'value' => $value,
            ];
        }

        // Update the variations attribute (this will be saved when the model is saved)
        $this->variations = $variations;

        return $this;
    }

    public function setColor(?string $value): static
    {
        return $this->setVariationAttribute('color', $value);
    }

    public function setSize(?string $value): static
    {
        return $this->setVariationAttribute('size', $value);
    }

    public function setPersonalization(?string $value): static
    {
        return $this->setVariationAttribute('personalization', $value);
    }

    public function personalization(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getPersonalization(),
            set: fn ($value) => $this->setPersonalization($value),
        );
    }

    /**
     * Get an attribute value from variations
     */
    protected function getVariationAttribute(string $attributeType): ?string
    {
        $variations = $this->variations ?? [];
        $attributeIndex = $this->findAttributeIndex($attributeType);

        if ($attributeIndex !== false) {
            return $variations[$attributeIndex]['value'] ?? null;
        }

        return null;
    }

    public function getColor(): ?string
    {
        return $this->getVariationAttribute('color');
    }

    public function getSize(): ?string
    {
        return $this->getVariationAttribute('size');
    }

    public function getPersonalization(): ?string
    {
        return $this->getVariationAttribute('personalization');
    }

    public function isNew(): bool
    {
        return $this->status === LineItemStatus::New;
    }

    public function isInProduction(): bool
    {
        return $this->status === LineItemStatus::InProduction;
    }

    public function isPacking(): bool
    {
        return $this->status === LineItemStatus::Packing;
    }

    public function isPacked(): bool
    {
        return $this->status === LineItemStatus::Packed;
    }

    public function isShipped(): bool
    {
        return $this->status === LineItemStatus::Shipped;
    }

    public function canBeQualityControlled(): bool
    {
        return $this->isInProduction() && ! $this->qcLineItemChecks()->where('status', QcLineItemStatus::Ready)->exists();
    }

    public function displayItemsInNew(): bool
    {
        return $this->isNew() && ! $this->qcLineItemChecks()->where('status', QcLineItemStatus::Ready)->exists();
    }

    protected function getStatusTimestampMapping(): array
    {
        return [
            LineItemStatus::InProduction->value => 'in_production_at',
            LineItemStatus::Packing->value => 'packing_at',
            LineItemStatus::Packed->value => 'packed_at',
            LineItemStatus::Shipped->value => 'shipped_at',
            LineItemStatus::Cancelled->value => 'cancelled_at',
        ];
    }

    protected function getStatusUserMapping(): array
    {
        return [
            LineItemStatus::InProduction->value => 'to_in_production_by_id',
            LineItemStatus::Packing->value => 'to_packing_by_id',
            LineItemStatus::Packed->value => 'to_packed_by_id',
            LineItemStatus::Shipped->value => 'to_shipped_by_id',
            LineItemStatus::Cancelled->value => 'to_cancelled_by_id',
        ];
    }

    public function compositeNotes(): string
    {
        $notes = [];
        $notes[] = 'Shop ID: '.$this->order->shop_id_platform;
        $notes[] = 'Is gift: '.($this->order->is_gift ? 'Yes' : 'No');
        $notes[] = 'Customer note: '.$this->order->customer_note;

        return implode("\n", $notes);
    }
}
