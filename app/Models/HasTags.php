<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasTags
{
    /**
     * Pending tags to be synced after save.
     */
    protected $pendingTagsToSync = null;

    /**
     * Boot the HasTags trait for a model.
     */
    public static function bootHasTags(): void
    {
        static::saved(function ($model) {
            if ($model->pendingTagsToSync !== null) {
                $model->syncTags($model->pendingTagsToSync);
                $model->pendingTagsToSync = null;
            }
        });

        static::deleting(function ($model) {
            $model->tags()->detach();
        });
    }

    /**
     * Get all tags for this model.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    /**
     * Sync tags with the model.
     */
    public function syncTags(array $tagNames): void
    {
        $tagIds = [];

        foreach ($tagNames as $tagName) {
            if (empty($tagName)) {
                continue;
            }

            $tag = Tag::firstOrCreate(['name' => trim($tagName)]);
            $tagIds[] = $tag->id;
        }

        $this->tags()->sync($tagIds);
    }

    /**
     * Set pending tags to be synced after save.
     */
    public function setPendingTags(array $tagNames): void
    {
        $this->pendingTagsToSync = $tagNames;
    }

    /**
     * Get tag names as array.
     */
    public function getTagNames(): array
    {
        return $this->tags->pluck('name')->toArray();
    }

    /**
     * Add a single tag.
     */
    public function addTag(string $tagName): void
    {
        if (empty($tagName)) {
            return;
        }

        $tag = Tag::firstOrCreate(['name' => trim($tagName)]);
        $this->tags()->syncWithoutDetaching([$tag->id]);
    }

    /**
     * Remove a single tag.
     */
    public function removeTag(string $tagName): void
    {
        $tag = Tag::where('name', trim($tagName))->first();

        if ($tag) {
            $this->tags()->detach($tag->id);
        }
    }

    /**
     * Check if model has a specific tag.
     */
    public function hasTag(string $tagName): bool
    {
        return $this->tags()->where('name', trim($tagName))->exists();
    }

    /**
     * Detach all tags from this model.
     */
    public function detachAllTags(): void
    {
        $this->tags()->detach();
    }
}
