<?php

namespace App\Models;

use App\Actions\GenerateProductVariantVid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends Model
{
    /** @use HasFactory<\Database\Factories\ProductVariantFactory> */
    use HasFactory;

    use LogsChange;
    use SoftDeletes;

    protected function casts(): array
    {
        return [
            'is_out_of_stock' => 'boolean',
        ];
    }

    protected static function booted(): void
    {
        static::created(function ($productVariant) {
            if (blank($productVariant->vid)) {
                $vid = GenerateProductVariantVid::make()->handle($productVariant);
                $productVariant->update([
                    'vid' => $vid,
                    'sku' => $productVariant->idea->uid.'-'.$vid,
                ]);
            }
        });
    }

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    public function idea(): BelongsTo
    {
        return $this->belongsTo(Idea::class);
    }

    public function optionValues(): BelongsToMany
    {
        return $this->belongsToMany(
            ProductTypeOptionValue::class,
            'product_variant_option_values',
            'product_variant_id',
            'product_type_option_value_id'
        )->withTimestamps();
    }

    public function attributesLines(): array
    {
        $lines = [];

        // Make sure optionValues are loaded with their options
        if (! $this->relationLoaded('optionValues')) {
            $this->load('optionValues.option');
        }

        foreach ($this->optionValues as $optionValue) {
            $optionName = $optionValue->option->name;
            $lines[] = "{$optionName}: {$optionValue->value}";
        }

        return $lines;
    }

    public function getAttributesData(): array
    {
        $attributes = [];

        // Make sure optionValues are loaded with their options
        if (! $this->relationLoaded('optionValues')) {
            $this->load('optionValues.option');
        }

        foreach ($this->optionValues as $optionValue) {
            $optionName = $optionValue->option->name;
            $attributes[$optionName] = $optionValue->value;
        }

        return $attributes;
    }
}
