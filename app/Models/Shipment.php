<?php

namespace App\Models;

use App\Enums\ShipmentStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Shipment extends Model
{
    /** @use HasFactory<\Database\Factories\ShipmentFactory> */
    use HasFactory;

    use LogsChange;

    protected function casts(): array
    {
        return [
            'status' => ShipmentStatus::class,
            'shipped_at' => 'datetime',
            'expected_delivery_date' => 'datetime',
            'delivered_at' => 'datetime',
        ];
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(LineItem::class);
    }

    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'line_items', 'shipment_id', 'order_id')
            ->distinct();
    }

    public function carrier(): BelongsTo
    {
        return $this->belongsTo(Carrier::class);
    }

    public function thirdPartyLogistic(): BelongsTo
    {
        return $this->belongsTo(ThirdPartyLogistic::class);
    }

    public function transportHandovers(): BelongsToMany
    {
        return $this->belongsToMany(TransportHandover::class, 'handover_shipments');
    }

    public function size(): Attribute
    {
        return Attribute::make(
            get: fn () => sprintf(
                '%s x %s x %s',
                $this->length ?: '_',
                $this->width ?: '_',
                $this->height ?: '_',
            )
        );
    }

    public function isPending(): bool
    {
        return $this->status === ShipmentStatus::Pending;
    }

    public function isPickedUp(): bool
    {
        return $this->status === ShipmentStatus::PickedUp;
    }

    public function isDelivered(): bool
    {
        return $this->status === ShipmentStatus::Delivered;
    }

    public function getCurrentTrackingNumber(): ?string
    {
        return $this->tracking_number;
    }

    public function getCurrentCarrier(): ?Carrier
    {
        return $this->carrier;
    }
}
