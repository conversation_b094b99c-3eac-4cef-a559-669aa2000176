<?php

namespace App\Livewire;

use Illuminate\Database\Eloquent\Model;
use Livewire\Component;
use Spatie\Activitylog\Models\Activity;

class ActivityTimeline extends Component
{
    public const int COMPACT_STRING_LIMIT = 322;

    public const int COMPACT_JSON_LIMIT = 322;

    public Model $record;

    public function mount(Model $record)
    {
        $this->record = $record;
    }

    public function getActivitiesProperty()
    {
        // Query activities from the logging database (SQLite)
        $query = Activity::on(config('activitylog.database_connection'));

        // Add activities for the main record only
        $query->where('subject_type', get_class($this->record))
            ->where('subject_id', $this->record->getKey());

        // Get activities from logging database
        $activities = $query->orderBy('created_at', 'desc')->orderBy('id', 'desc')->get();

        // Load causers from main database for each activity
        $this->loadCausersFromMainDatabase($activities);

        return $activities;
    }

    protected function loadCausersFromMainDatabase($activities): void
    {
        // Get unique causer IDs
        $causerIds = $activities->whereNotNull('causer_id')
            ->pluck('causer_id')
            ->unique()
            ->filter()
            ->toArray();

        if (empty($causerIds)) {
            return;
        }

        // Load causers from main database (MySQL) - explicitly specify connection
        $causers = \App\Models\User::on(config('database.default'))
            ->whereIn('id', $causerIds)
            ->get()
            ->keyBy('id');

        // Attach causers to activities
        foreach ($activities as $activity) {
            if ($activity->causer_id && isset($causers[$activity->causer_id])) {
                $activity->setRelation('causer', $causers[$activity->causer_id]);
            }
        }
    }

    public function getEventIcon(string $description): string
    {
        return match ($description) {
            'created' => 'heroicon-s-plus',
            'updated' => 'heroicon-s-pencil',
            'deleted' => 'heroicon-s-trash',
            default => 'heroicon-s-information-circle'
        };
    }

    public function getEventIconColor(string $description): string
    {
        return match ($description) {
            'created' => 'text-green-600',
            'updated' => 'text-blue-600',
            'deleted' => 'text-red-600',
            default => 'text-gray-600'
        };
    }

    public function getEventDescription($activity)
    {
        $subjectType = $this->getHumanReadableModelName($activity->subject_type);
        $description = $this->getHumanReadableAction($activity->description);
        $causer = $activity->causer ? ($activity->causer->name ?? $activity->causer->email ?? 'Unknown User') : null;

        $text = "<span>{$subjectType}</span> was <span>{$description}</span>";

        if ($causer) {
            $text .= " by <span class=\"font-medium text-blue-600\">{$causer}</span>";
        }

        return $text;
    }

    public function getHumanReadableModelName($modelClass)
    {
        $baseName = class_basename($modelClass);

        return match ($baseName) {
            'Order' => 'Order',
            'LineItem' => 'Line Item',
            'Shipment' => 'Shipment',
            'ShippingAddress' => 'Shipping Address',
            'BillingAddress' => 'Billing Address',
            default => str_replace('_', ' ', $baseName)
        };
    }

    public function getHumanReadableAction($action)
    {
        return match ($action) {
            'created' => 'created',
            'updated' => 'updated',
            'deleted' => 'deleted',
            'restored' => 'restored',
            default => $action
        };
    }

    public function formatValueCompact($value)
    {
        if ($value === null) {
            return '';
        }
        if ($value === '') {
            return '';
        }
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        // Handle arrays and objects - very compact
        if (is_array($value) || is_object($value)) {
            $json = json_encode($value);
            if (strlen($json) > self::COMPACT_JSON_LIMIT) {
                return substr($json, 0, self::COMPACT_JSON_LIMIT - 3).'...';
            }

            return $json;
        }

        // Handle long strings - more aggressive truncation for compact display
        $stringValue = (string) $value;
        if (strlen($stringValue) > self::COMPACT_STRING_LIMIT) {
            return substr($stringValue, 0, self::COMPACT_STRING_LIMIT - 3).'...';
        }

        return $stringValue;
    }

    public function render()
    {
        return view('livewire.activity-timeline');
    }
}
