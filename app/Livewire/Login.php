<?php

namespace App\Livewire;

use Filament\Actions\Action;
use Filament\Auth\Pages\Login as PagesLogin;
use Filament\Schemas\Schema;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\App;

class Login extends PagesLogin
{
    public function form(Schema $schema): Schema
    {
        if (App::isLocal()) {
            return parent::form($schema);
        }

        return $schema->schema([]);
    }

    public function getHeading(): string|Htmlable
    {
        return '';
    }

    protected function getFormActions(): array
    {
        if (App::isLocal()) {
            $actions = [
                ...parent::getFormActions(),
                Action::make('login-using-tda-passport')
                    ->label('Login with TDA Passport')
                    ->url(route('auth.login')),
            ];
        } else {
            $actions = [
                Action::make('login-using-tda-passport')
                    ->label('Login with TDA Passport')
                    ->url(route('auth.login')),
            ];
        }

        return $actions;
    }
}
