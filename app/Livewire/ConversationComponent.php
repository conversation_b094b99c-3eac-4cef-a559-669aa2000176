<?php

namespace App\Livewire;

use App\Actions\AddComment;
use App\Models\Comment;
use Illuminate\Database\Eloquent\Model;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;
use Livewire\Component;

class ConversationComponent extends Component
{
    public Model $record;

    #[Validate('required|string|min:3|max:1000')]
    public string $newComment = '';

    public function mount(Model $record)
    {
        $this->record = $record;
    }

    public function addComment()
    {
        $this->validate();

        AddComment::run(
            record: $this->record,
            content: $this->newComment,
            userId: user()->id
        );

        $this->newComment = '';

        // Emit event for real-time updates
        $this->dispatch('comment-added');

        // Show success message
        $this->dispatch('notify', [
            'message' => 'Comment added successfully!',
            'type' => 'success',
        ]);
    }

    public function deleteComment(Comment $comment)
    {
        // Check if user can delete this comment (only own comments for now)
        if ($comment->user_id !== user()->id) {
            $this->dispatch('notify', [
                'message' => 'You can only delete your own comments.',
                'type' => 'error',
            ]);

            return;
        }

        $comment->delete();

        $this->dispatch('comment-deleted');
        $this->dispatch('notify', [
            'message' => 'Comment deleted successfully!',
            'type' => 'success',
        ]);
    }

    #[On('comment-added', 'comment-deleted')]
    public function refreshComments()
    {
        // This will refresh the component when events are triggered
    }

    public function getCommentsProperty()
    {
        return $this->record->comments()->with('user')->get();
    }

    public function render()
    {
        return view('livewire.conversation-component', [
            'comments' => $this->comments,
        ]);
    }
}
