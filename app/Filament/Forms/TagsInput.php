<?php

namespace App\Filament\Forms;

use App\Models\Tag;
use Filament\Forms\Components\TagsInput as BaseTagsInput;

class TagsInput extends BaseTagsInput
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->suggestions(function () {
            return Tag::pluck('name')->toArray();
        });

        $this->afterStateHydrated(function (TagsInput $component, $state) {
            $record = $component->getRecord();

            // Only load existing tags in edit mode
            if ($record && $record->exists && method_exists($record, 'getTagNames')) {
                $component->state($record->getTagNames());
            }
        });

        $this->saveRelationshipsUsing(function (TagsInput $component, $state) {
            $record = $component->getRecord();

            if (! $record || ! method_exists($record, 'syncTags')) {
                return;
            }

            $record->syncTags($state ?? []);
        });

        $this->loadStateFromRelationshipsUsing(function (TagsInput $component, $state) {
            $record = $component->getRecord();

            if (! $record || ! $record->exists || ! method_exists($record, 'getTagNames')) {
                return $state;
            }

            return $record->getTagNames();
        });

        $this->dehydrated(false);
    }

    public static function make(?string $name = 'tags'): static
    {
        $static = app(static::class, ['name' => $name]);
        $static->configure();

        return $static;
    }
}
