<?php

namespace App\Filament\Backoffice\Resources\EtsyShops;

use App\Filament\Backoffice\Resources\EtsyShops\Pages\ListEtsyShops;
use App\Filament\Backoffice\Resources\EtsyShops\Schemas\EtsyShopForm;
use App\Filament\Backoffice\Resources\EtsyShops\Tables\EtsyShopsTable;
use App\Filament\TranslatableResource;
use App\Models\EtsyShop;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class EtsyShopResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = EtsyShop::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBuildingStorefront;

    protected static ?int $navigationSort = 50;

    // protected static bool $shouldRegisterNavigation = false;

    protected static string|UnitEnum|null $navigationGroup = 'System';

    public static function canViewAny(): bool
    {
        return user()->isManager();
    }

    public static function form(Schema $schema): Schema
    {
        return EtsyShopForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return EtsyShopsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListEtsyShops::route('/'),
        ];
    }
}
