<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Schemas;

use App\Actions\SplitTrackingNumbers;
use App\Enums\LineItemStatus;
use App\Enums\ShipmentStatus;
use App\Enums\TransportHandoverStatus;
use App\Models\Shipment;
use Filament\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Schemas\Schema;

class TransportHandoverForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        Select::make('third_party_logistic_id')
                            ->label('3PL Provider')
                            ->relationship('thirdPartyLogistic', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Select the third-party logistics provider for this handover'),
                        Select::make('status')
                            ->options(TransportHandoverStatus::class)
                            ->default(TransportHandoverStatus::Draft)
                            ->required()
                            ->disabled()
                            ->helperText('Status is automatically managed by the system'),
                        Group::make([
                            Textarea::make('tracking_numbers')
                                ->label('Tracking Numbers')
                                ->rows(7)
                                ->required()
                                ->helperText('Paste tracking codes here, one per line')
                                ->placeholder("ABC123456789\nDEF987654321\nGHI456789123")
                                ->lazy()
                                ->afterStateUpdated(fn (Get $get, Set $set) => static::validateTrackingNumbers($get, $set))
                                ->afterLabel([
                                    Action::make('validate-tracking-numbers')
                                        ->button()
                                        ->outlined()
                                        ->action(fn (Get $get, Set $set) => static::validateTrackingNumbers($get, $set)),
                                    Action::make('remove-invalid-tracking-numbers')
                                        ->label('Remove Invalid Tracking Numbers')
                                        ->link()
                                        ->color('warning')
                                        ->visible(fn (Get $get): bool => ! empty($get('invalid_tracking_numbers')))
                                        ->action(fn (Get $get, Set $set) => static::removeInvalidTrackingNumbers($get, $set)),
                                ]),
                            Textarea::make('validation_results')
                                ->disabled()
                                ->dehydrated(false)
                                ->rows(7)
                                ->extraInputAttributes(['style' => 'color: red; -webkit-text-fill-color: red;']),
                            Textarea::make('warning_results')
                                ->label('Warnings')
                                ->disabled()
                                ->dehydrated(false)
                                ->rows(4)
                                ->visible(fn (Get $get): bool => ! empty($get('warning_tracking_numbers')))
                                ->extraInputAttributes(['style' => 'color: orange; -webkit-text-fill-color: orange;']),
                            Hidden::make('invalid_tracking_numbers'),
                            Hidden::make('warning_tracking_numbers'),
                        ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    protected static function validateTrackingNumbers(Get $get, Set $set): void
    {
        $trackingNumbers = $get('tracking_numbers');

        if (empty($trackingNumbers)) {
            return;
        }

        // Parse tracking codes using the action
        $codes = SplitTrackingNumbers::make()->handle($trackingNumbers);

        // Validate each tracking number and collect detailed results
        $validationResults = [];
        $warningResults = [];
        $invalidCodes = [];
        $warningCodes = [];
        $validCodes = [];

        foreach ($codes as $code) {
            $shipment = Shipment::query()->where('tracking_number', $code)
                ->with(['lineItems.order'])
                ->first();

            if (! $shipment) {
                $validationResults[] = "{$code} - ".__('transport_handover.validation.not_found');
                $invalidCodes[] = $code;
            } elseif ($shipment->status !== ShipmentStatus::Pending) {
                $statusLabel = match ($shipment->status) {
                    ShipmentStatus::PickedUp => __('transport_handover.status.already_picked_up'),
                    ShipmentStatus::InTransit => __('transport_handover.status.in_transit'),
                    ShipmentStatus::OutForDelivery => __('transport_handover.status.out_for_delivery'),
                    ShipmentStatus::Delivered => __('transport_handover.status.delivered'),
                    ShipmentStatus::Failed => __('transport_handover.status.failed'),
                    ShipmentStatus::Returned => __('transport_handover.status.returned'),
                    ShipmentStatus::Cancelled => __('transport_handover.status.cancelled'),
                    default => __('transport_handover.status.invalid_status')
                };
                $validationResults[] = "{$code} - {$statusLabel} (".__('transport_handover.validation.only_pending_allowed').')';
                $invalidCodes[] = $code;
            } elseif ($shipment->third_party_logistic_id !== null) {
                $validationResults[] = "{$code} - ".__('transport_handover.validation.already_has_3pl');
                $invalidCodes[] = $code;
            } else {
                // Check line item statuses for cancelled items
                $lineItems = $shipment->lineItems;
                $cancelledLineItems = $lineItems->where('status', LineItemStatus::Cancelled);

                // Group by orders to count orders with cancelled items
                $allOrders = $lineItems->pluck('order')->unique('id');
                $ordersWithCancelledItems = $cancelledLineItems->pluck('order')->unique('id');
                $totalOrders = $allOrders->count();
                $cancelledOrdersCount = $ordersWithCancelledItems->count();

                if ($cancelledOrdersCount === $totalOrders && $totalOrders > 0) {
                    // All orders have cancelled line items - mark as invalid
                    $orderNumbers = $ordersWithCancelledItems->pluck('number_portal')->implode(', ');
                    $validationResults[] = "{$code} - ".__('transport_handover.validation.all_orders_cancelled', ['orders' => $orderNumbers]);
                    $invalidCodes[] = $code;
                } elseif ($cancelledOrdersCount > 0) {
                    // Some orders have cancelled line items - add to warnings
                    $orderNumbers = $ordersWithCancelledItems->pluck('number_portal')->implode(', ');
                    $warningResults[] = "{$code} - ".__('transport_handover.validation.some_orders_cancelled', [
                        'cancelled_count' => $cancelledOrdersCount,
                        'total_count' => $totalOrders,
                        'orders' => $orderNumbers,
                    ]);
                    $warningCodes[] = $code;
                    $validCodes[] = $code;
                } else {
                    $validCodes[] = $code;
                }
            }
        }

        // Set validation results
        if (count($invalidCodes) > 0) {
            $set('validation_results', implode("\n", $validationResults));
            $set('invalid_tracking_numbers', $invalidCodes);
        } else {
            $set('validation_results', '');
            $set('invalid_tracking_numbers', []);
        }

        // Set warning results
        if (count($warningCodes) > 0) {
            $set('warning_results', implode("\n", $warningResults));
            $set('warning_tracking_numbers', $warningCodes);
        } else {
            $set('warning_results', '');
            $set('warning_tracking_numbers', []);
        }

        // Show notification based on results
        if (count($invalidCodes) === 0 && count($warningCodes) === 0) {
            Notification::make()
                ->title(__('transport_handover.notifications.all_valid_title'))
                ->body(__('transport_handover.notifications.all_valid_body', ['count' => count($validCodes)]))
                ->success()
                ->send();
        } elseif (count($invalidCodes) === 0 && count($warningCodes) > 0) {
            Notification::make()
                ->title(__('transport_handover.notifications.validation_warnings_title'))
                ->body(__('transport_handover.notifications.validation_warnings_body', [
                    'valid_count' => count($validCodes),
                    'warning_count' => count($warningCodes),
                ]))
                ->warning()
                ->send();
        }
    }

    protected static function removeInvalidTrackingNumbers(Get $get, Set $set): void
    {
        $trackingNumbers = $get('tracking_numbers');
        $invalidCodes = $get('invalid_tracking_numbers') ?? [];

        if (empty($trackingNumbers) || empty($invalidCodes)) {
            return;
        }

        // Parse current tracking numbers
        $allCodes = SplitTrackingNumbers::make()->handle($trackingNumbers);

        // Remove invalid codes
        $validCodes = array_diff($allCodes, $invalidCodes);

        // Update the tracking numbers field
        $set('tracking_numbers', implode("\n", $validCodes));

        // Clear validation and warning results
        $set('validation_results', '');
        $set('invalid_tracking_numbers', []);
        $set('warning_results', '');
        $set('warning_tracking_numbers', []);

        Notification::make()
            ->title(__('transport_handover.notifications.invalid_removed_title'))
            ->body(__('transport_handover.notifications.invalid_removed_body', [
                'removed_count' => count($invalidCodes),
                'remaining_count' => count($validCodes),
            ]))
            ->success()
            ->send();
    }
}
