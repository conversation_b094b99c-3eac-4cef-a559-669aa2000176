<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;
use Illuminate\Support\HtmlString;

class TransportHandoverInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('thirdPartyLogistic.name')
                    ->numeric(),
                TextEntry::make('status')
                    ->badge(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('user.name')
                    ->label('Created by'),
                TextEntry::make('tracking_numbers')
                    ->formatStateUsing(fn (?string $state) => new HtmlString(nl2br($state))),
            ]);
    }
}
