<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers;

use App\Filament\Backoffice\Resources\TransportHandovers\Pages\CreateTransportHandover;
use App\Filament\Backoffice\Resources\TransportHandovers\Pages\EditTransportHandover;
use App\Filament\Backoffice\Resources\TransportHandovers\Pages\ListTransportHandovers;
use App\Filament\Backoffice\Resources\TransportHandovers\Pages\ViewTransportHandover;
use App\Filament\Backoffice\Resources\TransportHandovers\Schemas\TransportHandoverForm;
use App\Filament\Backoffice\Resources\TransportHandovers\Schemas\TransportHandoverInfolist;
use App\Filament\Backoffice\Resources\TransportHandovers\Tables\TransportHandoversTable;
use App\Filament\TranslatableResource;
use App\Models\TransportHandover;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class TransportHandoverResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = TransportHandover::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedTruck;

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?int $navigationSort = 4;

    public static function form(Schema $schema): Schema
    {
        return TransportHandoverForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return TransportHandoverInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return TransportHandoversTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTransportHandovers::route('/'),
            'create' => CreateTransportHandover::route('/create'),
            'view' => ViewTransportHandover::route('/{record}'),
            'edit' => EditTransportHandover::route('/{record}/edit'),
        ];
    }
}
