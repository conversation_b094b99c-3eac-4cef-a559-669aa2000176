<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Concerns;

use App\Actions\SplitTrackingNumbers;

trait CleansUpTrackingNumberData
{
    /**
     * Clean up tracking number data before saving to database
     */
    protected function cleanUpTrackingNumberData(array $data): array
    {
        // Clean up invalid tracking numbers from the tracking_numbers field if they exist
        if (isset($data['invalid_tracking_numbers']) && ! blank($data['invalid_tracking_numbers']) && isset($data['tracking_numbers'])) {
            $invalidNumbers = is_array($data['invalid_tracking_numbers'])
                ? $data['invalid_tracking_numbers']
                : [$data['invalid_tracking_numbers']];

            // Parse current tracking numbers
            $allNumbers = SplitTrackingNumbers::make()->handle($data['tracking_numbers']);

            // Remove invalid numbers
            $validNumbers = array_diff($allNumbers, $invalidNumbers);

            // Update tracking_numbers with only valid numbers
            $data['tracking_numbers'] = implode("\n", $validNumbers);
        }

        // Remove validation-only fields that shouldn't be persisted to database
        unset($data['invalid_tracking_numbers']);
        unset($data['validation_results']);
        unset($data['warning_tracking_numbers']);
        unset($data['warning_results']);

        return $data;
    }
}
