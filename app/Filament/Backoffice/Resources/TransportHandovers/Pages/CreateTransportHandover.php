<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Pages;

use App\Filament\Backoffice\Resources\TransportHandovers\Concerns\CleansUpTrackingNumberData;
use App\Filament\Backoffice\Resources\TransportHandovers\TransportHandoverResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTransportHandover extends CreateRecord
{
    use CleansUpTrackingNumberData;

    protected static string $resource = TransportHandoverResource::class;

    public function getTitle(): string
    {
        return 'Create Transport Handover';
    }

    public function getHeading(): string
    {
        return 'Create Transport Handover';
    }

    public function getSubheading(): ?string
    {
        return 'Create handover documents for 3PL pickup';
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Clean up tracking number data using the trait
        $data = $this->cleanUpTrackingNumberData($data);

        // Set the current user as the creator
        $data['user_id'] = user()->id;

        return $data;
    }
}
