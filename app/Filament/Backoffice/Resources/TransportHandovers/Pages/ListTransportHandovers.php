<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Pages;

use App\Filament\Backoffice\Resources\TransportHandovers\TransportHandoverResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListTransportHandovers extends ListRecords
{
    protected static string $resource = TransportHandoverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Tạo phiếu bàn giao'),
        ];
    }
}
