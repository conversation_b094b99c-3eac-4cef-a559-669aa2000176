<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Pages;

use App\Filament\Backoffice\Resources\TransportHandovers\Concerns\CleansUpTrackingNumberData;
use App\Filament\Backoffice\Resources\TransportHandovers\TransportHandoverResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditTransportHandover extends EditRecord
{
    use CleansUpTrackingNumberData;

    protected static string $resource = TransportHandoverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Clean up tracking number data using the trait
        return $this->cleanUpTrackingNumberData($data);
    }
}
