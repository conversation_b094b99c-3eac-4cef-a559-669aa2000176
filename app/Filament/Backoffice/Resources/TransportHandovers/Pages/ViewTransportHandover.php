<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Pages;

use App\Actions\ConfirmTransportHandover;
use App\Filament\Backoffice\Resources\TransportHandovers\TransportHandoverResource;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewTransportHandover extends ViewRecord
{
    protected static string $resource = TransportHandoverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make()->visible(fn () => $this->getRecord()->isDraft()),
            Action::make('confirm')
                ->label(__('Confirm'))
                ->icon('heroicon-o-check-circle')
                ->requiresConfirmation()
                ->visible(fn () => $this->getRecord()->isDraft())
                ->authorize('edit', $this->getRecord())
                ->action(function () {
                    ConfirmTransportHandover::make()->handle($this->getRecord());

                    $this->refreshFormData([
                        'status',
                    ]);
                }),
            Action::make('print')
                ->label(__('Print'))
                ->icon('heroicon-o-printer')
                ->color('gray')
                ->visible(fn () => $this->getRecord()->isConfirmed())
                ->url(fn () => route('transport-handover.print', $this->getRecord()))
                ->openUrlInNewTab(),
        ];
    }
}
