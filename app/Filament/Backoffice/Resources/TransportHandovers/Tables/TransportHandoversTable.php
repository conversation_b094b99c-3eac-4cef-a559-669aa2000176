<?php

namespace App\Filament\Backoffice\Resources\TransportHandovers\Tables;

use App\Models\TransportHandover;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TransportHandoversTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['thirdPartyLogistic']))
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('thirdPartyLogistic.name')
                    ->label('3PL Provider'),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('tracking_numbers_count')
                    ->label('Tracking numbers count')
                    ->state(fn (TransportHandover $record) => count(explode("\n", $record->tracking_numbers)))
                    ->badge()
                    ->alignRight(),
                TextColumn::make('user.name')
                    ->label('User'),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'confirmed' => 'Confirmed',
                    ]),
                SelectFilter::make('third_party_logistic_id')
                    ->label('3PL Provider')
                    ->relationship('thirdPartyLogistic', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
