<?php

namespace App\Filament\Backoffice\Resources\QcSessions\Schemas;

use App\Filament\Backoffice\Resources\QcSessions\Pages\ViewQcSession;
use App\Filament\Backoffice\Resources\QcSessions\QcFormWizard;
use App\Models\QcSession;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Flex;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Livewire;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

/**
 * @mixin ViewQcSession
 */
class QcSessionInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema

            ->schema([
                Flex::make([
                    Group::make()
                        ->schema(fn (QcSession $record) => [Livewire::make(QcFormWizard::class, ['qcSession' => $record])])
                        ->visible(fn (QcSession $record) => $record->isProcessing()),
                    static::metaSection()
                        ->visible(fn (QcSession $record) => $record->isCompleted()),
                ])->columnSpanFull(),
            ]);
    }

    protected static function metaSection()
    {
        return Section::make()
            ->heading(__('Information'))
            ->columns(2)
            ->schema([
                TextEntry::make('user.name')
                    ->label('Operator'),
                TextEntry::make('status')
                    ->badge(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('started_at')
                    ->dateTime(),
                TextEntry::make('completed_at')
                    ->dateTime(),
            ]);
    }
}
