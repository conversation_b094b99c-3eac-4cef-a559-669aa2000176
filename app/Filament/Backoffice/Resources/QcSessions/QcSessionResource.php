<?php

namespace App\Filament\Backoffice\Resources\QcSessions;

use App\Filament\Backoffice\Resources\QcSessions\Pages\ListQcSessions;
use App\Filament\Backoffice\Resources\QcSessions\Pages\ViewQcSession;
use App\Filament\Backoffice\Resources\QcSessions\Schemas\QcSessionInfolist;
use App\Filament\Backoffice\Resources\QcSessions\Tables\QcSessionsTable;
use App\Filament\TranslatableResource;
use App\Models\QcSession;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use UnitEnum;

class QcSessionResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = QcSession::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?string $modelLabel = 'QC Session';

    protected static ?int $navigationSort = 1;

    public static function infolist(Schema $schema): Schema
    {
        return QcSessionInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return QcSessionsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderChecksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListQcSessions::route('/'),
            'view' => ViewQcSession::route('/{record}'),
        ];
    }
}
