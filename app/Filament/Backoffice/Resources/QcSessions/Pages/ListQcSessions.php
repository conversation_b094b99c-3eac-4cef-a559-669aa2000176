<?php

namespace App\Filament\Backoffice\Resources\QcSessions\Pages;

use App\Actions\StartQcSession;
use App\Filament\Backoffice\Resources\QcSessions\QcSessionResource;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Exceptions\Halt;

class ListQcSessions extends ListRecords
{
    protected static string $resource = QcSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('start-new-qc-session')
                ->label(__('Start New QC Session'))
                ->icon('heroicon-o-play')
                ->action(function () {
                    if (user()->hasActiveQcSession()) {
                        Notification::make()->title(__('You already have an active QC session. Please finish or cancel it before starting a new one.'))
                            ->danger()
                            ->send();
                        throw new Halt;
                    }
                    $record = StartQcSession::make()->handle(user());

                    $this->redirect(route('filament.backoffice.resources.qc-sessions.view', $record));
                }),
        ];
    }
}
