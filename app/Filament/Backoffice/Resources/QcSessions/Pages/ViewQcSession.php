<?php

namespace App\Filament\Backoffice\Resources\QcSessions\Pages;

use App\Actions\FinishQcSession;
use App\Actions\UpdateLineItemStatus;
use App\Enums\QcLineItemStatus;
use App\Filament\Backoffice\Resources\QcSessions\QcSessionResource;
use App\Models\QcLineItemCheck;
use App\Models\QcSession;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\Storage;

class ViewQcSession extends ViewRecord
{
    protected static string $resource = QcSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('finishQc')
                ->label(__('Finish QC Session'))
                ->icon('heroicon-o-check-circle')
                ->visible(fn () => $this->getRecord()->isProcessing())
                ->requiresConfirmation()
                ->modalHeading(__('Finish QC Session'))
                ->modalDescription(__('Are you sure you want to finish this QC session? This action cannot be undone.'))
                ->action(function () {
                    try {
                        FinishQcSession::make()->handle($this->getRecord());

                        Notification::make()
                            ->title(__('QC session completed successfully'))
                            ->success()
                            ->send();

                        $this->redirect(static::getResource()::getUrl('view', ['record' => $this->getRecord()]));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title(__('Error finishing QC session'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
            Action::make('export-orders')
                ->label(__('Export orders'))
                ->icon(Heroicon::OutlinedArrowDownTray)
                ->color('gray')
                ->visible(fn (QcSession $record) => $record->isCompleted())
                ->action(function (QcSession $record) {
                    // Generate the CSV file and get the file path
                    $filePath = \App\Actions\ExportOrdersFromQcSession::make()->handle($record);

                    // Return download response
                    return response()->download(
                        Storage::disk('public')->path($filePath),
                        basename($filePath),
                        [
                            'Content-Type' => 'text/csv',
                        ]
                    );
                }),
            Action::make('export-packing-slip')
                ->label(__('Export Packing Slip'))
                ->icon(Heroicon::OutlinedDocumentText)
                ->color('gray')
                ->visible(fn (QcSession $record) => $record->isCompleted())
                ->action(function (QcSession $record) {
                    // Generate the CSV file and get the file path
                    $filePath = \App\Actions\ExportPackingSlipFromQcSession::make()->handle($record);

                    // Return download response
                    return response()->download(
                        Storage::disk('public')->path($filePath),
                        basename($filePath),
                        [
                            'Content-Type' => 'text/csv',
                        ]
                    );
                }),
        ];
    }

    public function updateLineItemStatus(int $lineItemCheckId, string $status): void
    {
        try {
            $lineItemCheck = QcLineItemCheck::findOrFail($lineItemCheckId);
            $qcStatus = QcLineItemStatus::from($status);

            UpdateLineItemStatus::make()->handle($lineItemCheck, $qcStatus);

            Notification::make()
                ->title(__('Line item status updated successfully'))
                ->success()
                ->send();

            // Refresh the current page
            $this->redirect(static::getResource()::getUrl('view', ['record' => $this->getRecord()]));
        } catch (\Exception $e) {
            Notification::make()
                ->title(__('Error updating line item status'))
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
