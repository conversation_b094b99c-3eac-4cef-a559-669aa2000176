<?php

namespace App\Filament\Backoffice\Resources\QcSessions\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LineItemChecksRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItemChecks';

    protected static ?string $title = 'Line Item QC Status';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('lineItem.product_image_url')
                    ->label('Image')
                    ->size(60)
                    ->defaultImageUrl('/images/placeholder.png'),
                TextColumn::make('lineItem.product_name')
                    ->label('Product Name')
                    ->limit(50)
                    ->searchable(),
                TextColumn::make('lineItem.sku')
                    ->label('SKU')
                    ->searchable(),
                TextColumn::make('lineItem.quantity')
                    ->label('Quantity')
                    ->numeric(),
                TextColumn::make('status')
                    ->badge()
                    ->sortable(),
                TextColumn::make('checked_at')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not checked'),
            ])
            ->defaultSort('created_at', 'asc')
            ->recordActions([
                // We'll add actions for updating QC status
            ]);
    }
}
