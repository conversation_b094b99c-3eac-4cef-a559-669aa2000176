<?php

namespace App\Filament\Backoffice\Resources\QcSessions\RelationManagers;

use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OrderChecksRelationManager extends RelationManager
{
    protected static string $relationship = 'orderChecks';

    protected function getTableHeading(): string
    {
        $count = $this->getOwnerRecord()->orderChecks()->count();

        return "Orders in QC Session ($count)";
    }

    protected $listeners = ['reloadRelations' => '$refresh'];

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->modifyQueryUsing(fn ($query) => $query->withLineItemCheckCounts()->with(['qcSession']))
            ->columns([
                TextColumn::make('order.number_portal')
                    ->label('TDA Number')
                    ->copyable()
                    ->badge()
                    ->url(fn ($record) => route('filament.backoffice.resources.orders.view', $record->order))
                    ->openUrlInNewTab(),
                TextColumn::make('order.order_number')
                    ->label('Order Number')
                    ->copyable()
                    ->badge(),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('line_item_checks_count')
                    ->label('Line Items'),
                TextColumn::make('missing_items_count')
                    ->label('Missing Items')
                    ->color('danger'),
                TextColumn::make('ready_items_count')
                    ->label('Ready Items')
                    ->color('success'),
                TextColumn::make('confirmed_at')
                    ->dateTime(),
                TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordActions([
                DeleteAction::make(),
                ViewAction::make()
                    ->url(fn ($record) => route('filament.backoffice.resources.orders.view', $record->order))
                    ->openUrlInNewTab(),
            ]);
    }
}
