<?php

namespace App\Filament\Backoffice\Resources\QcSessions\Tables;

use Carbon\CarbonInterface;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class QcSessionsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn (Builder $query) => $query->when(! user()->isManager(), fn (Builder $query) => $query->where('user_id', user()->id))
            )
            ->columns([
                TextColumn::make('id')
                    ->sortable(),
                TextColumn::make('user.name')
                    ->label('User')
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->sortable(),
                TextColumn::make('order_checks_count')
                    ->label('Orders count')
                    ->counts('orderChecks')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime(),
                TextColumn::make('started_at')
                    ->dateTime(),
                TextColumn::make('completed_at')
                    ->dateTime(),
                TextColumn::make('duration')
                    ->label('Duration')
                    ->state(function ($record) {
                        if (! $record->started_at || ! $record->completed_at) {
                            return null;
                        }

                        return $record->completed_at->diffForHumans($record->started_at, CarbonInterface::DIFF_ABSOLUTE);
                    })
                    ->formatStateUsing(function ($state) {
                        return $state;
                    })
                    ->alignRight(),
            ])
            ->defaultSort('id', 'desc')
            ->recordActions([
                ViewAction::make(),
            ]);
    }
}
