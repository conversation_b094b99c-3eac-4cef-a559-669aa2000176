<?php

namespace App\Filament\Backoffice\Resources\QcSessions;

use App\Actions\AddOrderToQc;
use App\Actions\ConfirmOrderQc;
use App\Actions\RemoveOrderFromQc;
use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\QcLineItemStatus;
use App\Enums\QcOrderCheckStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\QcOrderCheck;
use App\Models\QcSession;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Actions;
use Filament\Schemas\Components\Flex;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Schema;
use Filament\Support\Enums\Alignment;
use Filament\Support\Icons\Heroicon;
use Livewire\Component;

/**
 * @property Schema $searchForm
 * @property Schema $qualityCheckForm
 */
class QcFormWizard extends Component implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithSchemas;

    public ?array $searchData = [
        'number_portal' => '',
        'processing' => false,
    ];

    public ?array $qualityCheckData = [];

    public ?QcSession $qcSession = null;

    public function mount(?QcSession $qcSession = null): void
    {
        $this->qcSession = $qcSession;

        if ($orderCheck = $this->qcSession->orderChecks()->processing()->with('lineItemChecks.lineItem')->first()) {
            $this->searchForm->fill([
                'number_portal' => $orderCheck->order->number_portal,
                'processing' => true,
            ]);

            $this->qualityCheckForm->fill([
                'order_check_id' => $orderCheck->id,
                'line_item_checks' => $this->makeLineItemChecks($orderCheck),
                'line_item_in_new_checks' => $this->makeNewLineItemChecks($orderCheck),
            ]);
        }
    }

    protected function makeNewLineItemChecks(QcOrderCheck $orderCheck): array
    {
        return $orderCheck->order->lineItems
            ->filter(fn (LineItem $lineItem) => $lineItem->displayItemsInNew())
            ->map(function (LineItem $line, $i) use ($orderCheck) {
                $line->setRelation('order', $orderCheck->order);

                return [
                    'line_item_id' => $line->id,
                    'index' => $i + 1,
                    'product_name' => $line->product_name,
                    'product_url' => $line->productUrl(),
                    'product_image_url' => $line->product_image_url,
                    'quantity' => $line->quantity,
                    'attributes' => $line->attributesString(),
                    'size' => $line->size,
                    'color' => $line->color,
                    'personalization' => $line->personalization,
                    'seller_note' => $line->seller_note,
                    'notes' => $line->compositeNotes(),
                    'status' => null,
                ];
            })->toArray();
    }

    protected function makeLineItemChecks(QcOrderCheck $orderCheck): array
    {
        return $orderCheck->order->lineItems
            ->filter(fn (LineItem $lineItem) => $lineItem->canBeQualityControlled())
            ->map(function (LineItem $line, $i) use ($orderCheck) {
                $line->setRelation('order', $orderCheck->order);

                return [
                    'line_item_id' => $line->id,
                    'index' => $i + 1,
                    'product_name' => $line->product_name,
                    'product_url' => $line->productUrl(),
                    'product_image_url' => $line->product_image_url,
                    'quantity' => $line->quantity,
                    'product_type' => $line->product_type,
                    'attributes' => $line->attributesString(),
                    'size' => $line->size,
                    'color' => $line->color,
                    'personalization' => $line->personalization,
                    'seller_note' => $line->seller_note,
                    'notes' => $line->compositeNotes(),
                    'status' => null,
                ];
            })->toArray();
    }

    public function searchForm(Schema $schema): Schema
    {
        return $schema
            ->statePath('searchData')
            ->schema([
                Hidden::make('processing')
                    ->default(false),
                Section::make()
                    ->heading(__('Search order'))
                    ->schema([
                        Flex::make([
                            Select::make('number_portal')
                                ->label('Order TDA number')
                                ->hiddenLabel()
                                ->placeholder(__('Enter order TDA number...'))
                                ->required()
                                ->searchable()
                                ->getSearchResultsUsing(function ($search) {
                                    return Order::query()
                                        ->where('number_portal', 'like', "%{$search}")
                                        ->whereIn('status', [OrderStatus::Processing, OrderStatus::PartialShipped])
                                        ->orderBy('id', 'desc')
                                        ->limit(10)
                                        ->get()
                                        ->pluck('number_portal', 'number_portal');
                                })
                                ->getOptionLabelUsing(fn ($value) => $value)
                                ->afterStateUpdated(fn () => $this->searchOrder())
                                ->live()
                                ->disabled(fn (Get $get) => $get('processing'))
                                ->extraAttributes([
                                    'style' => 'user-select: text;',
                                ]),
                            Action::make('search')
                                ->label(__('Search'))
                                ->icon(Heroicon::OutlinedMagnifyingGlass)
                                ->disabled(fn (Get $get) => $get('processing'))
                                ->action($this->searchOrder(...)),
                        ]),
                    ]),
            ]);
    }

    public function qualityCheckForm(Schema $schema): Schema
    {
        return $schema
            ->statePath('qualityCheckData')
            ->schema([
                Hidden::make('order_check_id'),

                Section::make(__(''))
                    ->schema([
                        Flex::make([
                            TextEntry::make('number_portal')
                                ->label(__('TDA Number'))->copyable()->badge()
                                ->state(fn (Get $get) => optional(QcOrderCheck::find($get('order_check_id'))?->order)->number_portal ?? __('N/A')),
                            TextEntry::make('order_number')
                                ->label(__('Order Number'))->copyable()->badge()
                                ->state(fn (Get $get) => optional(QcOrderCheck::find($get('order_check_id'))?->order)->order_number ?? __('N/A')),
                            TextEntry::make('etsy_shop_name')
                                ->label(__('Shop Name'))->badge()
                                ->state(fn (Get $get) => optional(QcOrderCheck::find($get('order_check_id'))?->order->etsyShop)->username ?? __('N/A')),
                        ])->gap(2),
                    ])
                    ->visible(fn (Get $get) => ! blank($get('order_check_id'))),

                Section::make()
                    ->heading(__('Line Items Quality Check'))
                    ->visible(fn (Get $get) => ! blank($get('order_check_id')) && ! blank($get('line_item_checks')))
                    ->schema([
                        Repeater::make('line_item_checks')
                            ->hiddenLabel()
                            ->addable(false)
                            ->reorderable(false)
                            ->deletable(false)
                            ->table([
                                Repeater\TableColumn::make('Index')
                                    ->hiddenHeaderLabel(),
                                Repeater\TableColumn::make('Image')
                                    ->width(180)
                                    ->alignCenter(),
                                Repeater\TableColumn::make('Name'),
                                Repeater\TableColumn::make('Url'),
                                Repeater\TableColumn::make('Quantity')
                                    ->alignRight(),
                                Repeater\TableColumn::make('Product Type'),
                                Repeater\TableColumn::make('Size'),
                                Repeater\TableColumn::make('Color'),
                                Repeater\TableColumn::make('Personalization'),
                                Repeater\TableColumn::make('Attributes'),
                                Repeater\TableColumn::make('Seller notes'),
                                Repeater\TableColumn::make('Notes'),
                                Repeater\TableColumn::make('State')
                                    ->alignment(Alignment::Center),
                            ])
                            ->schema([
                                Hidden::make('line_item_id'),
                                TextEntry::make('index')
                                    ->grow(false)
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                ImageEntry::make('product_image_url')
                                    ->hiddenLabel()
                                    ->alignCenter()
                                    ->state(fn ($rawState) => $rawState)
                                    ->width(175)
                                    ->height(175),
                                TextEntry::make('product_name')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('product_url')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState)
                                    ->url(fn ($rawState) => $rawState)
                                    ->wrap()
                                    ->openUrlInNewTab(),
                                TextEntry::make('quantity')
                                    ->hiddenLabel()
                                    ->alignRight()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('product_type')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('size')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('color')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('personalization')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('attributes')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('seller_note')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('note')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                ToggleButtons::make('status')
                                    ->options(QcLineItemStatus::class)
                                    ->inline()
                                    ->required()
                                    ->extraAttributes([
                                        'style' => 'justify-content: center;',
                                        'class' => 'qc-status-buttons',
                                    ]),
                            ]),

                        // Actions
                        Actions::make([
                            Action::make('confirm-check-order')
                                ->label(__('Confirm'))
                                ->action($this->confirmOrder(...)),

                            Action::make('cancel-check-order')
                                ->label(__('Cancel'))
                                ->color('gray')
                                ->action($this->cancelOrder(...)),
                        ])->alignRight(),
                    ]),

                Section::make()
                    ->heading(__(''))
                    ->visible(fn (Get $get) => ! blank($get('order_check_id')) && ! blank($get('line_item_in_new_checks')))
                    ->schema([
                        Repeater::make('line_item_in_new_checks')
                            ->hiddenLabel()
                            ->addable(false)
                            ->reorderable(false)
                            ->deletable(false)
                            ->table([
                                Repeater\TableColumn::make('Index')
                                    ->hiddenHeaderLabel(),
                                Repeater\TableColumn::make('Image')
                                    ->width(180)
                                    ->alignCenter(),
                                Repeater\TableColumn::make('Name'),
                                Repeater\TableColumn::make('Url'),
                                Repeater\TableColumn::make('Quantity')
                                    ->alignRight(),
                                Repeater\TableColumn::make('Size'),
                                Repeater\TableColumn::make('Color'),
                                Repeater\TableColumn::make('Personalization'),
                                Repeater\TableColumn::make('Attributes'),
                                Repeater\TableColumn::make('Seller notes'),
                                Repeater\TableColumn::make('Notes'),
                                Repeater\TableColumn::make('State')
                                    ->alignment(Alignment::Center),
                            ])
                            ->schema([
                                Hidden::make('line_item_id'),
                                TextEntry::make('index')
                                    ->grow(false)
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                ImageEntry::make('product_image_url')
                                    ->hiddenLabel()
                                    ->alignCenter()
                                    ->state(fn ($rawState) => $rawState)
                                    ->width(175)
                                    ->height(175),
                                TextEntry::make('product_name')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('product_url')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState)
                                    ->url(fn ($rawState) => $rawState)
                                    ->wrap()
                                    ->openUrlInNewTab(),
                                TextEntry::make('quantity')
                                    ->hiddenLabel()
                                    ->alignRight()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('size')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('color')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('personalization')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('attributes')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('seller_note')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('note')
                                    ->hiddenLabel()
                                    ->state(fn ($rawState) => $rawState),
                                TextEntry::make('status')
                                    ->hiddenLabel()
                                    ->state('Item In New'),
                            ]),
                    ]),
            ]);
    }

    public function searchOrder(): void
    {
        $state = $this->searchForm->getState();
        $numberPortal = $state['number_portal'];

        if (empty($numberPortal)) {
            Notification::make()
                ->title(__('Please enter an order number'))
                ->warning()
                ->send();

            return;
        }

        /** @var Order $order */
        $order = Order::where('number_portal', $numberPortal)->first();

        if (! $order) {
            Notification::make()
                ->title(__('Order not found'))
                ->danger()
                ->send();

            return;
        }

        if (! ($order->isProcessing() || $order->isPartiallyShipped())) {
            Notification::make()
                ->title(__('Order cannot be quality controlled'))
                ->body(__('Only orders in processing or partially shipped status can be quality controlled.'))
                ->danger()
                ->send();

            return;
        }

        // Check if order has line items in production status
        $inProductionCount = $order->lineItems()->where('status', LineItemStatus::InProduction)->count();
        if ($inProductionCount === 0) {
            Notification::make()
                ->title(__('Order has no line items in production status'))
                ->body(__('Only orders with line items in production can be quality controlled.'))
                ->danger()
                ->send();

            return;
        }

        if ($order->qcOrderCheck && $order->qcOrderCheck->isProcessing() && $order->qcOrderCheck->qcSession->user_id !== $this->qcSession->user_id) {
            Notification::make()
                ->title(__('Order is already being QC\'d by another user.'))
                ->danger()
                ->send();

            return;
        }

        if ($order->qcOrderCheck && ! $order->hasInProductionLineItems()) {
            Notification::make()
                ->title(__('Order has already been QC\'d.'))
                ->danger()
                ->send();

            return;
        }

        // Check if order has already been QC'd in this session
        $existingOrderCheck = $this->qcSession->orderChecks()
            ->where('order_id', $order->id)
            ->where('status', QcOrderCheckStatus::Completed)
            ->first();

        if ($existingOrderCheck) {
            Notification::make()
                ->title(__('Order already QC\'d in this session'))
                ->body(__('This order has already been quality controlled in the current session.'))
                ->warning()
                ->send();

            return;
        }

        // Check if order has a pending cancel request
        if ($order->hasPendingCancelRequest()) {
            Notification::make()
                ->title(__('Order has a pending cancel request'))
                ->body(__('Orders with pending cancel requests cannot be quality controlled.'))
                ->danger()
                ->send();

            return;
        }

        $orderCheck = AddOrderToQc::make()->handle($this->qcSession, $order);
        $orderCheck->load('lineItemChecks.lineItem');

        $this->searchForm->fill([
            'number_portal' => $numberPortal,
            'processing' => true,
        ]);

        $this->qualityCheckForm->fill([
            'order_check_id' => $orderCheck->id,
            'line_item_checks' => $this->makeLineItemChecks($orderCheck),
            'line_item_in_new_checks' => $this->makeNewLineItemChecks($orderCheck),
        ]);

        Notification::make()
            ->title(__('Order found and added to QC session'))
            ->success()
            ->send();
    }

    public function confirmOrder($skipConfirmation = false): void
    {
        $qualityCheckState = $this->qualityCheckForm->getState();

        // Validate that we have an order check ID
        if (empty($qualityCheckState['order_check_id'])) {
            Notification::make()
                ->title(__('No order selected for QC'))
                ->danger()
                ->send();

            return;
        }

        $lineItemChecks = collect($qualityCheckState['line_item_checks']);
        $missingItems = $lineItemChecks->filter(fn (array $item) => $item['status'] === QcLineItemStatus::Missing);
        $hasMissingItems = $missingItems->isNotEmpty();
        $allItemsMissing = $lineItemChecks->isNotEmpty() && $missingItems->count() === $lineItemChecks->count();

        // Check if all items are missing - this should block confirmation
        if ($allItemsMissing) {
            Notification::make()
                ->title(__('Cannot confirm order'))
                ->body(__('All items are missing. Cannot confirm the order.'))
                ->danger()
                ->send();

            return;
        }

        if ($hasMissingItems && ! $skipConfirmation) {
            $this->replaceMountedAction('confirmHasMissingItems');

            return;
        }

        $orderCheck = QcOrderCheck::findOrFail($qualityCheckState['order_check_id']);

        ConfirmOrderQc::make()->handle($orderCheck, $qualityCheckState['line_item_checks']);

        $this->searchForm->fill([
            'number_portal' => null,
            'processing' => false,
        ]);

        $this->qualityCheckForm->fill([
            'order_check_id' => null,
            'line_item_checks' => [],
        ]);

        Notification::make()
            ->title(__('Order confirmed successfully'))
            ->success()
            ->send();

        // reload the page
        $this->js('window.location.reload()');
    }

    public function cancelOrder(): void
    {
        $state = $this->qualityCheckForm->getRawState();

        $orderCheck = QcOrderCheck::find($state['order_check_id']);

        RemoveOrderFromQc::make()->handle($this->qcSession, $orderCheck->order);

        Notification::make()
            ->title(__('Order removed from QC session'))
            ->warning()
            ->send();

        $this->searchForm->fill([
            'number_portal' => null,
            'processing' => false,
        ]);

        $this->qualityCheckForm->fill([
            'order_check_id' => null,
            'line_item_checks' => [],
        ]);

        $this->dispatch('reloadRelations');
    }

    public function confirmHasMissingItems(): Action
    {
        return Action::make('confirmHasMissingItems')
            ->requiresConfirmation()
            ->modalDescription(__('Are you sure you want to package the order with the available items?'))
            ->action(fn () => $this->confirmOrder(skipConfirmation: true));
    }

    public function render(): string
    {
        return <<<'BLADE'
<div>
    <div>{{ $this->searchForm }}</div>
    <div style="margin-top: 2rem;">{{ $this->qualityCheckForm }}</div>
    <x-filament-actions::modals />
</div>
BLADE;
    }
}
