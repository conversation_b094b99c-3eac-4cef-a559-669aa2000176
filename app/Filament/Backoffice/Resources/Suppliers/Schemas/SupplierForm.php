<?php

namespace App\Filament\Backoffice\Resources\Suppliers\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SupplierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(64),
                TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->trim(),
            ]);
    }
}
