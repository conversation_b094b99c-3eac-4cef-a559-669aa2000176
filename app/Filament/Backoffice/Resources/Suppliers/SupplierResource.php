<?php

namespace App\Filament\Backoffice\Resources\Suppliers;

use App\Filament\Backoffice\Resources\Suppliers\Pages\CreateSupplier;
use App\Filament\Backoffice\Resources\Suppliers\Pages\EditSupplier;
use App\Filament\Backoffice\Resources\Suppliers\Pages\ListSuppliers;
use App\Filament\Backoffice\Resources\Suppliers\Schemas\SupplierForm;
use App\Filament\Backoffice\Resources\Suppliers\Tables\SuppliersTable;
use App\Filament\TranslatableResource;
use App\Models\Supplier;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class SupplierResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Supplier::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBuildingOffice;

    protected static string|UnitEnum|null $navigationGroup = 'Settings';

    public static function form(Schema $schema): Schema
    {
        return SupplierForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SuppliersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSuppliers::route('/'),
            'create' => CreateSupplier::route('/create'),
            'edit' => EditSupplier::route('/{record}/edit'),
        ];
    }
}
