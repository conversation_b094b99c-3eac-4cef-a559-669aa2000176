<?php

namespace App\Filament\Backoffice\Resources\Ideas\Actions;

use App\Models\Idea;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Halt;

class DeleteIdeaAction extends DeleteAction
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->before(function (Idea $record) {
            if ($record->listingUrls()->count() > 0) {
                Notification::make()
                    ->title('Cannot delete idea')
                    ->body('This idea has associated listing URLs and cannot be deleted.')
                    ->danger()
                    ->send();

                throw new Halt('This idea cannot be deleted because it has associated listing URLs.');
            }

            $designsCount = $record->designs()->count();
            $todoDesignsCount = $record->designs()->where('status', 'todo')->count();

            if ($designsCount > 0 && $todoDesignsCount < $designsCount) {
                Notification::make()
                    ->title('Cannot delete idea')
                    ->body('This idea has associated designs that are not in "todo" status and cannot be deleted.')
                    ->danger()
                    ->send();
                throw new Halt("This idea cannot be deleted because it has associated designs that are not in 'todo' status.");
            }
        });
    }
}
