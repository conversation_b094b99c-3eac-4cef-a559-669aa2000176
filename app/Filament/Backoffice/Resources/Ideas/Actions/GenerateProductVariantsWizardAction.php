<?php

namespace App\Filament\Backoffice\Resources\Ideas\Actions;

use App\Actions\GenerateProductVariants;
use App\Models\Idea;
use App\Models\ProductType;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;

class GenerateProductVariantsWizardAction
{
    public static function make(Idea $idea): Action
    {
        return Action::make('generate-product-variants')
            ->label('Generate Product Variants')
            ->icon(Heroicon::OutlinedSparkles)
            ->modalWidth('4xl')
            ->modalHeading('Generate Product Variants')
            ->form([
                Wizard::make([
                    Wizard\Step::make('Select Product Type')
                        ->description('Choose the product type for variant generation')
                        ->schema([
                            Select::make('product_type_id')
                                ->label('Product Type')
                                ->required()
                                ->options(
                                    $idea->productTypes()
                                        ->active()
                                        ->pluck('name', 'id')
                                        ->toArray()
                                )
                                ->placeholder('Select a product type...')
                                ->helperText('Only product types associated with this design are available.')
                                ->live()
                                ->afterStateUpdated(function ($state, Set $set) {
                                    // Clear options data when product type changes
                                    if ($state) {
                                        $productType = ProductType::with('options.values')->find($state);
                                        if ($productType) {
                                            foreach ($productType->options as $option) {
                                                $set("option_{$option->id}", []);
                                            }
                                        }
                                    }
                                }),
                        ]),

                    Wizard\Step::make('Configure Options')
                        ->description('Set values for each product option')
                        ->schema(function ($get) {
                            $productTypeId = $get('product_type_id');

                            if (! $productTypeId) {
                                return [];
                            }

                            $productType = ProductType::with('options.values')->find($productTypeId);

                            if (! $productType) {
                                return [];
                            }

                            $components = [];

                            foreach ($productType->options as $option) {
                                $components[] = TagsInput::make("option_{$option->id}")
                                    ->label($option->name)
                                    ->placeholder("Enter {$option->name} values...")
                                    ->helperText("Add multiple values for {$option->name}. Use the 'Load Default Values' button to load all predefined values.")
                                    ->suffixAction(
                                        FormAction::make('loadDefaultValues')
                                            ->label('Load Default Values')
                                            ->icon(Heroicon::OutlinedArrowDownTray)
                                            ->action(function (Set $set) use ($option) {
                                                $defaultValues = $option->values()->pluck('value')->toArray();
                                                $set("option_{$option->id}", $defaultValues);

                                                Notification::make()
                                                    ->title('Default values loaded')
                                                    ->body('Loaded '.count($defaultValues)." default values for {$option->name}")
                                                    ->success()
                                                    ->send();
                                            })
                                    );
                            }

                            return $components;
                        }),
                ])
                    ->submitAction(false)
                    ->nextAction(
                        fn (Action $action) => $action->label('Next Step'),
                    )
                    ->previousAction(
                        fn (Action $action) => $action->label('Previous Step'),
                    ),
            ])
            ->action(function (array $data) use ($idea) {
                $productType = ProductType::with('options')->find($data['product_type_id']);

                if (! $productType) {
                    Notification::make()
                        ->title('Error')
                        ->body('Product type not found.')
                        ->danger()
                        ->send();

                    return;
                }

                // Prepare options data
                $optionsData = [];
                foreach ($productType->options as $option) {
                    $values = $data["option_{$option->id}"] ?? [];
                    if (! empty($values)) {
                        $optionsData[$option->name] = $values;
                    }
                }

                if (empty($optionsData)) {
                    Notification::make()
                        ->title('No Options Selected')
                        ->body('Please select at least one option with values to generate variants.')
                        ->warning()
                        ->send();

                    return;
                }

                try {
                    $variants = GenerateProductVariants::make()->handle($idea, $productType, $optionsData);

                    if ($variants->isEmpty()) {
                        Notification::make()
                            ->title('No New Variants Created')
                            ->body('All possible variants for the selected options already exist.')
                            ->warning()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Variants Generated Successfully')
                            ->body("Created {$variants->count()} new product variants.")
                            ->success()
                            ->send();
                    }
                } catch (\Exception $e) {
                    Notification::make()
                        ->title('Error Generating Variants')
                        ->body($e->getMessage())
                        ->danger()
                        ->send();
                }
            });
    }
}
