<?php

namespace App\Filament\Backoffice\Resources\Ideas\Actions;

use App\Actions\ImportIdeaListings;
use App\Actions\ParseCsvFile;
use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\View;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class ImportIdeaListingsAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Import Idea Listings')
            ->icon('heroicon-o-cloud-arrow-up')
            ->outlined()
            ->color('gray')
            ->slideOver();

        $this->steps([
            Step::make('upload-file')
                ->label('Upload CSV File')
                ->schema([
                    FileUpload::make('csv_file')
                        ->label('CSV File')
                        ->required()
                        ->acceptedFileTypes(['text/csv', '.csv'])
                        ->directory('imports/idea-listings')
                        ->maxSize(2 * 1024) // 1MB max
                        ->helperText('Only CSV files are allowed.'),

                    TextEntry::make('example_format')
                        ->label('Example CSV file')
                        ->state('https://docs.google.com/spreadsheets/d/1u1tD8cJNatWktczuMX6XCxxOzRP0TnJzPYclmvTRX1c/edit?usp=sharing')
                        ->url('https://docs.google.com/spreadsheets/d/1u1tD8cJNatWktczuMX6XCxxOzRP0TnJzPYclmvTRX1c/edit?usp=sharing')
                        ->openUrlInNewTab()
                        ->columnSpanFull(),
                ])
                ->afterValidation(function (Get $get) {
                    if (! $get('csv_file')) {
                        throw new Halt('Please upload a CSV file.');
                    }

                    try {
                        $filePath = Arr::first($get('csv_file'))->getRealPath();
                        ImportIdeaListings::make()->validate($filePath);
                    } catch (Exception $e) {
                        Notification::make()
                            ->title(__('Validation Error'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        throw new Halt($e->getMessage());
                    }
                }),

            Step::make('confirm-data')
                ->schema([
                    View::make('basic-table')
                        ->viewData(function (Get $get) {
                            if (! $get('csv_file')) {
                                return ['rows' => []];
                            }

                            $filePath = Arr::first($get('csv_file'))->getRealPath();
                            $records = ParseCsvFile::make()->handle($filePath);

                            return ['rows' => $records];
                        }),
                ]),
        ])->action(function (array $data) {
            if (! isset($data['csv_file'])) {
                throw new Halt('No file uploaded.');
            }

            ImportIdeaListings::make()->handle(
                Storage::path($data['csv_file'])
            );

            Notification::make()
                ->title('Import completed successfully')
                ->body('Listing URLs have been imported successfully')
                ->success()
                ->send();
        });
    }
}
