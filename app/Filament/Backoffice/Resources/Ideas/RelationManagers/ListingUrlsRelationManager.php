<?php

namespace App\Filament\Backoffice\Resources\Ideas\RelationManagers;

use App\Models\ListingUrl;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ListingUrlsRelationManager extends RelationManager
{
    protected static string $relationship = 'listingUrls';

    protected static ?string $title = 'Listing URLs';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                TextInput::make('link')
                    ->columnSpanFull()
                    ->required(),
                Select::make('design_id')
                    ->label('Design')
                    ->relationship('design', 'productType.name')
                    ->options(function () {
                        $this->ownerRecord->load('designs.productType');

                        $options = [];
                        foreach ($this->ownerRecord->designs as $design) {
                            $options[$design->id] = $design->productType->name;
                        }

                        return $options;
                    })
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->modifyQueryUsing(function ($query) {
                return $query->with(['design.productType']);
            })
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('link')
                    ->label('Link')
                    ->url(fn (ListingUrl $record) => $record->link)
                    ->openUrlInNewTab()
                    ->wrap(),

                TextColumn::make('product_type')
                    ->label('Design')
                    ->state(fn (ListingUrl $record) => $record->design?->productType?->name),

                TextColumn::make('created_at'),
            ])
            ->defaultSort('created_at', 'desc')
            ->headerActions([
                CreateAction::make()
                    ->outlined()
                    ->color('gray'),
            ])
            ->recordActions([
                EditAction::make()
                    ->hiddenLabel(),
                DeleteAction::make()
                    ->hiddenLabel(),
            ]);
    }
}
