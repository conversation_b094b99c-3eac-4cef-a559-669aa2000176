<?php

namespace App\Filament\Backoffice\Resources\Ideas\RelationManagers;

use App\Actions\CreateTaskForDesigner;
use App\Enums\UserRole;
use App\Filament\Backoffice\Resources\Designs\DesignResource;
use App\Filament\Backoffice\Resources\Designs\DownloadMediaAction;
use App\Filament\Backoffice\Resources\Designs\Schemas\DesignForm;
use App\Filament\Backoffice\Resources\Designs\Tables\DesignsTable;
use App\Models\Design;
use App\Models\ProductType;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Actions\BulkAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Collection;

class DesignsRelationManager extends RelationManager
{
    protected static string $relationship = 'designs';

//    public static function canViewForRecord(Model|Idea $ownerRecord, string $pageClass): bool
//    {
//        return $ownerRecord->isSimple();
//    }

    public function form(Schema $schema): Schema
    {
        return DesignForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        /* @var \App\Models\Idea $idea */
        $idea = $this->getOwnerRecord();

        return match (true) {
            $idea->isGrouped() => $this->tableForGroupedIdea($table),
            $idea->isSimple() => $this->tableForSimpleIdea($table),
        };
    }

    protected function tableForGroupedIdea(Table $table): Table
    {
        $table = DesignsTable::configure($table);

        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // We do not use $query here, because it scoped with the parent idea
                // And get the design of the Grouped idea, what we don't want to
                return Design::whereIn('idea_id', $this->getOwnerRecord()->children->pluck('id')->toArray())
                    ->with(['idea', 'media', 'productType']);
            })
            ->defaultGroup(
                Group::make('product_type_id')
                    ->label('Product type')
                    ->getTitleFromRecordUsing(fn(Design $record) => $record->productType->name)
            )
            ->columns([
                ...$table->getColumns(),
                TextColumn::make('idea.uid')
                    ->label('Idea UID')
                    ->badge(),
            ])
            ->recordUrl(fn(Design $record) => DesignResource::getUrl('edit', ['record' => $record]))
            ->checkIfRecordIsSelectableUsing(fn(Design $record) => !$record->designByCompany())
            ->recordActions([
                DownloadMediaAction::make('download-media'),
                EditAction::make()->hiddenLabel(),
                DeleteAction::make()->hiddenLabel(),
            ])
            ->toolbarActions([
                BulkAction::make('assign-designer')
                    ->label('Assign designer')
                    ->schema([
                        Select::make('user_id')
                            ->label('Designer')
                            ->options(
                                User::where('role', UserRole::Designer)
                                    ->orderBy('id')
                                    ->pluck('name', 'id')
                            )
                            ->required(fn(Get $get) => !$get('design_by_company'))
                            ->searchable()
                            ->placeholder('Select a designer'),
                        Textarea::make('comment')
                            ->label('Comment')
                            ->placeholder('Nôi dung bổ sung cho designer')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->action(function (array $data, Collection $records) {
                        $task = CreateTaskForDesigner::make()->handle(
                            designer: User::query()->findOrFail($data['user_id']),
                            designs: $records,
                            owner: user(),
                            title: "Design task for " . $records->first()->productType->name,
                            comment: $data['comment']
                        );

                        Notification::make()
                            ->title('Designer assigned successfully')
                            ->body("Task #{$task->id} has been assigned to {$task->assignee->name}.")
                            ->success()
                            ->send();
                    })
            ]);
    }

    protected function tableForSimpleIdea(Table $table): Table
    {
        return DesignsTable::configure($table)
            ->recordUrl(fn(Design $record) => DesignResource::getUrl('edit', ['record' => $record]))
            ->headerActions([
                Action::make('attach-product-type')
                    ->outlined()
                    ->color('gray')
                    ->label('Attach Product Type')
                    ->fillForm(function () {
                        return [
                            'product_type_id' => $this->ownerRecord->productTypes()->pluck('product_types.id')->toArray(),
                        ];
                    })
                    ->schema([
                        CheckboxList::make('product_type_id')
                            ->label('Product Type')
                            ->options(ProductType::all()->pluck('name', 'id'))
                            ->required()
                            ->columns(2),
                    ])
                    ->action(function (array $data) {
                        $this->ownerRecord->productTypes()->sync($data['product_type_id']);

                        Notification::make()
                            ->title('Product Type Attached')
                            ->success()
                            ->send();
                    }),
            ]);
    }
}
