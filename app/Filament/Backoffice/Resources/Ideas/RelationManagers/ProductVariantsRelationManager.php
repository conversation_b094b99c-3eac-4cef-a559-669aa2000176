<?php

namespace App\Filament\Backoffice\Resources\Ideas\RelationManagers;

use App\Actions\GenerateProductVariants;
use App\Filament\Backoffice\Resources\ProductVariants\Tables\ProductVariantsTable;
use App\Models\Idea;
use App\Models\ProductType;
use App\Models\ProductTypeOptionValue;
use Filament\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ProductVariantsRelationManager extends RelationManager
{
    protected static string $relationship = 'productVariants';

    public static function canViewForRecord(Model|Idea $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->isSimple();
    }

    protected function getTableHeaderActions(): array
    {
        return [
            Action::make('generate-product-variants')
                ->label('Generate Product Variants')
                ->icon(Heroicon::Plus)
                ->outlined()
                ->color('gray')
                ->schema([
                    TextEntry::make('warning-no-product-type')
                        ->visible(fn () => $this->getOwnerRecord()->productTypes->isEmpty())
                        ->state(fn () => $this->getOwnerRecord()->productTypes->isEmpty() ? __('No product types available, please attach product types for this design') : ''),

                    Radio::make('product_type_id')
                        ->label('Product Type')
                        ->columns(3)
                        ->options(function () {
                            $design = $this->getOwnerRecord();

                            return $design->productTypes->filter(fn ($productType) => $productType->is_active)
                                ->pluck('name', 'id');
                        })
                        ->required()
                        ->live()
                        ->afterStateUpdated(function ($state, $set) {
                            if ($state) {
                                $productType = ProductType::with('options.values')->find($state);
                                if ($productType && $productType->options->isNotEmpty()) {
                                    $options = $productType->options->map(function ($option) {
                                        return [
                                            'name' => $option->name,
                                            'values' => [],
                                            'available_values' => $option->values()->pluck('value', 'id')->toArray(),
                                            'option_id' => $option->id,
                                            'is_required' => $option->is_required,
                                        ];
                                    })->toArray();
                                    $set('options', $options);
                                } else {
                                    $set('options', []);
                                }
                            } else {
                                $set('options', []);
                            }
                        }),
                    Repeater::make('options')
                        ->label('Product Options')
                        ->columns(3)
                        ->schema(function (Get $get) {
                            $productTypeId = $get('product_type_id');

                            if (! $productTypeId) {
                                return [
                                    TextInput::make('placeholder')
                                        ->disabled()
                                        ->default('Please select a product type first')
                                        ->dehydrated(false),
                                ];
                            }

                            $productType = ProductType::with('options.values')->find($productTypeId);

                            if (! $productType || $productType->options->isEmpty()) {
                                return [
                                    TextInput::make('placeholder')
                                        ->disabled()
                                        ->default('This product type has no options configured')
                                        ->dehydrated(false),
                                ];
                            }

                            return [
                                TextInput::make('name')
                                    ->label('Option Name')
                                    ->columnSpan(1)
                                    ->readOnly(),
                                Select::make('values')
                                    ->label('Values')
                                    ->multiple()
                                    ->required(fn (Get $get) => $get('is_required'))
                                    ->columnSpan(2)
                                    ->placeholder('Select option values')
                                    ->options(fn (Get $get) => $get('available_values') ?? [])
                                    ->suffixAction(
                                        fn (Get $get) => Action::make('select_all')
                                            ->label('Select All')
                                            ->icon(Heroicon::Check)
                                            ->color('gray')
                                            ->action(function ($set) use ($get) {
                                                $availableValues = $get('available_values');
                                                if ($availableValues) {
                                                    $set('values', array_keys($availableValues));
                                                }
                                            })
                                            ->visible(fn (Get $get) => ! empty($get('available_values')))
                                            ->tooltip('Select all available values for this option')
                                    ),
                                TextInput::make('available_values')
                                    ->hidden()
                                    ->dehydrated(false),
                                Hidden::make('option_id'),
                                Hidden::make('is_required'),
                            ];
                        })
                        ->reorderable(false)
                        ->deletable(false)
                        ->addable(false)
                        ->visible(fn (Get $get) => ! empty($get('product_type_id'))),

                ])
                ->action(function (array $data) {
                    $design = $this->getOwnerRecord();
                    $productType = ProductType::with('options.values')->find($data['product_type_id']);

                    try {
                        DB::beginTransaction();

                        // Convert option value IDs back to values for the action
                        $processedOptions = collect($data['options'])->map(function ($option) {
                            if (! empty($option['values'])) {
                                // Convert IDs to actual values
                                $valueIds = $option['values'];
                                $actualValues = ProductTypeOptionValue::whereIn('id', $valueIds)
                                    ->pluck('value')
                                    ->toArray();

                                $option['values'] = $actualValues;
                            }

                            return $option;
                        })->toArray();

                        $variants = GenerateProductVariants::make()->handle($design, $productType, $processedOptions);

                        DB::commit();

                        Notification::make()
                            ->title('Product variants generated successfully')
                            ->body("Generated {$variants->count()} new product variants.")
                            ->success()
                            ->send();

                        // Refresh the table to show new variants
                        $this->resetTable();
                    } catch (\Exception $e) {
                        DB::rollBack();
                        report($e);

                        Notification::make()
                            ->title('Error generating variants')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->modalWidth('5xl'),
        ];
    }

    public function form(Schema $schema): Schema
    {
        return $schema;
    }

    public function table(Table $table): Table
    {
        return ProductVariantsTable::configure($table)
            ->filters([
                SelectFilter::make('product_type_id')
                    ->label('Product Type')
                    ->relationship('productType', 'name')
                    ->preload()
                    ->searchable(),
            ]);
    }
}
