<?php

namespace App\Filament\Backoffice\Resources\Ideas\RelationManagers;

use App\Filament\Backoffice\Resources\Ideas\IdeaResource;
use App\Filament\Backoffice\Resources\Ideas\Tables\IdeasTable;
use App\Models\Idea;
use Filament\Actions\AssociateAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DissociateAction;
use Filament\Actions\DissociateBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChildrenRelationManager extends RelationManager
{
    protected static string $relationship = 'children';

    protected static bool $isLazy = false;

    public static function canViewForRecord(Model|Idea $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->isGrouped();
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return IdeasTable::configure($table)
            ->heading('Children ideas')
            ->searchable(false)
            ->recordActions([
                EditAction::make()
                    ->hiddenLabel()
                    ->url(fn (Idea $record) => IdeaResource::getUrl('edit', ['record' => $record])),
                DeleteAction::make()
                    ->hiddenLabel(),
            ]);
    }
}
