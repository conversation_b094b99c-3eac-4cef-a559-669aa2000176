<?php

namespace App\Filament\Backoffice\Resources\Ideas\Tables;

use App\Actions\ReplicateIdea;
use App\Filament\Backoffice\Resources\Ideas\Actions\DeleteIdeaAction;
use App\Filament\Backoffice\Resources\Ideas\IdeaResource;
use App\Models\Idea;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class IdeasTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('featured_image')
                    ->collection('featured_image')
                    ->label(false)
                    ->grow(false)
                    ->imageSize(40),

                TextColumn::make('name')
                    ->searchable(),

                TextColumn::make('uid')
                    ->label('UID')
                    ->searchable()
                    ->badge()
                    ->copyable(),


                TextColumn::make('type')
                    ->badge(),

                TextColumn::make('parent.uid')
                    ->label('Parent idea')
                    ->badge()
                    ->copyable(),

                IconColumn::make('is_trademark')
                    ->boolean(),

                TextColumn::make('trademark_description')
                    ->label('TM'),

                TextColumn::make('tags.name')
                    ->label('Tags')
                    ->badge()
                    ->separator(',')
                    ->wrap(),

                TextColumn::make('product_variants_count')
                    ->label('Variants')
                    ->counts('productVariants')
                    ->badge()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('user.name')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->recordActions([
                EditAction::make()
                    ->hiddenLabel(),
                Action::make('replicate-idea')
                    ->label('Replicate')
                    ->hiddenLabel()
                    ->tooltip('Clone the idea')
                    ->icon(Heroicon::OutlinedSquare3Stack3d)
                    ->authorize('replicate', Idea::class)
                    ->requiresConfirmation()
                    ->action(function (Idea $record) {
                        $replica = ReplicateIdea::run($record->refresh());

                        Notification::make()
                            ->title('Idea replicated successfully!')
                            ->success()
                            ->send();

                        // redirect to the edit page
                        return redirect()->to(IdeaResource::getUrl('edit', ['record' => $replica->id]));
                    }),
                DeleteIdeaAction::make()
                    ->hiddenLabel(),
            ])
            ->toolbarActions([])
            ->defaultSort('id', 'desc');
    }
}
