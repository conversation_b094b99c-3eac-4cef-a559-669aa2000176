<?php

namespace App\Filament\Backoffice\Resources\Ideas\Schemas;

use App\Enums\IdeaType;
use App\Filament\Actions\CopyAction;
use App\Filament\Forms\TagsInput;
use App\Models\Idea;
use App\Models\Tag;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Support\Enums\Operation;

class IdeaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Section::make('Infomation')
                            ->columnSpan(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Idea name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., Summer Vibes, Vintage Logo, Abstract Art'),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->placeholder('Brief description of the design')
                                    ->rows(3),

                                Checkbox::make('is_trademark')
                                    ->label('Is trademark?')
                                    ->live(),

                                TextInput::make('trademark_description')
                                    ->label('Trademark Description')
                                    ->visible(fn($get) => $get('is_trademark')),

                                Checkbox::make('is_personalized')
                                    ->label('Is personalized?')
                                    ->live(),

                                Radio::make('personalized_description')
                                    ->inline()
                                    ->visible(fn(Get $get) => $get('is_personalized'))
                                    ->required(fn(Get $get) => $get('is_personalized'))
                                    ->options([
                                        'Text'    => 'Text',
                                        'Picture' => 'Picture',
                                        'Both'    => 'Both',
                                        'Other'   => 'Other',
                                    ]),

                                Checkbox::make('is_made_by_ai')
                                    ->label('Is made by AI?')
                                    ->live(),

                                CheckboxList::make('made_by_ai_description')
                                    ->label('AI Tools')
                                    ->visible(fn(Get $get) => $get('is_made_by_ai'))
                                    ->required(fn(Get $get) => $get('is_made_by_ai'))
                                    ->columns(4)
                                    ->options([
                                        'ChatGPT-Sora' => 'ChatGPT-Sora',
                                        'Midjourney'   => 'Midjourney',
                                        'Ideogram'     => 'Ideogram',
                                        'Other'        => 'Other',
                                    ]),

                                TagsInput::make('tags')
                                    ->label('Tags')
                                    ->required()
                                    ->placeholder('Add tags to categorize this design')
                                    ->helperText('Press Enter to add a tag')
                                    ->reorderable()
                                    ->suggestions(function () {
                                        return Tag::query()
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->hintAction(
                                        CopyAction::make('copy-tags')->content(fn(?array $state) => implode(',', $state))
                                    ),

                                Select::make('product_type_ids')
                                    ->visibleOn(Operation::Create)
                                    ->label('Product Types')
                                    ->relationship('productTypes', 'name')
                                    ->multiple()
                                    ->searchable()
                                    ->preload()
                                    ->required(),

                                Hidden::make('user_id')
                                    ->default(fn() => user()->id),

                                Textarea::make('notes')
                                    ->rows(3),

                            ]),

                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Select::make('type')
                                    ->label('Idea type')
                                    ->selectablePlaceholder(false)
                                    ->options(IdeaType::class)
                                    ->default(IdeaType::Grouped)
                                    ->live(),

                                TextEntry::make('idea_group_description')
                                    ->visible(fn(Get $get) => $get('type') === IdeaType::Grouped)
                                    ->hiddenLabel()
                                    ->color('info')
                                    ->state('Đối với idea type là Group, sẽ bulk upload ảnh sau khi tạo idea'),

                                // Simple idea
                                Group::make()
                                    ->visible(fn(Get $get) => $get('type') === IdeaType::Simple)
                                    ->schema([
                                        SpatieMediaLibraryFileUpload::make('featured_image')
                                            ->collection('featured_image')
                                            ->image()
                                            ->required()
                                            ->label('Image'),

                                        Select::make('parent_id')
                                            ->label('Parent Idea')
                                            ->relationship('parent', 'name')
                                            ->searchable()
                                            ->getSearchResultsUsing(
                                                fn(string $search): array => Idea::query()
                                                    ->where(function ($query) use ($search) {
                                                        $query->where('uid', 'like', "%$search");
                                                    })
                                                    ->limit(10)
                                                    ->get()
                                                    ->mapWithKeys(fn($idea) => [$idea->id => $idea->uid . ' - ' . $idea->name])
                                                    ->toArray()
                                            )
                                            ->getOptionLabelUsing(fn($value): ?string => Idea::find($value)?->uid . ' - ' . Idea::find($value)?->name)
                                            ->placeholder('Search by UID...'),

                                        TextInput::make('legacy_sku')
                                            ->label('Legacy SKU')
                                            ->placeholder('Enter legacy SKU if available')
                                            ->helperText('For legacy SKU, pls set all scores of the designs to 0')
                                            ->maxLength(64),

                                    ]),

                                // Grouped idea
//                                Group::make()
//                                    ->visible(fn (Get $get) => $get('type') === IdeaType::Grouped)
//                                    ->schema([
//                                        FileUpload::make('images')
//                                            ->image()
//                                            ->multiple()
//                                            ->disk('public')
//                                            ->directory('ideas-images')
//                                            ->panelLayout('grid'),
//                                    ]),

                                TextInput::make('uid')
                                    ->label('Idea UID')
                                    ->placeholder('Auto-generated')
                                    ->disabled()
                                    ->helperText('Unique identifier will be auto-generated'),

                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }
}
