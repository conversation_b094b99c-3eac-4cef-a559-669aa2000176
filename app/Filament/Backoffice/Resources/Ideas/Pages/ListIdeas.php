<?php

namespace App\Filament\Backoffice\Resources\Ideas\Pages;

use App\Filament\Backoffice\Resources\Ideas\Actions\ImportIdeaListingsAction;
use App\Filament\Backoffice\Resources\Ideas\IdeaResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;

class ListIdeas extends ListRecords
{
    protected static string $resource = IdeaResource::class;

    public function table(Table $table): Table
    {
        return parent::table($table)
            ->modifyQueryUsing(function ($query) {
                return $query->whereNull('parent_id');
            });
    }

    protected function getHeaderActions(): array
    {
        return [
            ImportIdeaListingsAction::make('import-idea-listings'),
            CreateAction::make(),
        ];
    }
}
