<?php

namespace App\Filament\Backoffice\Resources\Ideas\Pages;

use App\Filament\Backoffice\Resources\ProductVariants\Tables\ProductVariantsTable;
use App\Filament\Seller\Resources\Ideas\IdeaResource;
use BackedEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ManageProductVariants extends ManageRelatedRecords
{
    protected static string $resource = IdeaResource::class;

    protected static string $relationship = 'productVariants';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $navigationLabel = 'Manage Product Variants';

    protected function getHeaderActions(): array {}

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('sku')
                    ->required()
                    ->disabled()
                    ->maxLength(255),

                TextInput::make('vid')
                    ->label('Variant ID')
                    ->disabled(),

                Select::make('product_type_id')
                    ->label('Product Type')
                    ->relationship('productType', 'name')
                    ->disabled()
                    ->required()
                    ->searchable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return ProductVariantsTable::configure($table)
            ->filters([
                SelectFilter::make('product_type_id')
                    ->label('Product Type')
                    ->relationship('productType', 'name')
                    ->preload()
                    ->searchable(),
            ]);
    }
}
