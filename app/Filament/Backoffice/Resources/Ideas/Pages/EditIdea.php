<?php

namespace App\Filament\Backoffice\Resources\Ideas\Pages;

use App\Actions\CreateChildIdeasFromImages;
use App\Enums\DesignStatus;
use App\Enums\IdeaType;
use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Backoffice\Resources\Ideas\IdeaResource;
use App\Models\Idea;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditIdea extends EditRecord
{
    protected static string $resource = IdeaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('bulk-images-upload')
                ->label('Bulk Images Upload')
                ->visible(fn (Idea $record) => $record->type === IdeaType::Grouped && $record->children->isEmpty())
                ->schema([
                    FileUpload::make('images')
                        ->image()
                        ->multiple()
                        ->disk('public')
                        ->directory('idea-images')
                        ->panelLayout('grid')
                        ->required(),
                ])
                ->action(function (array $data, Idea $record) {
                    CreateChildIdeasFromImages::make()->handle($record, $data['images']);

                    Notification::make()
                        ->title('Sub ideas created successfully')
                        ->body(count($data['images']).' sub ideas created with uploaded images.')
                        ->success()
                        ->send();
                }),
            ActivityLogTimelineAction::make(),
            DeleteAction::make(),
        ];
    }
}
