<?php

namespace App\Filament\Backoffice\Resources\Ideas;

use App\Filament\Backoffice\Resources\Ideas\Pages\CreateIdea;
use App\Filament\Backoffice\Resources\Ideas\Pages\EditIdea;
use App\Filament\Backoffice\Resources\Ideas\Pages\ListIdeas;
use App\Filament\Backoffice\Resources\Ideas\RelationManagers\ChildrenRelationManager;
use App\Filament\Backoffice\Resources\Ideas\RelationManagers\DesignsRelationManager;
use App\Filament\Backoffice\Resources\Ideas\RelationManagers\ListingUrlsRelationManager;
use App\Filament\Backoffice\Resources\Ideas\RelationManagers\ProductVariantsRelationManager;
use App\Filament\Backoffice\Resources\Ideas\Schemas\IdeaForm;
use App\Filament\Backoffice\Resources\Ideas\Tables\IdeasTable;
use App\Filament\TranslatableResource;
use App\Models\Idea;
use BackedEnum;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class IdeaResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Idea::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedLightBulb;

    protected static string|UnitEnum|null $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 50;

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (user()->isManager()) {
            return $query;
        }

        return $query->where('user_id', user()->id);
    }

    public static function form(Schema $schema): Schema
    {
        return IdeaForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return IdeasTable::configure($table);
    }

    public static function getRelations(): array
    {
        $relations =  [
            RelationGroup::make('Products', [
                ChildrenRelationManager::class,
                DesignsRelationManager::class,
                ProductVariantsRelationManager::class,
            ]),
            RelationGroup::make('Listing URLs', [
                ListingUrlsRelationManager::class,
            ]),
        ];

        return $relations;
    }

    public static function getPages(): array
    {
        return [
            'index'  => ListIdeas::route('/'),
            'create' => CreateIdea::route('/create'),
            'edit'   => EditIdea::route('/{record}/edit'),
        ];
    }
}
