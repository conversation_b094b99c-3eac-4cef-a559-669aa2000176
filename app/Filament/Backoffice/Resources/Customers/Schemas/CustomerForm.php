<?php

namespace App\Filament\Backoffice\Resources\Customers\Schemas;

use App\Enums\Platform;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class CustomerForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('platform')
                    ->options(Platform::class),
                TextInput::make('id_platform'),
                TextInput::make('name')
                    ->required(),
                TextInput::make('email')
                    ->email()
                    ->required(),
                TextInput::make('username')
                    ->required(),
                TextInput::make('avatar_url'),
            ]);
    }
}
