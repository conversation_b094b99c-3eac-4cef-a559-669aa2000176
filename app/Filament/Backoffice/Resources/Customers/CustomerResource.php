<?php

namespace App\Filament\Backoffice\Resources\Customers;

use App\Filament\Backoffice\Resources\Customers\Pages\CreateCustomer;
use App\Filament\Backoffice\Resources\Customers\Pages\EditCustomer;
use App\Filament\Backoffice\Resources\Customers\Pages\ListCustomers;
use App\Filament\Backoffice\Resources\Customers\Pages\ViewCustomer;
use App\Filament\Backoffice\Resources\Customers\Schemas\CustomerForm;
use App\Filament\Backoffice\Resources\Customers\Schemas\CustomerInfolist;
use App\Filament\Backoffice\Resources\Customers\Tables\CustomersTable;
use App\Filament\TranslatableResource;
use App\Models\Customer;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CustomerResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Customer::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedUserCircle;

    protected static string|UnitEnum|null $navigationGroup = 'Others';

    public static function form(Schema $schema): Schema
    {
        return CustomerForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return CustomerInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CustomersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCustomers::route('/'),
            'create' => CreateCustomer::route('/create'),
            'view' => ViewCustomer::route('/{record}'),
            'edit' => EditCustomer::route('/{record}/edit'),
        ];
    }
}
