<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Tables;

use App\Actions\ReplicateProductType;
use App\Filament\Backoffice\Resources\ProductTypes\ProductTypeResource;
use App\Models\ProductType;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProductTypesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                return $query->with('options.values');
            })
            ->columns([
                SpatieMediaLibraryImageColumn::make('image')
                    ->collection('featured_image'),
                TextColumn::make('id'),
                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->weight('medium'),

                TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }

                        return $state;
                    }),

                TextColumn::make('options_display')
                    ->label('Options')
                    ->wrap()
                    ->html()
                    ->state(function (ProductType $record) {
                        return $record->options->map(function ($option) {
                            $values = $option->values->pluck('value')->join(', ');

                            return $option->name.': '.($values ?: 'No values');
                        })->join('<br>');
                    }),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ]),
            ])
            ->recordActions([
                EditAction::make()
                    ->hiddenLabel(),
                Action::make('replicate')
                    ->label('Replicate')
                    ->authorize('replicate', ProductType::class)
                    ->icon(Heroicon::OutlinedDocumentDuplicate)
                    ->requiresConfirmation()
                    ->action(function (ProductType $record) {
                        $replica = ReplicateProductType::run($record);

                        Notification::make()
                            ->title('Product Type Replicated')
                            ->body("The product type '{$record->name}' has been successfully replicated as '{$replica->name}'.")
                            ->success()
                            ->send();

                        // redirect to the edit page
                        return redirect()->to(ProductTypeResource::getUrl('edit', ['record' => $replica->id]));
                    }),
                DeleteAction::make()
                    ->hiddenLabel(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc');
    }
}
