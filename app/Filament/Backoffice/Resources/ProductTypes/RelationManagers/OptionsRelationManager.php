<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\RelationManagers;

use App\Actions\CreateProductTypeOption;
use App\Actions\UpdateProductTypeOption;
use App\Models\ProductTypeOption;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;

class OptionsRelationManager extends RelationManager
{
    protected static string $relationship = 'options';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                TagsInput::make('values')
                    ->hint('Press enter to add a new value')
                    ->reorderable(),
                Toggle::make('is_required')
                    ->label('Required')
                    ->default(false),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->recordTitleAttribute('name')
            ->modifyQueryUsing(fn ($query) => $query->with('values'))
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                TextColumn::make('values.value')
                    ->label('Values')
                    ->badge(),
                ToggleColumn::make('is_required')
                    ->label('Required'),
                TextColumn::make('sort_order'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->outlined()
                    ->color('gray')
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                        TagsInput::make('values')
                            ->required()
                            ->hint('Press enter to add a new value')
                            ->reorderable(),
                        Toggle::make('is_required')
                            ->label('Required')
                            ->default(true),
                    ])
                    ->createAnother(true)
                    ->action(function (array $data) {
                        CreateProductTypeOption::make()->handle(
                            $this->getOwnerRecord(),
                            $data['name'],
                            $data['values'] ?? [],
                            $data['is_required'] ?? false,
                        );
                    }),
            ])
            ->recordActions([
                EditAction::make()
                    ->mutateRecordDataUsing(function (array $data, ProductTypeOption $record): array {
                        $data['values'] = $record->values()->pluck('value')->toArray();

                        return $data;
                    })
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                        TagsInput::make('values')
                            ->required()
                            ->hint('Press enter to add a new value')
                            ->helperText('You can drag values to reorder them')
                            ->reorderable(),
                        Toggle::make('is_required')
                            ->label('Required')
                            ->default(false),
                    ])
                    ->action(function (ProductTypeOption $record, array $data): void {
                        UpdateProductTypeOption::make()->handle(
                            $record,
                            $data['name'],
                            $data['values'] ?? [],
                            $data['is_required'] ?? false,
                        );
                    }),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
