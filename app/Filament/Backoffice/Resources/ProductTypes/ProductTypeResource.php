<?php

namespace App\Filament\Backoffice\Resources\ProductTypes;

use App\Filament\Backoffice\Resources\ProductTypes\Pages\CreateProductType;
use App\Filament\Backoffice\Resources\ProductTypes\Pages\EditProductType;
use App\Filament\Backoffice\Resources\ProductTypes\Pages\ListProductTypes;
use App\Filament\Backoffice\Resources\ProductTypes\RelationManagers\OptionsRelationManager;
use App\Filament\Backoffice\Resources\ProductTypes\Schemas\ProductTypeForm;
use App\Filament\Backoffice\Resources\ProductTypes\Tables\ProductTypesTable;
use App\Filament\TranslatableResource;
use App\Models\ProductType;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class ProductTypeResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = ProductType::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedCube;

    protected static string|UnitEnum|null $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 80;

    public static function form(Schema $schema): Schema
    {
        return ProductTypeForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ProductTypesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            OptionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductTypes::route('/'),
            'create' => CreateProductType::route('/create'),
            'edit' => EditProductType::route('/{record}/edit'),
        ];
    }
}
