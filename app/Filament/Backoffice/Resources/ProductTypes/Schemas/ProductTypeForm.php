<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Schemas;

use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Section::make('Basic Information')
                            ->columnSpan(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Product Type Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->placeholder('e.g., T-Shirt, Mug, Phone Case'),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->rows(3)
                                    ->placeholder('Describe this product type...'),

                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true)
                                    ->helperText('Inactive product types will not be available for new designs'),
                            ]),

                        Section::make('Image')
                            ->columnSpan(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('featured_image')
                                    ->label('Product Image')
                                    ->collection('featured_image')
                                    ->image()
                                    ->helperText('Upload an image that represents this product type'),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }
}
