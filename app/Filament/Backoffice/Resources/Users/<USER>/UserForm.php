<?php

namespace App\Filament\Backoffice\Resources\Users\Schemas;

use App\Enums\UserRole;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('email')
                    ->email()
                    ->required(),
                Select::make('role')
                    ->options(UserRole::class)
                    ->default(UserRole::Seller->value)
                    ->required(),
                TextInput::make('password')
                    ->password()
                    ->helperText('You can input anything, but it will not work.'),
            ]);
    }
}
