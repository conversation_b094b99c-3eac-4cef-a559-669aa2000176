<?php

namespace App\Filament\Backoffice\Resources\Users\RelationManagers;

use App\Filament\Seller\Resources\PersonalAccessTokens\Tables\PersonalAccessTokensTable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class PersonalAccessTokensRelationManager extends RelationManager
{
    protected static string $relationship = 'tokens';

    protected static ?string $recordTitleAttribute = 'name';

    public function table(Table $table): Table
    {
        return PersonalAccessTokensTable::configure($table)
            ->minimal();
    }
}
