<?php

namespace App\Filament\Backoffice\Resources\Users;

use App\Filament\Backoffice\Resources\Users\Pages\CreateUser;
use App\Filament\Backoffice\Resources\Users\Pages\EditUser;
use App\Filament\Backoffice\Resources\Users\Pages\ListUsers;
use App\Filament\Backoffice\Resources\Users\Schemas\UserForm;
use App\Filament\Backoffice\Resources\Users\Tables\UsersTable;
use App\Filament\TranslatableResource;
use App\Models\User;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class UserResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = User::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedUserGroup;

    protected static string|UnitEnum|null $navigationGroup = 'System';

    public static function form(Schema $schema): Schema
    {
        return UserForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return UserForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return UsersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PersonalAccessTokensRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListUsers::route('/'),
            'create' => CreateUser::route('/create'),
            'edit' => EditUser::route('/{record}/edit'),
        ];
    }
}
