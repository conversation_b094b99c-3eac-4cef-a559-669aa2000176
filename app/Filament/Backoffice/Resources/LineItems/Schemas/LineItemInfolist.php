<?php

namespace App\Filament\Backoffice\Resources\LineItems\Schemas;

use App\Models\LineItem;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class LineItemInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('number_portal')
                    ->label('TDA number')
                    ->url(fn (LineItem $record) => route('filament.backoffice.resources.orders.view', $record->order))
                    ->badge(),
                TextEntry::make('status')
                    ->badge(),
                TextEntry::make('sku')
                    ->label('SKU'),
                TextEntry::make('quantity')
                    ->numeric(),
                TextEntry::make('price')
                    ->recordMoney(),
                TextEntry::make('subtotal')
                    ->recordMoney(),
                TextEntry::make('id_platform'),
                TextEntry::make('etsy_listing_id'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
