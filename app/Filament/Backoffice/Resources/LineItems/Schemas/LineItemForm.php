<?php

namespace App\Filament\Backoffice\Resources\LineItems\Schemas;

use App\Enums\LineItemStatus;
use App\Models\LineItem;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class LineItemForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Textarea::make('product_name')
                    ->required()
                    ->columnSpanFull(),
                Textarea::make('product_image_url')
                    ->columnSpanFull(),
                TextInput::make('sku')
                    ->label('SKU'),
                Select::make('status')
                    ->options(LineItemStatus::class),
                TextInput::make('product_type'),

                TextInput::make('color')
                    ->placeholder('Enter color')
                    ->lazy()
                    ->afterStateHydrated(function ($component, $state, LineItem $record) {
                        $component->state($record->getColor());
                    }),

                TextInput::make('size')
                    ->placeholder('Enter size')
                    ->lazy()
                    ->afterStateHydrated(function ($component, $state, LineItem $record) {
                        $component->state($record->getSize());
                    }),
            ]);
    }
}
