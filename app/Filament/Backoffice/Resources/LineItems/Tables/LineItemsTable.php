<?php

namespace App\Filament\Backoffice\Resources\LineItems\Tables;

use App\Enums\LineItemStatus;
use App\Models\LineItem;
use Filament\Actions\Action;
use Filament\Actions\ViewAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Eloquent\Builder;

class LineItemsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with('order'))
            ->columns([
                TextColumn::make('id'),
                ImageColumn::make('product_image_url')
                    ->label('Image')
                    ->url(fn ($state) => $state)
                    ->openUrlInNewTab()
                    ->extraImgAttributes(['loading' => 'lazy']),
                TextColumn::make('number_portal')
                    ->label('Item TDA number')
                    ->searchable()
                    ->badge(),
                TextColumn::make('status')->badge(),
                TextColumn::make('product_name')
                    ->wrap()
                    ->extraAttributes(['style' => 'width: 322px'])
                    ->suffix(
                        fn (LineItem $record) => $record->productUrl()
                            ? Action::make('view-product-'.$record->id)
                                ->hiddenLabel()
                                ->link()
                                ->color('gray')
                                ->icon(Heroicon::OutlinedArrowTopRightOnSquare)
                                ->url($record->productUrl())
                                ->openUrlInNewTab()
                            : null
                    ),
                TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable(),
                TextColumn::make('quantity')
                    ->numeric()
                    ->alignRight(),
                TextColumn::make('price')
                    ->recordMoney(),
                TextColumn::make('subtotal')
                    ->recordMoney(),
                TextColumn::make('variations')
                    ->state(fn (LineItem $record) => $record->variationsLines())
                    ->bulleted(),
                TextColumn::make('product_type'),
                TextColumn::make('color'),
                TextColumn::make('size'),
                TextColumn::make('personalization'),
                TextColumn::make('supplier.name'),
                TextColumn::make('supplier_order_number')
                    ->badge(),
                TextColumn::make('idea_file_url')
                    ->url(fn (?string $state) => $state)
                    ->extraAttributes(['style' => 'width: 322px'])
                    ->wrap()
                    ->openUrlInNewTab(),
                TextColumn::make('seller_note')
                    ->width(300)
                    ->wrap(),
                TextColumn::make('production_total')
                    ->money('USD', divideBy: 100)
                    ->alignRight(),
                TextColumn::make('packaging_total')
                    ->money('USD', divideBy: 100)
                    ->alignRight(),
                TextColumn::make('shipment.shipping_total')
                    ->label('Shipping total')
                    ->money('USD', divideBy: 100)
                    ->alignRight(),
                TextColumn::make('shipment.tracking_number')
                    ->label('Tracking number')
                    ->badge(),
                TextColumn::make('shipment.carrier.name')
                    ->label('Carrier'),
                TextColumn::make('shipment.thirdPartyLogistic.name')
                    ->label('3PL'),
                TextColumn::make('shipment.packer')
                    ->label('Packer'),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->options(LineItemStatus::class),
            ])
            ->recordActions([
                ViewAction::make(),
                // EditAction::make(),
            ])
            ->toolbarActions([]);
    }
}
