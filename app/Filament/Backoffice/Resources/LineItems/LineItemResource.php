<?php

namespace App\Filament\Backoffice\Resources\LineItems;

use App\Filament\Backoffice\Resources\LineItems\Pages\CreateLineItem;
use App\Filament\Backoffice\Resources\LineItems\Pages\EditLineItem;
use App\Filament\Backoffice\Resources\LineItems\Pages\ListLineItems;
use App\Filament\Backoffice\Resources\LineItems\Pages\ViewLineItem;
use App\Filament\Backoffice\Resources\LineItems\Schemas\LineItemForm;
use App\Filament\Backoffice\Resources\LineItems\Schemas\LineItemInfolist;
use App\Filament\Backoffice\Resources\LineItems\Tables\LineItemsTable;
use App\Filament\TranslatableResource;
use App\Models\LineItem;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class LineItemResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = LineItem::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBars2;

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?int $navigationSort = 30;

    protected static ?string $modelLabel = 'Item';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Schema $schema): Schema
    {
        return LineItemForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return LineItemInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return LineItemsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ShipmentRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListLineItems::route('/'),
            'create' => CreateLineItem::route('/create'),
            'view' => ViewLineItem::route('/{record}'),
            'edit' => EditLineItem::route('/{record}/edit'),
        ];
    }
}
