<?php

namespace App\Filament\Backoffice\Resources\LineItems\Pages;

use App\Filament\Backoffice\Resources\LineItems\LineItemResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditLineItem extends EditRecord
{
    protected static string $resource = LineItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $this->record->setColor($data['color']);
        unset($data['color']);

        $this->record->setSize($data['size']);
        unset($data['size']);

        return $data;
    }
}
