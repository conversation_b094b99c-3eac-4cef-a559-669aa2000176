<?php

namespace App\Filament\Backoffice\Resources\LineItems\Pages;

use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Backoffice\Resources\LineItems\LineItemResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewLineItem extends ViewRecord
{
    protected static string $resource = LineItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make(),
            EditAction::make(),
        ];
    }
}
