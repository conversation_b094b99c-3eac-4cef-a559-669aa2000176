<?php

namespace App\Filament\Backoffice\Resources\LineItems\RelationManagers;

use App\Enums\ShipmentStatus;
use App\Filament\Backoffice\Resources\Shipments\Tables\ShipmentsTable;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Table;

class ShipmentRelationManager extends RelationManager
{
    protected static string $relationship = 'shipment';

    protected static ?string $recordTitleAttribute = 'tracking_number';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('carrier_id')
                    ->label('Carrier')
                    ->relationship('carrier', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->placeholder('Select carrier'),
                TextInput::make('tracking_number')
                    ->label('Tracking Number')
                    ->required()
                    ->maxLength(128),
                Select::make('status')
                    ->options(ShipmentStatus::class)
                    ->required()
                    ->default(ShipmentStatus::Pending),
                DateTimePicker::make('shipped_at'),
                DateTimePicker::make('expected_delivery_date')
                    ->date(),
                DateTimePicker::make('delivered_at'),
                TextInput::make('weight')
                    ->numeric()
                    ->suffix('g'),
                TextInput::make('height')
                    ->numeric()
                    ->suffix('cm'),
                TextInput::make('length')
                    ->numeric()
                    ->suffix('cm'),
                TextInput::make('width')
                    ->numeric()
                    ->suffix('cm'),
                TextInput::make('shipping_total')
                    ->numeric()
                    ->prefix('$')
                    ->step(0.01)
                    ->formatStateUsing(fn ($state) => $state ? $state / 100 : null)
                    ->dehydrateStateUsing(fn ($state) => $state ? $state * 100 : null),
                TextInput::make('currency_code')
                    ->default('USD')
                    ->maxLength(6),
            ]);
    }

    public function table(Table $table): Table
    {
        return ShipmentsTable::configure($table)
            ->minimal();
    }
}
