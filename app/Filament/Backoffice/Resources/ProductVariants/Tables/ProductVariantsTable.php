<?php

namespace App\Filament\Backoffice\Resources\ProductVariants\Tables;

use App\Models\Idea;
use App\Models\ProductVariant;
use Filament\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ProductVariantsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function ($query) {
                return $query->with(['optionValues.option', 'idea', 'productType']);
            })
            ->columns([
                TextColumn::make('sku')
                    ->label('SKU')
                    ->badge()
                    ->copyable()
                    ->searchable(),
                TextColumn::make('vid')
                    ->label('VID')
                    ->badge()
                    ->copyable()
                    ->searchable(),
                TextColumn::make('idea.name'),
                TextColumn::make('productType.name'),
                TextColumn::make('attributes')
                    ->label('Attributes')
                    ->state(fn (ProductVariant $record) => $record->attributesLines()),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('product_type_id')
                    ->label('Product Type')
                    ->relationship('productType', 'name')
                    ->preload()
                    ->searchable(),
                SelectFilter::make('idea_id')
                    ->label('Idea')
                    ->relationship('idea', 'uid')
                    ->placeholder('Enter UID to search')
                    ->searchable()
                    ->getSearchResultsUsing(function ($search) {
                        return Idea::query()
                            ->where('uid', 'like', "%$search")
                            ->limit(5)
                            ->get()
                            ->pluck('uid', 'id');
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->recordActions([
                DeleteAction::make()
                    ->hiddenLabel(),
            ])
            ->toolbarActions([]);
    }
}
