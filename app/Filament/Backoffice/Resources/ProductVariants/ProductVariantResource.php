<?php

namespace App\Filament\Backoffice\Resources\ProductVariants;

use App\Filament\Backoffice\Resources\ProductVariants\Pages\CreateProductVariant;
use App\Filament\Backoffice\Resources\ProductVariants\Pages\EditProductVariant;
use App\Filament\Backoffice\Resources\ProductVariants\Pages\ListProductVariants;
use App\Filament\Backoffice\Resources\ProductVariants\Schemas\ProductVariantForm;
use App\Filament\Backoffice\Resources\ProductVariants\Tables\ProductVariantsTable;
use App\Models\ProductVariant;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class ProductVariantResource extends Resource
{
    protected static ?string $model = ProductVariant::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedPuzzlePiece;

    protected static string|UnitEnum|null $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 70;

    public static function getEloquentQuery(): Builder
    {
        if (user()->isManager()) {
            return parent::getEloquentQuery();
        }

        return parent::getEloquentQuery()->whereHas('idea', fn ($query) => $query->where('user_id', user()->id));
    }

    public static function form(Schema $schema): Schema
    {
        return ProductVariantForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ProductVariantsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductVariants::route('/'),
            //            'create' => CreateProductVariant::route('/create'),
            //            'edit' => EditProductVariant::route('/{record}/edit'),
        ];
    }
}
