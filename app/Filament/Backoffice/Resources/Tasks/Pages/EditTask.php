<?php

namespace App\Filament\Backoffice\Resources\Tasks\Pages;

use App\Actions\AddComment;
use App\Actions\ApproveTask;
use App\Actions\MarkTaskAsProcessing;
use App\Actions\RequestRevisionTask;
use App\Filament\Backoffice\Resources\Designs\RequestRevisionAction;
use App\Filament\Backoffice\Resources\Tasks\TaskResource;
use App\Models\Task;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditTask extends EditRecord
{
    protected static string $resource = TaskResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $this->record->load('designs.productType', 'designs.idea.media', 'designs.media');

        return parent::mutateFormDataBeforeFill($data);
    }

    protected function getHeaderActions(): array
    {
        return [
            // Approve
            Action::make('approve')
                ->visible(fn(Task $record) => $record->isSubmitted())
                ->schema([
                    Radio::make('score')
                        ->label('Score')
                        ->hint('Bạn đánh giá thiết kế của designer này như thế nào?')
                        ->helperText('Kết quả đánh giá sẽ không được hiển thị công khai.')
                        ->inline()
                        ->options([
                            1 => '⭐',
                            2 => '⭐⭐',
                            3 => '⭐⭐⭐',
                            4 => '⭐⭐⭐⭐',
                            5 => '⭐⭐⭐⭐⭐',
                        ]),
                    Textarea::make('feedback')
                        ->label('Feedback')
                        ->placeholder('Feedback sẽ không được hiển thị công khai.')
                        ->rows(3)
                        ->columnSpanFull(),
                ])
                ->action(function (Task $task, array $data) {
                    ApproveTask::make()->handle($task, $data['score'], $data['feedback']);

                    Notification::make()
                        ->title('Task approved successfully')
                        ->body('You have successfully approved the task.')
                        ->success()
                        ->send();
                }),

            // Reject
            Action::make('request-revision')
                ->visible(fn(Task $record) => $record->isSubmitted())
                ->color('warning')
                ->schema([
                    Textarea::make('comment')
                        ->required(),
                ])
                ->action(function (Task $record, array $data) {
                    // need revision
                    RequestRevisionTask::make()->handle($record);

                    AddComment::make()->handle($record, $data['comment'], user()->id);

                    Notification::make()
                        ->title('Task rejected successfully')
                        ->success()
                        ->send();
                }),

            DeleteAction::make(),
        ];
    }
}
