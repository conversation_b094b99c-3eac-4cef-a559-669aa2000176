<?php

namespace App\Filament\Backoffice\Resources\Tasks\Pages;

use App\Enums\TaskStatus;
use App\Filament\Backoffice\Resources\Tasks\TaskResource;
use App\Models\Task;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs;
use Illuminate\Database\Eloquent\Builder;

class ListTasks extends ListRecords
{
    protected static string $resource = TaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
//            CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [];

        foreach (TaskStatus::cases() as $status) {
            $tabs[] = Tabs\Tab::make($status->name)
                ->modifyQueryUsing(fn(Builder $query) => $query->where('status', $status))
                ->badge(function () use ($status) {
                    if (in_array($status, [
                        TaskStatus::Todo,
                        TaskStatus::NeedRevision,
                        TaskStatus::Processing,
                        TaskStatus::Submitted,
                    ])) {
                        $count = Task::query()->where('status', $status)
                            ->where('owner_id', user()->id)
                            ->count();

                        return $count > 0 ? $count : null;
                    }
                    return null;
                });
        }

        return $tabs;
    }
}
