<?php

namespace App\Filament\Backoffice\Resources\Tasks\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TasksTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('title'),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('owner.name'),
                TextColumn::make('assignee.name')
                    ->label('Designer'),
                TextColumn::make('designs_count')
                    ->counts('designs')
                    ->label('Designs count')
                    ->badge()
                    ->color('gray'),
                TextColumn::make('updated_at'),
            ])
            ->defaultSort('tasks.id', 'desc')
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make()
                    ->hiddenLabel(),
            ])
            ->toolbarActions([
            ]);
    }
}
