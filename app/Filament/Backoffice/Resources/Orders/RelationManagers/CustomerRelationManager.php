<?php

namespace App\Filament\Backoffice\Resources\Orders\RelationManagers;

use App\Filament\Backoffice\Resources\Customers\CustomerResource;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class CustomerRelationManager extends RelationManager
{
    protected static string $relationship = 'customer';

    protected static ?string $relatedResource = CustomerResource::class;

    public function table(Table $table): Table
    {
        return $table
            ->recordActions([])
            ->minimal();
    }
}
