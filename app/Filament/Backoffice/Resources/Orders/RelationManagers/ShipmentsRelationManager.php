<?php

namespace App\Filament\Backoffice\Resources\Orders\RelationManagers;

use App\Filament\Backoffice\Resources\Shipments\Tables\ShipmentsTable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Table;

class ShipmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'shipments';

    protected static ?string $recordTitleAttribute = 'tracking_number';

    public function form(Schema $schema): Schema
    {
        return $schema;
    }

    public function table(Table $table): Table
    {
        return ShipmentsTable::configure($table)
            ->query(fn () => $this->getOwnerRecord()->shipments())
            ->minimal();
    }
}
