<?php

namespace App\Filament\Backoffice\Resources\Orders\RelationManagers;

use App\Filament\Backoffice\Resources\LineItems\LineItemResource;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class LineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    protected static ?string $relatedResource = LineItemResource::class;

    public function table(Table $table): Table
    {
        return $table->minimal();
    }
}
