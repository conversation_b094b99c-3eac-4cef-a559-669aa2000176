<?php

namespace App\Filament\Backoffice\Resources\Orders\RelationManagers;

use Filament\Actions\EditAction;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ShippingAddressRelationManager extends RelationManager
{
    protected static string $relationship = 'shippingAddress';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('line_one')
                    ->required(),
                TextInput::make('line_two'),
                TextInput::make('city')
                    ->required(),
                TextInput::make('state'),
                TextInput::make('zip')
                    ->required(),
                TextInput::make('country')
                    ->required(),
                TextInput::make('phone'),
                TextInput::make('email')
                    ->email(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('line_one'),
                TextColumn::make('line_two'),
                TextColumn::make('city'),
                TextColumn::make('state'),
                TextColumn::make('zip'),
                TextColumn::make('country'),
                TextColumn::make('phone'),
                TextColumn::make('email'),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                // EditAction::make(),
                EditAction::make()
                    ->modalHeading('Edit Shipping Address')
                    ->modalButton('Save Changes')
                    ->form([
                        TextInput::make('name')->required(),
                        TextInput::make('line_one')->required(),
                        TextInput::make('line_two'),
                        TextInput::make('city')->required(),
                        TextInput::make('state'),
                        TextInput::make('zip')->required(),
                        TextInput::make('country')->required(),
                        TextInput::make('phone'),
                        TextInput::make('email')->email(),
                    ]),
            ])
            ->toolbarActions([
            ]);
    }
}
