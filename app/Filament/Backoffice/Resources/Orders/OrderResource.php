<?php

namespace App\Filament\Backoffice\Resources\Orders;

use App\Filament\Backoffice\Resources\Orders\Pages\CreateOrder;
use App\Filament\Backoffice\Resources\Orders\Pages\EditOrder;
use App\Filament\Backoffice\Resources\Orders\Pages\ListOrders;
use App\Filament\Backoffice\Resources\Orders\Pages\ViewOrder;
use App\Filament\Backoffice\Resources\Orders\Schemas\OrderForm;
use App\Filament\Backoffice\Resources\Orders\Schemas\OrderInfolist;
use App\Filament\Backoffice\Resources\Orders\Tables\OrdersTable;
use App\Filament\TranslatableResource;
use App\Models\Order;
use BackedEnum;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use UnitEnum;

class OrderResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Order::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedShoppingCart;

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?string $recordTitleAttribute = 'number_portal';

    protected static ?int $navigationSort = 1;

    public static function getGloballySearchableAttributes(): array
    {
        return ['order_number', 'number_portal'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string|Htmlable
    {
        return $record->number_portal.'|'.$record->order_number;
    }

    public static function form(Schema $schema): Schema
    {
        return OrderForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return OrderInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return OrdersTable::configure($table)
            ->paginated([100, 200, 500]);
    }

    public static function getRelations(): array
    {
        return [
            RelationGroup::make('', [
                RelationManagers\LineItemsRelationManager::class,
                RelationManagers\ShippingAddressRelationManager::class,
                RelationManagers\ShipmentsRelationManager::class,
                RelationManagers\CustomerRelationManager::class,
            ]),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrders::route('/'),
            'create' => CreateOrder::route('/create'),
            'view' => ViewOrder::route('/{record}'),
            'edit' => EditOrder::route('/{record}/edit'),
        ];
    }
}
