<?php

namespace App\Filament\Backoffice\Resources\Orders\Schemas;

use App\Models\Order;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;

class OrderInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make(__('Pending cancel request'))
                    ->visible(fn (Order $record) => $record->hasPendingCancelRequest())
                    ->icon(Heroicon::ExclamationTriangle)
                    ->iconColor('danger')
                    ->extraAttributes(function (Order $record) {
                        if ($record->hasPendingCancelRequest()) {
                            return [
                                'style' => '
                                    animation: shake 0.5s ease-in-out infinite alternate;
                                    @keyframes shake {
                                        0% { transform: translateX(0); }
                                        25% { transform: translateX(-2px); }
                                        50% { transform: translateX(2px); }
                                        75% { transform: translateX(-1px); }
                                        100% { transform: translateX(1px); }
                                    }
                                ',
                            ];
                        }

                        return [];
                    })
                    ->columns(2)
                    ->schema([
                        TextEntry::make('id')
                            ->state(fn (Order $record) => $record->latestPendingCancelRequest->id)
                            ->url(fn (Order $record) => route('filament.backoffice.resources.order-cancel-requests.view', ['record' => $record->latestPendingCancelRequest->id])),
                        TextEntry::make('reason')
                            ->state(fn (Order $record) => $record->latestPendingCancelRequest->reason),
                    ]),
                Section::make('Order Information')
                    ->headerActions([
                        self::editOrderAction(),
                    ])
                    ->columns(3)
                    ->schema([
                        TextEntry::make('number_portal')
                            ->label('Order TDA number')
                            ->badge()
                            ->copyable()
                            ->copyable(),
                        TextEntry::make('order_number')
                            ->badge()
                            ->copyable(),
                        TextEntry::make('status')
                            ->badge(),
                        TextEntry::make('order_date')
                            ->dateTime(),
                        TextEntry::make('platform'),
                        TextEntry::make('id_platform'),
                        IconEntry::make('is_gift')
                            ->boolean(),
                        IconEntry::make('is_gift_wrapped')
                            ->boolean(),
                        TextEntry::make('currency_code'),
                        TextEntry::make('total')
                            ->label('Total - Tax')
                            ->recordMoney(),
                        /* TextEntry::make('subtotal')
                            ->recordMoney(), */
                        TextEntry::make('shipping_total')
                            ->recordMoney(),
                        TextEntry::make('tax_total')
                            ->numeric(),
                        TextEntry::make('discount_total')
                            ->recordMoney(),
                        TextEntry::make('date_paid')
                            ->date(),
                        TextEntry::make('payment_method'),
                        TextEntry::make('expected_ship_date')
                            ->dateTime(),
                        TextEntry::make('seller.name')
                            ->label('Seller Name'),
                        TextEntry::make('seller_confirmed_at')
                            ->dateTime(),
                        TextEntry::make('created_at')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->dateTime(),
                        TextEntry::make('getTaxLabel')
                            ->label('Tax/VAT Label')
                            ->state(fn (Order $record) => $record->getTaxLabel()),
                        TextEntry::make('getTaxMessage')
                            ->label('Tax/VAT Message')
                            ->state(fn (Order $record) => $record->getTaxMessage()),
                        TextEntry::make('customer_note')
                            ->label('Customer Note'),
                        TextEntry::make('gift_message')
                            ->label('Gift Message'),
                    ]),
            ]);
    }

    public static function editOrderAction(): Action
    {
        return Action::make('editOrderInformation')
            ->label('')
            ->icon('heroicon-o-pencil-square')
            ->extraAttributes([
                'class' => 'ml-auto flex items-center gap-1 justify-center',
            ])
            ->modalHeading('Edit Order Information')
            ->modalButton('Save Changes')
            ->fillForm(function (Order $record) {
                return $record->toArray();
            })
            ->form([
                Textarea::make('customer_note')
                    ->rows(3)
                    ->placeholder('Add any notes here'),
                Textarea::make('gift_message')
                    ->rows(3)
                    ->placeholder('Add a gift message here'),
            ])
            ->action(function (array $data, Order $record) {
                $record->update($data);
            });
    }
}
