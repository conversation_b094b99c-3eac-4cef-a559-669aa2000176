<?php

namespace App\Filament\Backoffice\Resources\Orders\Schemas;

use App\Enums\OrderStatus;
use App\Enums\Platform;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('order_number')
                    ->required(),
                DateTimePicker::make('order_date'),
                Select::make('platform')
                    ->options(Platform::class),
                TextInput::make('id_platform')
                    ->numeric(),
                TextInput::make('shop_id_platform')
                    ->numeric(),
                Select::make('status')
                    ->options(OrderStatus::class)
                    ->default('draft')
                    ->required(),
                Toggle::make('is_gift')
                    ->required(),
                Toggle::make('is_gift_wrapped')
                    ->required(),
                Textarea::make('gift_message')
                    ->columnSpanFull(),
                TextInput::make('currency_code'),
                TextInput::make('total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('subtotal')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('shipping_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('tax_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('discount_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                DatePicker::make('date_paid'),
                TextInput::make('payment_method'),
                DateTimePicker::make('expected_ship_date'),
                DateTimePicker::make('seller_confirmed_at'),
                Select::make('customer_id')
                    ->relationship('customer', 'name'),
                Select::make('seller_id')
                    ->relationship('seller', 'name')
                    ->required(),
                Select::make('backup_seller_id')
                    ->relationship('backupSeller', 'name'),
                Textarea::make('customer_note')
                    ->columnSpanFull(),
            ]);
    }
}
