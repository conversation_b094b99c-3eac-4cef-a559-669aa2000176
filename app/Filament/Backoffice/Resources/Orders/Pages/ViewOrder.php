<?php

namespace App\Filament\Backoffice\Resources\Orders\Pages;

use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Backoffice\Resources\Orders\OrderResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewOrder extends ViewRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make(),
            //            EditAction::make(),
        ];
    }
}
