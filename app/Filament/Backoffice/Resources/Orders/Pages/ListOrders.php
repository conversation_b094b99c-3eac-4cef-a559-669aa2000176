<?php

namespace App\Filament\Backoffice\Resources\Orders\Pages;

use App\Enums\OrderStatus;
use App\Filament\Backoffice\Resources\Orders\ImportSuppliersShipmentAction;
use App\Filament\Backoffice\Resources\Orders\ImportTrackingShipmentAction;
use App\Filament\Backoffice\Resources\Orders\OrderResource;
use App\Models\Order;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs\Tab;
use Filament\Support\Enums\Width;
use Filament\Support\Icons\Heroicon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Contracts\Support\Htmlable;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    public function getTitle(): string|Htmlable
    {
        return parent::getTitle(); // TODO: Change the autogenerated stub
    }

    protected function getHeaderActions(): array
    {
        return [
            ImportSuppliersShipmentAction::make('import-line-items-suppliers')
                ->label(__('Supplier data import'))
                ->icon(Heroicon::OutlinedArrowUpTray)
                ->outlined()
                ->color('gray')
                ->slideOver()
                ->modalWidth(Width::SevenExtraLarge),
            ImportTrackingShipmentAction::make('import-tracking-shipment')
                ->label(__('Tracking data import'))
                ->icon(Heroicon::OutlinedTruck)
                ->outlined()
                ->color('gray')
                ->slideOver()
                ->modalWidth(Width::SevenExtraLarge),
        ];
    }

    public function getDefaultActiveTab(): string|int|null
    {
        return 'all';
    }

    public function getTabs(): array
    {
        $tabs = [];

        $tabs['all'] = Tab::make(__('All'));

        foreach (
            [
                OrderStatus::New,
                OrderStatus::Processing,
                OrderStatus::PartialShipped,
                OrderStatus::Shipped,
                OrderStatus::Cancelled,
                OrderStatus::Draft,
            ] as $case
        ) {
            $tabs[$case->value] = Tab::make($case->getLabel())
                ->query(fn (Builder $query) => $query->where('status', $case))
                ->badgeColor($case->getColor())
                ->badge(
                    in_array($case, [
                        OrderStatus::New,
                        OrderStatus::Processing,
                        OrderStatus::PartialShipped,
                        OrderStatus::Draft,
                    ]) ? Order::where('status', $case)->count() : null
                );
        }

        /*         $tabs['all'] = Tab::make(__('All')); */

        return $tabs;
    }
}
