<?php

namespace App\Filament\Backoffice\Resources\Orders;

use App\Actions\ImportLineItemsSuppliers;
use App\Actions\ParseCsvFile;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\View;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class ImportSuppliersShipmentAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->steps([
            Step::make('upload-file')
                ->schema([
                    FileUpload::make('supplier_shipment_file')
                        ->label('Suppliers shipment file')
                        ->required()
                        ->acceptedFileTypes(['text/csv'])
                        ->directory('imports/supplier-shipments')
                        ->maxSize(1024 * 5)
                        ->helperText('Only CSV files are allowed.'), // 5 MB
                    TextEntry::make('Example csv file')
                        ->state('https://docs.google.com/spreadsheets/d/1PzGiuj_1SOnhq3d-orPH74zLxuFV_vay6GgT_x6JPJ8/edit?usp=sharing')
                        ->url('https://docs.google.com/spreadsheets/d/1PzGiuj_1SOnhq3d-orPH74zLxuFV_vay6GgT_x6JPJ8/edit?usp=sharing')
                        ->openUrlInNewTab(),
                ])
                ->afterValidation(function (Get $get) {
                    if (! $get('supplier_shipment_file')) {
                        throw new Halt(__('Please upload a suppliers shipment file.'));
                    }
                    $importAction = ImportLineItemsSuppliers::make();

                    try {
                        $importAction->validate(Arr::first($get('supplier_shipment_file'))->getRealPath());
                    } catch (\InvalidArgumentException $e) {
                        Notification::make()
                            ->title(__('Validation Error'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        throw new Halt($e->getMessage());
                    }
                }),
            Step::make('confirm-data')
                ->schema([
                    View::make('basic-table')
                        ->viewData(function (Get $get) {
                            if (! $get('supplier_shipment_file')) {
                                return ['rows' => []];
                            }

                            $rows = ParseCsvFile::make()->handle(Arr::first($get('supplier_shipment_file'))->getRealPath());

                            return [
                                'rows' => $rows,
                            ];
                        }),

                ]),

        ])->action(function (array $data) {
            if (! isset($data['supplier_shipment_file'])) {
                throw new Halt(__('No file uploaded.'));
            }

            ImportLineItemsSuppliers::make()->handle(
                Storage::path($data['supplier_shipment_file'])
            );

            Notification::make()
                ->title(__('Import Successful'))
                ->body(__('The suppliers shipment has been successfully imported.'))
                ->success()
                ->send();
        });
    }
}
