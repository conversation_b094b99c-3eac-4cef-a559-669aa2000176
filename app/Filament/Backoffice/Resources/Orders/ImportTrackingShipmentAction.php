<?php

namespace App\Filament\Backoffice\Resources\Orders;

use App\Actions\ImportTrackingShipment;
use App\Actions\ParseCsvFile;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\View;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class ImportTrackingShipmentAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->steps([
            Step::make('upload-file')
                ->schema([
                    FileUpload::make('tracking_shipment_file')
                        ->label('Tracking shipment file')
                        ->required()
                        ->acceptedFileTypes(['text/csv'])
                        ->directory('imports/tracking-shipments')
                        ->maxSize(1024 * 5) // 5 MB
                        ->helperText('Upload a CSV file with tracking and carrier information for line items.'),
                    TextEntry::make('Example csv file')
                        ->state('https://docs.google.com/spreadsheets/d/1XOpNyYudKsdnX4w3H58yMcD_AklyHF4T-YzB1rqA1sk/edit?usp=sharing')
                        ->url('https://docs.google.com/spreadsheets/d/1XOpNyYudKsdnX4w3H58yMcD_AklyHF4T-YzB1rqA1sk/edit?usp=sharing')
                        ->openUrlInNewTab(),
                ])
                ->afterValidation(function (Get $get) {
                    if (! $get('tracking_shipment_file')) {
                        throw new Halt('Please upload a tracking shipment file.');
                    }
                    $importAction = ImportTrackingShipment::make();

                    try {
                        $importAction->validate(Arr::first($get('tracking_shipment_file'))->getRealPath());
                    } catch (\InvalidArgumentException $e) {
                        Notification::make()
                            ->title('Validation Error')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        throw new Halt($e->getMessage());
                    }
                }),
            Step::make('confirm-data')
                ->schema([
                    View::make('basic-table')
                        ->viewData(function (Get $get) {
                            if (! $get('tracking_shipment_file')) {
                                return [
                                    'rows' => [],
                                ];
                            }

                            $rows = ParseCsvFile::make()->handle(Arr::first($get('tracking_shipment_file'))->getRealPath());

                            return [
                                'rows' => $rows,
                            ];
                        }),

                ]),

        ])->action(function (array $data) {
            if (! isset($data['tracking_shipment_file'])) {
                throw new Halt(__('No file uploaded.'));
            }

            ImportTrackingShipment::make()->handle(
                Storage::path($data['tracking_shipment_file'])
            );

            Notification::make()
                ->title(__('Import Successful'))
                ->body(__('The tracking shipment has been successfully imported.'))
                ->success()
                ->send();
        });
    }
}
