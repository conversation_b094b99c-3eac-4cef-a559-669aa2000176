<?php

namespace App\Filament\Backoffice\Resources\Orders\Tables;

use App\Filament\Backoffice\Exports\LineItemsExporter;
use App\Filament\Backoffice\Exports\PrepareOrderToLineItemExportCsv;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;
use Filament\Actions\BulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ExportBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TimePicker;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->striped()
            ->modifyQueryUsing(function (Builder $query) {
                return $query->with('cancelRequests');
            })
            ->recordClasses(function (Order $order) {
                return match ($order->hasPendingCancelRequest()) {
                    true => 'bg-color-danger',
                    default => null,
                };
            })
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('number_portal')
                    ->label('TDA number')
                    ->badge()
                    ->copyable()
                    ->grow(false)
                    ->searchable(),
                TextColumn::make('order_number')
                    ->badge()
                    ->copyable()
                    ->grow(false)
                    ->searchable(),
                TextColumn::make('lineItems.sku')
                    ->label('Items')
                    ->listWithLineBreaks(),
                TextColumn::make('lineItems.status')
                    ->label('')
                    ->badge()
                    ->searchable()
                    ->listWithLineBreaks(),
                TextColumn::make('order_date')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Imported date')
                    ->dateTime(),
                TextColumn::make('seller_confirmed_at')
                    ->label('Confirmed at')
                    ->sortable()
                    ->dateTime(),
                TextColumn::make('status')
                    ->badge(),
                /* TextColumn::make('subtotal')->recordMoney(), */
                TextColumn::make('total')
                    ->label('Total - Tax')
                    ->recordMoney(),
                TextColumn::make('etsyShop.username')
                    ->label('Etsy shop')
                    ->copyable()
                    ->badge()
                    ->color('gray'),
                TextColumn::make('seller.name'),
                TextColumn::make('backupSeller.name')
                    ->label('B/Seller'),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Filter::make('order-filters')
                    ->columns(2)
                    ->columnSpanFull()
                    ->schema([
                        DatePicker::make('created_at')->label('Import date')
                            ->columnSpan(2),

                        DateTimePicker::make('confirmed_from')
                            ->label('Confirmed From'),

                        DateTimePicker::make('confirmed_until')
                            ->label('Confirmed To'),

                        TagsInput::make('confirmed_dates')
                            ->label('Confirmed Dates')
                            ->placeholder('e.g., 2025-05-24 or 05-24')
                            ->suggestions(
                                collect(range(0, 8))->map(function ($i) {
                                    return Carbon::today()->subDays($i)->toDateString();
                                })->toArray()
                            )
                            ->hint('YYYY-MM-DD or MM-DD')
                            ->columnSpan(2),

                        TimePicker::make('confirmed_time_from')
                            ->label('Confirmed Time From')
                            ->seconds(false),
                        TimePicker::make('confirmed_time_until')
                            ->label('Confirmed Time To')
                            ->seconds(false),
                    ])
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if (! empty($data['created_at'])) {
                            $indicators['created_at'] = 'Imported at '.$data['created_at'];
                        }

                        if (! empty($data['confirmed_from']) && ! empty($data['confirmed_until'])) {
                            $indicators['confirmed'] = 'Confirmed from '.$data['confirmed_from'].' to '.$data['confirmed_until'];
                        }

                        if (! empty($data['confirmed_dates'])) {
                            $indicators['confirmed_dates'] = 'Confirmed on: '.implode(', ', $data['confirmed_dates']);
                        }

                        if (! empty($data['confirmed_time_from']) || ! empty($data['confirmed_time_until'])) {
                            $from = $data['confirmed_time_from'] ?? '—';
                            $to = $data['confirmed_time_until'] ?? '—';
                            $indicators['confirmed_time'] = "Confirmed time: {$from} → {$to}";
                        }

                        return $indicators;
                    })
                    ->query(function (Builder $query, array $data) {
                        if (! empty($data['created_at'])) {
                            $query->whereDate('created_at', $data['created_at']);
                        }

                        if (! empty($data['confirmed_from']) && ! empty($data['confirmed_until'])) {
                            $userTz = config('app.user_timezone');
                            $dbTz = config('app.timezone');

                            $start = Carbon::parse($data['confirmed_from'], $userTz)->setTimezone($dbTz);
                            $end = Carbon::parse($data['confirmed_until'], $userTz)->setTimezone($dbTz);

                            $query->whereBetween('seller_confirmed_at', [$start, $end]);
                        }

                        if (! empty($data['confirmed_dates']) && is_array($data['confirmed_dates'])) {
                            $query->where(function ($q) use ($data) {
                                foreach ($data['confirmed_dates'] as $input) {
                                    // YYYY-MM-DD
                                    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $input)) {
                                        $q->orWhereDate('seller_confirmed_at', $input);
                                    }
                                    // MM-DD
                                    elseif (preg_match('/^\d{2}-\d{2}$/', $input)) {
                                        [$month, $day] = explode('-', $input);
                                        $q->orWhere(function ($subQ) use ($month, $day) {
                                            $subQ->whereMonth('seller_confirmed_at', $month)
                                                ->whereDay('seller_confirmed_at', $day);
                                        });
                                    }

                                }
                            });
                        }

                        $timeFrom = $data['confirmed_time_from'] ?? null; // HH:MM
                        $timeUntil = $data['confirmed_time_until'] ?? null;

                        $userTz = config('app.user_timezone');
                        $dbTz = config('app.timezone');

                        if ($timeFrom || $timeUntil) {
                            $tf = $timeFrom
                                ? Carbon::parse(strlen($timeFrom) === 5 ? $timeFrom.':00' : $timeFrom, $userTz)
                                    ->setTimezone($dbTz)->format('H:i:s')
                                : null;

                            $tu = $timeUntil
                                ? Carbon::parse(strlen($timeUntil) === 5 ? $timeUntil.':00' : $timeUntil, $userTz)
                                    ->setTimezone($dbTz)->format('H:i:s')
                                : null;

                            $query->where(function ($q) use ($tf, $tu) {
                                if ($tf && ! $tu) {
                                    $q->whereTime('seller_confirmed_at', '>=', $tf);

                                    return;
                                }
                                if ($tu && ! $tf) {
                                    $q->whereTime('seller_confirmed_at', '<=', $tu);

                                    return;
                                }
                                if ($tf && $tu) {
                                    if ($tf <= $tu) {
                                        $q->whereTime('seller_confirmed_at', '>=', $tf)
                                            ->whereTime('seller_confirmed_at', '<=', $tu);
                                    } else {
                                        $q->where(function ($q2) use ($tf, $tu) {
                                            $q2->whereTime('seller_confirmed_at', '>=', $tf)
                                                ->orWhereTime('seller_confirmed_at', '<=', $tu);
                                        });
                                    }
                                }
                            });
                        }

                        return $query;
                    }),

                Filter::make('line_item_status')
                    ->label('Line Item Status')
                    ->form([
                        Select::make('item_status')
                            ->options([
                                'new' => 'New',
                                'in-production' => 'In Production',
                                'packed' => 'Packed',
                                'shipped' => 'Shipped',
                                'canceled' => 'Canceled',
                            ])
                            ->placeholder('All')
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (! empty($data['item_status'])) {
                            $query->whereHas('lineItems', function ($q) use ($data) {
                                $q->where('status', $data['item_status']);
                            });
                        }
                    })
                    ->indicateUsing(function (array $data) {
                        return ! empty($data['item_status'])
                            ? ['line_item_status' => 'Item Status: '.ucfirst($data['item_status'])]
                            : [];
                    }),

            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                ExportBulkAction::make('bulk-export-orders')
                    ->exporter(LineItemsExporter::class)
                    ->job(PrepareOrderToLineItemExportCsv::class) // If you don't know what is this, do not delete it.
                    ->label(__('Bulk export orders'))
                    ->tooltip(__('Export orders for fulfillment'))
                    ->columnMapping(false)
                    ->icon(Heroicon::OutlinedArrowDownTray),

                BulkAction::make('bulk-assign-sellers')
                    ->label(__('Assign backup sellers'))
                    ->outlined()
                    ->color('gray')
                    ->icon(Heroicon::OutlinedUser)
                    ->schema([
                        Select::make('backup_seller_id')
                            ->options(
                                fn () => User::seller()->pluck('name', 'id')->toArray()
                            )
                            ->preload(),
                    ])
                    ->action(function (Collection $records, array $data) {
                        $records->toQuery()->update(['backup_seller_id' => $data['backup_seller_id']]);

                        Notification::make()
                            ->title(__('Backup seller assigned successfully'))
                            ->success()
                            ->send();
                    }),
            ]);
    }
}
