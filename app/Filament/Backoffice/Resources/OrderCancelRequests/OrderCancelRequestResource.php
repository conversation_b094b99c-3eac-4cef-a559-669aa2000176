<?php

namespace App\Filament\Backoffice\Resources\OrderCancelRequests;

use App\Enums\OrderCancelRequestStatus;
use App\Filament\Backoffice\Resources\OrderCancelRequests\Pages\ListOrderCancelRequests;
use App\Filament\Backoffice\Resources\OrderCancelRequests\Pages\ViewOrderCancelRequest;
use App\Filament\Backoffice\Resources\OrderCancelRequests\Schemas\OrderCancelRequestInfolist;
use App\Filament\TranslatableResource;
use App\Models\OrderCancelRequest;
use BackedEnum;
use Filament\Actions\ViewAction;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Colors\Color;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use UnitEnum;

class OrderCancelRequestResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = OrderCancelRequest::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedHandRaised;

    protected static ?string $navigationLabel = 'Cancel Requests';

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?int $navigationSort = 50;

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::pending()->count() ?: null;
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return Color::Amber;
    }

    public static function infolist(Schema $schema): Schema
    {
        return OrderCancelRequestInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('order.number_portal')
                    ->label('Order TDA')
                    ->badge()
                    ->color('gray')
                    ->searchable(),
                TextColumn::make('order.order_number')
                    ->label('Order Number')
                    ->badge()
                    ->color('gray')
                    ->searchable(),
                TextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('reason')
                    ->label('Reason')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        return strlen($state) > 50 ? $state : null;
                    }),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('created_at')
                    ->label('Request Date')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('processedBy.name')
                    ->label('Processed By')
                    ->placeholder('—'),
                TextColumn::make('processed_at')
                    ->label('Processing Date')
                    ->dateTime()
                    ->placeholder('—'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(OrderCancelRequestStatus::class),
            ])
            ->recordActions([
                ViewAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrderCancelRequests::route('/'),
            'view' => ViewOrderCancelRequest::route('/{record}'),
        ];
    }
}
