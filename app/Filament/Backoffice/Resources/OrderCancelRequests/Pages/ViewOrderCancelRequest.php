<?php

namespace App\Filament\Backoffice\Resources\OrderCancelRequests\Pages;

use App\Actions\ProcessOrderCancelRequest;
use App\Filament\Backoffice\Resources\OrderCancelRequests\OrderCancelRequestResource;
use App\Models\OrderCancelRequest;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewOrderCancelRequest extends ViewRecord
{
    protected static string $resource = OrderCancelRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('approve')
                ->label('Approve Request')
                ->icon('heroicon-o-check-circle')
                ->visible(fn (OrderCancelRequest $record) => $record->isPending())
                ->schema([
                    Textarea::make('feedback_note')
                        ->label('Approval Note (Optional)')
                        ->placeholder('Add any notes about the approval...')
                        ->rows(3),
                ])
                ->requiresConfirmation()
                ->modalHeading(__('Approve Cancel Request'))
                ->modalDescription(__('Are you sure you want to approve this cancel request? This will cancel the order and cannot be undone.'))
                ->action(function (array $data, OrderCancelRequest $record) {
                    try {
                        ProcessOrderCancelRequest::make()->handle(
                            $record,
                            auth()->user(),
                            true,
                            $data['feedback_note'] ?? null
                        );

                        Notification::make()
                            ->title(__('Cancel request approved successfully'))
                            ->body(__('The order has been cancelled.'))
                            ->success()
                            ->send();

                        $this->redirect(static::getResource()::getUrl('index'));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title(__('Error approving cancel request'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Action::make('reject')
                ->label('Reject Request')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn (OrderCancelRequest $record) => $record->isPending())
                ->schema([
                    Textarea::make('feedback_note')
                        ->label('Rejection Reason')
                        ->placeholder('Please provide a reason for rejecting this cancel request...')
                        ->required()
                        ->rows(3),
                ])
                ->requiresConfirmation()
                ->modalHeading(__('Reject Cancel Request'))
                ->modalDescription(__('Please provide a reason for rejecting this cancel request.'))
                ->action(function (array $data, OrderCancelRequest $record) {
                    try {
                        ProcessOrderCancelRequest::make()->handle(
                            $record,
                            auth()->user(),
                            false,
                            $data['feedback_note']
                        );

                        Notification::make()
                            ->title(__('Cancel request rejected'))
                            ->body(__('The seller has been notified of the rejection.'))
                            ->success()
                            ->send();

                        $this->redirect(static::getResource()::getUrl('index'));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title(__('Error rejecting cancel request'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
}
