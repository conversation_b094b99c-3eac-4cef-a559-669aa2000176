<?php

namespace App\Filament\Backoffice\Resources\OrderCancelRequests\Schemas;

use App\Models\OrderCancelRequest;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class OrderCancelRequestInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make(__('Request Details'))
                    ->schema([
                        TextEntry::make('order.number_portal')
                            ->label('Order TDA')
                            ->badge()
                            ->color('gray'),
                        TextEntry::make('order.order_number')
                            ->label('Order Number (Platform)')
                            ->badge()
                            ->color('gray'),
                        TextEntry::make('seller.name')
                            ->label('Seller'),
                        TextEntry::make('reason')
                            ->label('Cancellation Reason')
                            ->columnSpanFull(),
                        TextEntry::make('status')
                            ->badge(),
                        TextEntry::make('created_at')
                            ->label('Request Date')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Section::make('Processing Details')
                    ->schema([
                        TextEntry::make('processedBy.name')
                            ->label('Processed By')
                            ->placeholder('Not processed yet'),
                        TextEntry::make('processed_at')
                            ->label('Processing Date')
                            ->dateTime()
                            ->placeholder('Not processed yet'),
                        TextEntry::make('feedback_note')
                            ->label('Feedback Note')
                            ->columnSpanFull()
                            ->placeholder('No feedback provided'),
                    ])
                    ->columns(2)
                    ->visible(fn (OrderCancelRequest $record) => $record->isProcessed()),

                Section::make(__('Order Information'))
                    ->schema([
                        TextEntry::make('order.status')
                            ->label('Order Status')
                            ->badge(),
                        TextEntry::make('order.total')
                            ->label('Order Total')
                            ->money(fn (OrderCancelRequest $record) => $record->order->currency_code, divideBy: 100),
                        TextEntry::make('order.order_date')
                            ->label('Order Date')
                            ->dateTime(),
                        TextEntry::make('order.customer.name')
                            ->label('Customer'),
                        RepeatableEntry::make('order.lineItems')
                            ->label('Line Items')
                            ->schema([
                                ImageEntry::make('product_image_url')
                                    ->label('Image')
                                    ->imageSize(60)
                                    ->grow(false),
                                TextEntry::make('product_name')
                                    ->label('Product name')
                                    ->weight('medium')
                                    ->wrap(),
                                TextEntry::make('sku')
                                    ->label('SKU')
                                    ->badge()
                                    ->color('gray'),
                                TextEntry::make('status')
                                    ->badge(),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }
}
