<?php

namespace App\Filament\Backoffice\Resources\Shipments;

use App\Filament\Backoffice\Resources\Orders\RelationManagers\LineItemsRelationManager;
use App\Filament\Backoffice\Resources\Shipments\Pages\CreateShipment;
use App\Filament\Backoffice\Resources\Shipments\Pages\EditShipment;
use App\Filament\Backoffice\Resources\Shipments\Pages\ListShipments;
use App\Filament\Backoffice\Resources\Shipments\Pages\ViewShipment;
use App\Filament\Backoffice\Resources\Shipments\Schemas\ShipmentForm;
use App\Filament\Backoffice\Resources\Shipments\Tables\ShipmentsTable;
use App\Filament\TranslatableResource;
use App\Models\Shipment;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class ShipmentResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Shipment::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedTruck;

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?int $navigationSort = 30;

    public static function form(Schema $schema): Schema
    {
        return ShipmentForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ShipmentsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            LineItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListShipments::route('/'),
            'create' => CreateShipment::route('/create'),
            'view' => ViewShipment::route('/{record}'),
            'edit' => EditShipment::route('/{record}/edit'),
        ];
    }
}
