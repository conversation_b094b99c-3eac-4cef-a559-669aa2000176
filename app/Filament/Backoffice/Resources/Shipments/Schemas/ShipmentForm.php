<?php

namespace App\Filament\Backoffice\Resources\Shipments\Schemas;

use App\Enums\ShipmentStatus;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ShipmentForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->disabled()
            ->components([
                Section::make('Tracking Information')
                    ->schema([
                        Select::make('third_party_logistic_id')
                            ->label('3PL Provider')
                            ->relationship('thirdPartyLogistic', 'name')
                            ->searchable()
                            ->preload()
                            ->placeholder('Select 3PL provider'),
                        Select::make('carrier_id')
                            ->label('Carrier')
                            ->relationship('carrier', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->placeholder('Select carrier'),
                        TextInput::make('tracking_number')
                            ->label('Tracking Number')
                            ->required()
                            ->maxLength(128),
                        Select::make('status')
                            ->options(ShipmentStatus::class)
                            ->required()
                            ->default(ShipmentStatus::Pending),
                        TextInput::make('packer')
                            ->label('Packer')
                            ->maxLength(16)
                            ->placeholder('e.g., TDA, SUP'),
                    ])
                    ->columns(5),

                Section::make('Timeline')
                    ->schema([
                        DateTimePicker::make('shipped_at')
                            ->label('Shipped At'),
                        DateTimePicker::make('expected_delivery_date')
                            ->label('Expected Delivery Date')
                            ->date(),
                        DateTimePicker::make('delivered_at')
                            ->label('Delivered At'),
                    ])
                    ->columns(3),

                Section::make('Physical Properties')
                    ->schema([
                        TextInput::make('weight')
                            ->label('Weight (grams)')
                            ->numeric()
                            ->suffix('g'),
                        TextInput::make('height')
                            ->label('Height (cm)')
                            ->numeric()
                            ->suffix('cm'),
                        TextInput::make('length')
                            ->label('Length (cm)')
                            ->numeric()
                            ->suffix('cm'),
                        TextInput::make('width')
                            ->label('Width (cm)')
                            ->numeric()
                            ->suffix('cm'),
                    ])
                    ->columns(4),

                Section::make('Cost Information')
                    ->schema([
                        TextInput::make('shipping_total')
                            ->label('Shipping Total')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->formatStateUsing(fn ($state) => $state ? $state / 100 : null)
                            ->dehydrateStateUsing(fn ($state) => $state ? $state * 100 : null),
                        TextInput::make('currency_code')
                            ->label('Currency Code')
                            ->default('USD')
                            ->maxLength(6),
                        TextInput::make('label_url')
                            ->label('Label URL')
                            ->url()
                            ->maxLength(512),
                    ])
                    ->columns(3),
            ]);
    }
}
