<?php

namespace App\Filament\Backoffice\Resources\Shipments\Tables;

use App\Enums\ShipmentStatus;
use App\Models\Shipment;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Eloquent\Builder;

class ShipmentsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['carrier', 'thirdPartyLogistic']))
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Shipment Status')
                    ->badge(),
                TextColumn::make('thirdPartyLogistic.name')
                    ->label('3PL'),
                TextColumn::make('tracking_number')
                    ->label('Tracking Number')
                    ->searchable()
                    ->copyable()
                    ->badge()
                    ->url(fn ($record) => $record->carrier?->tracking_url ? str_replace('{tracking_number}', $record->tracking_number, $record->carrier->tracking_url) : null)
                    ->openUrlInNewTab(),
                TextColumn::make('carrier.name')
                    ->label('Carrier'),
                TextColumn::make('packer')
                    ->label('Packer'),
                TextColumn::make('line_items_count')
                    ->label('Line items count')
                    ->counts('lineItems'),
                TextColumn::make('weight')
                    ->label('Weight')
                    ->suffix('g'),
                TextColumn::make('size')
                    ->suffix('cm'),
                //                TextColumn::make('shipping_total')
                //                    ->recordMoney(),
                TextColumn::make('shipped_at')
                    ->label('Shipped At')
                    ->dateTime(),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort(Shipment::make()->qualifyColumn('id'), 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->options(ShipmentStatus::class),
                SelectFilter::make('thirdPartyLogistic')
                    ->relationship('thirdPartyLogistic', 'name'),
            ])
            ->recordActions([
                ViewAction::make(),
            ])
            ->toolbarActions([]);
    }
}
