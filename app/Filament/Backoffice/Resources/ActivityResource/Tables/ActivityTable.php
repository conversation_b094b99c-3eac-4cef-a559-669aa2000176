<?php

namespace App\Filament\Backoffice\Resources\ActivityResource\Tables;

use App\Models\User;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ActivityTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->grow(false),

                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                TextColumn::make('log_name')
                    ->label('Log Name'),

                TextColumn::make('description')
                    ->label('Description')
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            'created' => 'success',
                            'updated' => 'warning',
                            'deleted' => 'danger',
                            default => 'primary',
                        };
                    }),

                TextColumn::make('subject_type')
                    ->label('Subject Type')
                    ->formatStateUsing(fn (string $state): string => class_basename($state)),

                TextColumn::make('subject_id')
                    ->label('Subject ID')
                    ->searchable(),

                TextColumn::make('causer_type')
                    ->label('Causer Type')
                    ->formatStateUsing(fn (?string $state): string => $state ? class_basename($state) : 'System'),

                TextColumn::make('causer_id')
                    ->label('Causer ID')
                    ->searchable(),

            ])
            ->filters([
                SelectFilter::make('subject_type')
                    ->label('Subject Type')
                    ->options(function () {
                        return \Spatie\Activitylog\Models\Activity::distinct()
                            ->whereNotNull('subject_type')
                            ->pluck('subject_type')
                            ->mapWithKeys(fn ($type) => [$type => class_basename($type)])
                            ->toArray();
                    }),

                SelectFilter::make('causer_id')
                    ->label('User')
                    ->options(User::query()->pluck('email', 'id')->toArray())
                    ->searchable()
                    ->preload(),
            ], layout: FiltersLayout::AboveContent)
            ->recordActions([
                ViewAction::make()->slideOver(),
            ])
            ->defaultSort('id', 'desc');
    }
}
