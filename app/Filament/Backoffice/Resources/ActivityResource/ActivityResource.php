<?php

namespace App\Filament\Backoffice\Resources\ActivityResource;

use App\Filament\Backoffice\Resources\ActivityResource\Tables\ActivityTable;
use BackedEnum;
use Filament\Infolists\Components\CodeEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Spatie\Activitylog\Models\Activity;
use UnitEnum;

class ActivityResource extends Resource
{
    protected static ?string $model = Activity::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static string|UnitEnum|null $navigationGroup = 'System';

    protected static ?int $navigationSort = 999;

    public static function infolist(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Activity Details')
                    ->schema([
                        TextEntry::make('id')
                            ->label('ID'),

                        TextEntry::make('description')
                            ->label('Description'),

                        TextEntry::make('subject_type')
                            ->label('Subject Type')
                            ->formatStateUsing(fn (?string $state): string => $state ? class_basename($state) : 'N/A'),

                        TextEntry::make('subject_id')
                            ->label('Subject ID'),

                        TextEntry::make('causer_type')
                            ->label('Causer Type')
                            ->formatStateUsing(fn (?string $state): string => $state ? class_basename($state) : 'System'),

                        TextEntry::make('causer_id')
                            ->label('Causer ID'),

                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Section::make('Changes')
                    ->schema([
                        CodeEntry::make('old_values')
                            ->label('Old Values')
                            ->state(function ($record): string {
                                return json_encode($record->changes['old'] ?? [], JSON_PRETTY_PRINT);
                            })
                            ->columnSpan(1)
                            ->extraAttributes(['class' => 'font-mono text-sm']),

                        CodeEntry::make('new_values')
                            ->label('New Values')
                            ->state(function ($record): string {
                                return json_encode($record->changes['attributes'] ?? [], JSON_PRETTY_PRINT);
                            })
                            ->columnSpan(1)
                            ->extraAttributes(['class' => 'font-mono text-sm']),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->visible(fn ($record) => ! empty($record->changes)),
            ]);
    }

    public static function table(Table $table): Table
    {
        return ActivityTable::configure($table);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivities::route('/'),
            // 'view' => Pages\ViewActivity::route('/{record}'),
        ];
    }

    public static function canViewAny(): bool
    {
        return user()->isSuperAdmin();
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
