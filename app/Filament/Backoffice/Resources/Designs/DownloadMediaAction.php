<?php

namespace App\Filament\Backoffice\Resources\Designs;

use App\Actions\DownloadDesignMedia;
use App\Models\Design;
use Filament\Actions\Action;
use Filament\Support\Icons\Heroicon;

class DownloadMediaAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub

        $this->icon(Heroicon::OutlinedArrowDownTray)
            ->tooltip('Download media files')
            ->disabled(fn(Design $record) => !$record->hasMedia('*'))
            ->hiddenLabel()
            ->action(function (Design $record, $livewire) {
                $url = DownloadDesignMedia::make()->handle($record);

                $livewire->js("window.open('$url', '_blank')");
            })
            ->openUrlInNewTab();
    }
}
