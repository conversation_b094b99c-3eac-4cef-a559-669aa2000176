<?php

namespace App\Filament\Backoffice\Resources\Designs\Pages;

use App\Filament\Backoffice\Resources\Designs\DesignResource;
use App\Filament\Backoffice\Resources\Designs\DownloadMediaAction;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditDesign extends EditRecord
{
    protected static string $resource = DesignResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DownloadMediaAction::make('download-media')
                ->hiddenLabel(false),
            DeleteAction::make(),
        ];
    }
}
