<?php

namespace App\Filament\Backoffice\Resources\Designs;

use App\Actions\ApproveDesign;
use App\Models\Design;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;

class ApproveDesignAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Approve Design')
            ->icon('heroicon-o-check-circle')
            ->requiresConfirmation()
            ->modalWidth('2xl')
            ->schema([
                Radio::make('rating_score')
                    ->label('Hidden rating score')
                    ->hint('Bạn đánh giá thiết kế của designer n<PERSON><PERSON> như thế nào?')
                    ->helperText('Kết quả đánh giá sẽ không được hiển thị công khai.')
                    ->inline()
                    ->options([
                        1 => 'Xấu',
                        2 => 'Khá xấu',
                        3 => 'Trung bình',
                        4 => 'Khá đẹp',
                        5 => 'Rất ưng',
                    ]),
                Textarea::make('rating_comment')
                    ->label('Rating Comment')
                    ->placeholder('Optional comment about the design')
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->action(function (Design $record) {
                ApproveDesign::make()->handle($record);

                Notification::make()
                    ->title('Design approved successfully')
                    ->body('The design has been approved and is now live.')
                    ->success()
                    ->send();
            });
    }
}
