<?php

namespace App\Filament\Backoffice\Resources\Designs\Tables;

use App\Actions\DownloadDesignMedia;
use App\Filament\Backoffice\Resources\Designs\DownloadMediaAction;
use App\Filament\Backoffice\Resources\Designs\SetDesignerAction;
use App\Models\Design;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class DesignsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->modifyQueryUsing(fn($query) => $query->with(['idea', 'productType', 'designer', 'media']))
            ->minimal()
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('feature')
                    ->label('Image')
                    ->disabledClick()
                    ->state(fn() => 'featured_image')
                    ->formatStateUsing(function (Design $record) {
                        $featuredImage = $record->media->first(fn(Media $media) => $media->collection_name === 'featured_image');

                        if ($featuredImage) {
                            return '<img src="' . e($featuredImage->getFullUrl()) . '" alt="Featured Image" style="height:48px;width:48px;object-fit:cover;border-radius:6px;" />';
                        }

                        return '';
                    })
                    ->html(),
                TextColumn::make('images')
                    ->label('Gallery')
                    ->disabledClick()
                    ->state(fn() => 'images')
                    ->formatStateUsing(function (Design $record) {
                        $urls = $record->media->filter(fn(Media $media) => $media->collection_name === 'gallery_images')->map->getFullUrl();

                        return $urls->map(
                            fn(string $url) => '<a href="' . e($url) . '" target="_blank" rel="noopener noreferrer">'
                                . '<img src="' . e($url) . '" alt="Design image" style="display:inline-block;height:48px;width:48px;object-fit:cover;border-radius:6px;margin-right:6px;" />'
                                . '</a>'
                        )->implode('');
                    })
                    ->html(),
                SelectColumn::make('score')
                    ->width('50px')
                    ->grow(false)
                    ->options(function () {
                        $values = range(0, 3, 0.5);

                        return array_combine($values, $values);
                    }),
                TextColumn::make('productType.name')
                    ->label('Product Type'),
                ToggleColumn::make('design_by_company')
                    ->label('By company'),
                TextColumn::make('latestTask.status')
                    ->badge(),
                IconColumn::make('has_source_files')
                    ->label('Hsf')
                    ->boolean()
                    ->state(fn(Design $record): bool => !blank($record->source_file_urls)),
            ])
            ->defaultSort('id', 'desc')
            ->recordActions([
                SetDesignerAction::make('set-designer')
                    ->disabled(fn(Design $record) => $record->design_by_company)
                    ->hiddenLabel(),
                ViewAction::make()
                    ->hiddenLabel(),
                DownloadMediaAction::make('download-media'),
                EditAction::make()
                    ->hiddenLabel()
                    ->slideOver(),
                DeleteAction::make()
                    ->hiddenLabel(),
            ]);
    }
}
