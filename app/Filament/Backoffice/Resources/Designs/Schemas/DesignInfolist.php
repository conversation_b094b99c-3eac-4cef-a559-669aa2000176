<?php

namespace App\Filament\Backoffice\Resources\Designs\Schemas;

use App\Models\Design;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\View;
use Filament\Schemas\Schema;

class DesignInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Grid::make()
                    ->columnSpanFull()
                    ->schema([
                        Section::make('Design results')
                            ->columnSpanFull()
                            ->schema([
                                SpatieMediaLibraryImageEntry::make('featured_image')
                                    ->collection('featured_image')
                                    ->imageWidth(300)
                                    ->imageHeight('auto'),

                                SpatieMediaLibraryImageEntry::make('gallery_images')
                                    ->collection('gallery_images')
                                    ->extraAttributes(['style' => 'align-items: start;'])
                                    ->imageWidth(300)
                                    ->imageHeight('auto')
                                    ->alignStart(),

                                TextEntry::make('source_file_urls')
                                    ->formatStateUsing(fn($state) => nl2br($state))
                                    ->html()
                                    ->columnSpanFull()
                                    ->helperText('URLs of the source files for the design, PSD, AI, etc.'),
                            ]),
                    ]),
            ]);
    }
}
