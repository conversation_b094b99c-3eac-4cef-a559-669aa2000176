<?php

namespace App\Filament\Backoffice\Resources\Designs\Schemas;

use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class DesignForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->schema([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Section::make()
                            ->heading('Design information')
                            ->columnSpan(2)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('featured_image')
                                    ->collection('featured_image')
                                    ->label('Main image')
                                    ->columnSpanFull()
                                    ->image(),

                                SpatieMediaLibraryFileUpload::make('images')
                                    ->collection('gallery_images')
                                    ->label('Gallery images')
                                    ->multiple()
                                    ->reorderable()
                                    ->downloadable()
                                    ->columnSpanFull()
                                    ->panelLayout('grid'),

                                Textarea::make('source_file_urls')
                                    ->label('Design File URLs')
                                    ->rows(3)
                                    ->columnSpanFull()
                                    ->helperText('PSD files, AI files, or any other source files for the design.'),
                            ]),
                        Section::make()
                            ->heading('Idea')
                            ->columnSpan(1)
                            ->relationship('idea')
                            ->schema([
                                TextEntry::make('uid')
                                    ->label('UID')
                                    ->badge(),
                                TextEntry::make('name'),
                                TextEntry::make('description'),
                                SpatieMediaLibraryImageEntry::make('featured_image')
                                    ->collection('featured_image')
                                    ->label('Idea image'),
                            ])
                    ]),
            ]);
    }
}
