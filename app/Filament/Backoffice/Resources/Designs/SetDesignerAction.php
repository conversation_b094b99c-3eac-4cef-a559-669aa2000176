<?php

namespace App\Filament\Backoffice\Resources\Designs;

use App\Actions\CreateTaskForDesigner;
use App\Enums\UserRole;
use App\Models\Design;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\ToggleButtons;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Support\Icons\Heroicon;

class SetDesignerAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Set Designer')
            ->icon(Heroicon::OutlinedUserPlus)
            ->color('info')
            ->fillForm(function (Design $record) {
                return [
                    'design_by_company' => $record->design_by_company,
                    'designer_id'       => $record->designer_id,
                    'score'             => $record->score,
                ];
            })
            ->schema([
                Select::make('user_id')
                    ->label('Designer')
                    ->options(
                        User::where('role', UserRole::Designer)
                            ->orderBy('id')
                            ->pluck('name', 'id')
                    )
                    ->required(fn(Get $get) => !$get('design_by_company'))
                    ->searchable()
                    ->placeholder('Select a designer')
                    ->default(fn($record) => $record->user_id),
                Textarea::make('comment')
                    ->label('Comment')
                    ->placeholder('Nôi dung bổ sung cho designer')
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->action(function (array $data, $record): void {
                /* @var User $designer */
                $designer = User::findOrFail($data['user_id']);

                $task = CreateTaskForDesigner::make()->handle(
                    designer: $designer,
                    designs: $record,
                    owner: user(),
                    title: "Design task for {$record->idea->name}",
                    comment: $data['comment']
                );

                Notification::make()
                    ->title('Designer assigned successfully')
                    ->body("Task #{$task->id} has been assigned to {$designer->name}.")
                    ->success()
                    ->send();
            });
    }
}
