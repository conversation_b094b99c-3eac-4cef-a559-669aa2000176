<?php

namespace App\Filament\Backoffice\Resources\Designs;

use App\Actions\RequestRevision;
use App\Models\Design;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;

class RequestRevisionAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Request Revision')
            ->icon('heroicon-o-arrow-path')
            ->color('warning')
            ->visible(fn (Design $record) => $record->isSubmitted())
            ->authorize('requestRevision')
            ->requiresConfirmation()
            ->modalWidth('2xl')
            ->schema([
                Textarea::make('revision_comment')
                    ->label('Revision Request')
                    ->placeholder('Please describe what changes are needed for this design...')
                    ->required()
                    ->rows(5)
                    ->columnSpanFull(),
            ])
            ->action(function (Design $record, array $data) {
                RequestRevision::run(
                    design: $record,
                    comment: $data['revision_comment'],
                    userId: user()->id
                );

                Notification::make()
                    ->title('Revision requested successfully')
                    ->body('The design has been marked as needing revision and the designer has been notified.')
                    ->success()
                    ->send();
            });
    }
}
