<?php

namespace App\Filament\Backoffice\Resources\Designs\RelationManagers;

use App\Filament\Backoffice\Resources\Tasks\TaskResource;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TasksRelationManager extends RelationManager
{
    protected static string $relationship = 'tasks';

    protected static ?string $relatedResource = TaskResource::class;

    protected static bool $isLazy = false;

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('Task ID'),
                TextColumn::make('assignee.name')
                    ->label('Designer'),
                TextColumn::make('status')
                    ->badge(),
            ])
            ->paginated(false)
            ->headerActions([
                CreateAction::make(),
            ]);
    }
}
