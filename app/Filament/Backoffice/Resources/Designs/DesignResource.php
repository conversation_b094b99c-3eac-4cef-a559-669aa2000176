<?php

namespace App\Filament\Backoffice\Resources\Designs;

use App\Enums\DesignStatus;
use App\Filament\Backoffice\Resources\Designs\Pages\EditDesign;
use App\Filament\Backoffice\Resources\Designs\Pages\ListDesigns;
use App\Filament\Backoffice\Resources\Designs\Pages\ViewDesign;
use App\Filament\Backoffice\Resources\Designs\RelationManagers\TasksRelationManager;
use App\Filament\Backoffice\Resources\Designs\Schemas\DesignForm;
use App\Filament\Backoffice\Resources\Designs\Schemas\DesignInfolist;
use App\Filament\Backoffice\Resources\Designs\Tables\DesignsTable;
use App\Models\Design;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class DesignResource extends Resource
{
    protected static ?string $model = Design::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedPhoto;

    protected static ?string $recordTitleAttribute = 'id';

    protected static string|UnitEnum|null $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 60;

    public static function getEloquentQuery(): Builder
    {
        if (user()->isManager()) {
            return parent::getEloquentQuery();
        }

        return parent::getEloquentQuery()->whereHas('idea', fn ($query) => $query->where('user_id', user()->id));
    }

    public static function getNavigationBadge(): ?string
    {
        $query = static::getModel()::where('status', DesignStatus::Submitted);

        $count = user()->isManager()
            ? $query->count()
            : $query->whereHas('idea', fn ($query) => $query->where('user_id', user()->id))->count();

        return $count ?: null;
    }

    public static function form(Schema $schema): Schema
    {
        return DesignForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return DesignsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            TasksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListDesigns::route('/'),
            'edit' => EditDesign::route('/{record}/edit'),
//            'view' => ViewDesign::route('/{record}'),
        ];
    }
}
