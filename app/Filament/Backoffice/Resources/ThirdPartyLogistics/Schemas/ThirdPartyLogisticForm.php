<?php

namespace App\Filament\Backoffice\Resources\ThirdPartyLogistics\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ThirdPartyLogisticForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Provider Name')
                    ->required()
                    ->maxLength(128)
                    ->helperText('Full name of the 3PL provider (e.g., "Hongkong Post")'),

                TextInput::make('code')
                    ->label('Provider Code')
                    ->required()
                    ->maxLength(16)
                    ->unique(ignoreRecord: true)
                    ->helperText('Short code for the provider (e.g., "HP" for Hongkong Post)'),
            ]);
    }
}
