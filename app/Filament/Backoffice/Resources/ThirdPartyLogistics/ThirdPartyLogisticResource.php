<?php

namespace App\Filament\Backoffice\Resources\ThirdPartyLogistics;

use App\Filament\Backoffice\Resources\ThirdPartyLogistics\Pages\CreateThirdPartyLogistic;
use App\Filament\Backoffice\Resources\ThirdPartyLogistics\Pages\EditThirdPartyLogistic;
use App\Filament\Backoffice\Resources\ThirdPartyLogistics\Pages\ListThirdPartyLogistics;
use App\Filament\Backoffice\Resources\ThirdPartyLogistics\Schemas\ThirdPartyLogisticForm;
use App\Filament\Backoffice\Resources\ThirdPartyLogistics\Tables\ThirdPartyLogisticsTable;
use App\Filament\TranslatableResource;
use App\Models\ThirdPartyLogistic;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class ThirdPartyLogisticResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = ThirdPartyLogistic::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBuildingOffice2;

    protected static string|UnitEnum|null $navigationGroup = 'Settings';

    public static function form(Schema $schema): Schema
    {
        return ThirdPartyLogisticForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ThirdPartyLogisticsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListThirdPartyLogistics::route('/'),
            'create' => CreateThirdPartyLogistic::route('/create'),
            'edit' => EditThirdPartyLogistic::route('/{record}/edit'),
        ];
    }
}
