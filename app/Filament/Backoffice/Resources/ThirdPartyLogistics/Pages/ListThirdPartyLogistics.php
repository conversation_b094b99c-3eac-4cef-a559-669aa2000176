<?php

namespace App\Filament\Backoffice\Resources\ThirdPartyLogistics\Pages;

use App\Filament\Backoffice\Resources\ThirdPartyLogistics\ThirdPartyLogisticResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListThirdPartyLogistics extends ListRecords
{
    protected static string $resource = ThirdPartyLogisticResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
