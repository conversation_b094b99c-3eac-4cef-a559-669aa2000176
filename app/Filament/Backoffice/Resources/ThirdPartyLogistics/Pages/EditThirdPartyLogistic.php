<?php

namespace App\Filament\Backoffice\Resources\ThirdPartyLogistics\Pages;

use App\Filament\Backoffice\Resources\ThirdPartyLogistics\ThirdPartyLogisticResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditThirdPartyLogistic extends EditRecord
{
    protected static string $resource = ThirdPartyLogisticResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
