<?php

namespace App\Filament\Backoffice\Resources\Carriers\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class CarrierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->trim(),
                TextInput::make('tracking_url')
                    ->nullable()
                    ->regex('/^https?:\/\/[^\s\/$.?#].[^\s]*\{tracking_number\}[^\s]*$/i')
                    ->helperText('If availabe the url must be like example: https://example.com/track/{tracking_number}')
                    ->validationMessages([
                        'regex' => 'The tracking URL must be a valid URL containing the {tracking_number} placeholder',
                    ]),
            ]);
    }
}
