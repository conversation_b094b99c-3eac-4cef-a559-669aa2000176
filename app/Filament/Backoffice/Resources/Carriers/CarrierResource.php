<?php

namespace App\Filament\Backoffice\Resources\Carriers;

use App\Filament\Backoffice\Resources\Carriers\Pages\CreateCarrier;
use App\Filament\Backoffice\Resources\Carriers\Pages\EditCarrier;
use App\Filament\Backoffice\Resources\Carriers\Pages\ListCarriers;
use App\Filament\Backoffice\Resources\Carriers\Schemas\CarrierForm;
use App\Filament\Backoffice\Resources\Carriers\Tables\CarriersTable;
use App\Filament\TranslatableResource;
use App\Models\Carrier;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CarrierResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Carrier::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedTruck;

    protected static string|UnitEnum|null $navigationGroup = 'Settings';

    public static function form(Schema $schema): Schema
    {
        return CarrierForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CarriersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCarriers::route('/'),
            'create' => CreateCarrier::route('/create'),
            'edit' => EditCarrier::route('/{record}/edit'),
        ];
    }
}
