<?php

namespace App\Filament\Backoffice\Resources\OrdersImportRequests;

use App\Filament\Backoffice\Resources\OrdersImportRequests\Pages\CreateOrdersImportRequest;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Pages\EditOrdersImportRequest;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Pages\ListOrdersImportRequests;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Pages\ViewOrdersImportRequest;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Schemas\OrdersImportRequestForm;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Schemas\OrdersImportRequestInfolist;
use App\Filament\Backoffice\Resources\OrdersImportRequests\Tables\OrdersImportRequestsTable;
use App\Models\OrdersImportRequest;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class OrdersImportRequestResource extends Resource
{
    protected static ?string $model = OrdersImportRequest::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedArrowDownLeft;

    protected static string|UnitEnum|null $navigationGroup = 'System';

    protected static ?int $navigationSort = 70;

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Schema $schema): Schema
    {
        return OrdersImportRequestForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return OrdersImportRequestInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return OrdersImportRequestsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrdersImportRequests::route('/'),
            'create' => CreateOrdersImportRequest::route('/create'),
            'view' => ViewOrdersImportRequest::route('/{record}'),
            'edit' => EditOrdersImportRequest::route('/{record}/edit'),
        ];
    }
}
