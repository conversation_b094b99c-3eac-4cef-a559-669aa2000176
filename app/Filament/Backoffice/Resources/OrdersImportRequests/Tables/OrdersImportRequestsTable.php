<?php

namespace App\Filament\Backoffice\Resources\OrdersImportRequests\Tables;

use App\Actions\ProcessOrdersImportRequest;
use App\Models\OrdersImportRequest;
use Filament\Actions\Action;
use Filament\Actions\ViewAction;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OrdersImportRequestsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('user.name')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('platform')
                    ->searchable(),
                TextColumn::make('hash')
                    ->fontFamily('mono')
                    ->copyable(),
                TextColumn::make('processed_at')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->recordActions([
                Action::make('process')
                    ->visible(fn (OrdersImportRequest $record) => ! $record->isProcessed())
                    ->requiresConfirmation()
                    ->action(function (OrdersImportRequest $record) {
                        ProcessOrdersImportRequest::make()->handle($record);

                        Notification::make()
                            ->title('Processing started')
                            ->body('The import request is being processed. You will be notified once it is complete.')
                            ->success()
                            ->send();
                    }),
                ViewAction::make(),
            ])
            ->toolbarActions([]);
    }
}
