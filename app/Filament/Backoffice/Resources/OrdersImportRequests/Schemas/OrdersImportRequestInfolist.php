<?php

namespace App\Filament\Backoffice\Resources\OrdersImportRequests\Schemas;

use Filament\Infolists\Components\CodeEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class OrdersImportRequestInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('user.name')
                    ->numeric(),
                TextEntry::make('platform'),
                TextEntry::make('hash'),
                TextEntry::make('processed_at')
                    ->dateTime(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                CodeEntry::make('raw_data')
                    ->columnSpanFull()
                    ->grammar('json'),
            ]);
    }
}
