<?php

namespace App\Filament\Backoffice\Resources\OrdersImportRequests\Schemas;

use App\Enums\Platform;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class OrdersImportRequestForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('user_id')
                    ->relationship('user', 'name'),
                Select::make('platform')
                    ->options(Platform::class)
                    ->default('etsy'),
                TextInput::make('raw_data')
                    ->required(),
                DateTimePicker::make('processed_at'),
            ]);
    }
}
