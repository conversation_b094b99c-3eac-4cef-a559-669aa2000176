<?php

namespace App\Filament\Backoffice\Widgets;

use App\Enums\QcSessionStatus;
use App\Enums\UserRole;
use App\Models\User;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class OperatorQcRankingWidget extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 3;

    protected static ?string $heading = 'Top trending';

    protected ?string $pollingInterval = null;

    public function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()
                    ->where('role', UserRole::Operator)
                    ->withCount([
                        'qcSessions as completed_sessions_count' => function (Builder $query) {
                            $query->where('status', QcSessionStatus::Completed);
                        },
                    ])
                    ->selectRaw('users.*, (
                        SELECT COALESCE(SUM(TIMESTAMPDIFF(MINUTE, started_at, completed_at)), 0)
                        FROM qc_sessions
                        WHERE qc_sessions.user_id = users.id
                        AND status = ?
                    ) as total_duration_minutes', [QcSessionStatus::Completed->value])
                    ->having('completed_sessions_count', '>', 0)
                    ->orderByDesc('completed_sessions_count')
                    ->orderByDesc('total_duration_minutes')
            )
            ->columns([
                TextColumn::make('rank')
                    ->label('#')
                    ->state(function ($record, $rowLoop) {
                        return $rowLoop->iteration;
                    })
                    ->width(60)
                    ->alignCenter(),

                TextColumn::make('name')
                    ->label('Operator'),

                TextColumn::make('completed_sessions_count')
                    ->label('Sessions Completed')
                    ->numeric()
                    ->alignCenter()
                    ->badge(),

                TextColumn::make('total_duration_minutes')
                    ->label('Total Duration')
                    ->numeric()
                    ->sortable()
                    ->alignCenter()
                    ->formatStateUsing(function ($state) {
                        if (! $state) {
                            return '-';
                        }

                        $hours = floor($state / 60);
                        $minutes = $state % 60;

                        if ($hours > 0) {
                            return sprintf('%dh %dm', $hours, $minutes);
                        }

                        return sprintf('%dm', $minutes);
                    })
                    ->badge()
                    ->color('primary'),

                TextColumn::make('average_duration')
                    ->label('Avg. Duration')
                    ->alignCenter()
                    ->state(function ($record) {
                        $sessions = $record->completed_sessions_count;
                        $totalMinutes = $record->total_duration_minutes;

                        if (! $sessions || ! $totalMinutes) {
                            return '-';
                        }

                        $avgMinutes = round($totalMinutes / $sessions);

                        if ($avgMinutes >= 60) {
                            $hours = floor($avgMinutes / 60);
                            $minutes = $avgMinutes % 60;

                            return sprintf('%dh %dm', $hours, $minutes);
                        }

                        return sprintf('%dm', $avgMinutes);
                    })
                    ->color('success'),
            ])
            ->defaultSort('completed_sessions_count', 'desc')
            ->minimal();
    }
}
