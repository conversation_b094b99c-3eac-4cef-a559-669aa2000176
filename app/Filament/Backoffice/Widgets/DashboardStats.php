<?php

namespace App\Filament\Backoffice\Widgets;

use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Foundation\Inspiring;

class DashboardStats extends StatsOverviewWidget
{
    protected ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $quote = Inspiring::quotes()->random();
        [$quote, $author] = explode(' - ', $quote);

        return [
            Stat::make('New orders', Order::where('status', OrderStatus::New)->count()),
            Stat::make('Order cancel requests', OrderCancelRequest::where('status', OrderCancelRequestStatus::Pending)->count()),
            Stat::make($quote, '')
                ->description('-'.$author)
                ->extraAttributes([
                    'class' => 'quote-stat',
                    'x-on:click' => 'triggerNyanCat()',
                    'x-data' => '{
                    triggerNyanCat() {
                        // Remove existing nyan cat if any
                        const existing = document.querySelector(\'.nyan-cat\');
                        if (existing) existing.remove();

                        // Create nyan cat element
                        const nyanCat = document.createElement(\'img\');
                        nyanCat.src = \'https://www.nyan.cat/cats/original.gif\';
                        nyanCat.className = \'nyan-cat\';
                        nyanCat.alt = \'Nyan Cat\';

                        // Randomize vertical position (between 20px and 80% of screen height)
                        const randomY = Math.floor(Math.random() * (window.innerHeight - 20)) + 20;
                        nyanCat.style.bottom = randomY + \'px\';

                        // Add to body
                        document.body.appendChild(nyanCat);

                        // Trigger animation
                        setTimeout(() => {
                            nyanCat.classList.add(\'running\');
                        }, 100);

                        // Get duration from CSS variable and remove after animation
                        const duration = parseFloat(getComputedStyle(document.documentElement).getPropertyValue(\'--nyan-duration\')) * 1000;
                        setTimeout(() => {
                            nyanCat.remove();
                        }, duration + 500);
                    }
                }',
                ]),
        ];
    }
}
