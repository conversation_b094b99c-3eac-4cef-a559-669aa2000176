<?php

namespace App\Filament\Backoffice\Exports;

use App\Models\LineItem;
use Filament\Actions\Exports\Jobs\PrepareCsvExport;
use Filament\Actions\Exports\Models\Export;

class PrepareOrderToLineItemExportCsv extends PrepareCsvExport
{
    public function __construct(Export $export, string $query, array $columnMap, array $options = [], int $chunkSize = 100, ?array $records = null)
    {
        $records = LineItem::query()->whereIn('order_id', $records)->pluck('id')->toArray();

        $export->update([
            'total_rows' => count($records),
        ]);

        parent::__construct($export, $query, $columnMap, $options, $chunkSize, $records);
    }
}
