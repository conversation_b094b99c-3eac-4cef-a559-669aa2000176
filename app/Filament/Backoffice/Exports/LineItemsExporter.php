<?php

namespace App\Filament\Backoffice\Exports;

use App\Models\LineItem;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Number;

class LineItemsExporter extends Exporter
{
    protected static ?string $model = LineItem::class;

    public static function modifyQuery(Builder $query): Builder
    {
        return LineItem::query()
            ->whereIn('order_id', $query->pluck('id'))
            ->with(['order', 'order.customer', 'order.shippingAddress', 'order.etsyShop', 'shipment']);
    }

    public function getJobConnection(): ?string
    {
        return 'sync';
    }

    public function getFormats(): array
    {
        return [
            ExportFormat::Csv,
        ];
    }

    public function getFileName(Export $export): string
    {
        return sprintf(
            'user-%s-export-orders-%s.csv',
            $export->user_id,
            $export->created_at->format('Ymdhis'),
        );
    }

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('order.order_date')
                ->label('Order date'),
            ExportColumn::make('order.number_portal')
                ->label('Order TDA number'),
            ExportColumn::make('number_portal')
                ->label('Line item code'),
            ExportColumn::make('order.order_number')
                ->label('Order number'),
            ExportColumn::make('shop_name')
                ->label('Shop name')
                ->state(fn (LineItem $record) => $record->order->shopName()),
            ExportColumn::make('order.shippingAddress.name')
                ->label('Shipping name'),
            ExportColumn::make('order.shippingAddress.line_one')
                ->label('Shipping address 1'),
            ExportColumn::make('order.shippingAddress.line_two')
                ->label('Shipping address 2'),
            ExportColumn::make('order.shippingAddress.city')
                ->label('Shipping city'),
            ExportColumn::make('order.shippingAddress.state')
                ->label('Shipping state'),
            ExportColumn::make('order.shippingAddress.zip')
                ->label('Shipping zip'),
            ExportColumn::make('order.shippingAddress.country')
                ->label('Shipping country'),
            ExportColumn::make('tracking_number')
                ->state(fn (LineItem $record) => $record->shipment?->tracking_number)
                ->label('Tracking number'),
            ExportColumn::make('product_name')
                ->label('Product name'),
            ExportColumn::make('sku')
                ->label('SKU'),
            ExportColumn::make('product_url')
                ->label('Product url')
                ->state(fn (LineItem $record) => $record->productUrl()),
            ExportColumn::make('product_image_url')
                ->label('Product image URL'),
            ExportColumn::make('custom_image')
                ->label('Image'),
            ExportColumn::make('quantity')
                ->label('Quantity'),
            ExportColumn::make('currency_code')
                ->label('Currency code'),
            ExportColumn::make('price')
                ->formatStateUsing(fn (LineItem $record, $state) => $record->paidUsingVnd() ? $state : $state / 100)
                ->label('Price'),
            ExportColumn::make('notes')
                ->state(fn (LineItem $record) => $record->compositeNotes())
                ->label('Notes'),
            ExportColumn::make('tax_label')
                ->state(fn (LineItem $record) => $record->order->getTaxLabel())
                ->label('Tax/VAT Label'),
            ExportColumn::make('tax_message')
                ->state(fn (LineItem $record) => $record->order->getTaxMessage())
                ->label('Tax/VAT Message'),
            ExportColumn::make('gift_message')
                ->state(fn (LineItem $record) => $record->order->gift_message)
                ->label('Gift Message'),
            ExportColumn::make('seller_note')
                ->label('Seller notes'),
            ExportColumn::make('attributes_string')
                ->label('Product attributes')
                ->state(fn (LineItem $record) => $record->attributesString()),
            ExportColumn::make('product_type')
                ->label('Type of product'),
            ExportColumn::make('size')
                ->label('Size'),
            ExportColumn::make('color')
                ->label('Color'),
            ExportColumn::make('personalization')
                ->label('Personalization'),
            ExportColumn::make('custom_status')
                ->label('Status'),
            ExportColumn::make('suggest_supplier')
                ->label('Suggested supplier'),
            ExportColumn::make('suggest_design_file_url')
                ->label('Suggested design file URL'),
            ExportColumn::make('order.seller_confirmed_at')
                ->label('Confirmed at'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = __('Your line item export has completed and :count :rows exported.', [
            'count' => Number::format($export->successful_rows),
            'rows' => str('row')->plural($export->successful_rows),
        ]);

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.__(':count :rows failed to export.', [
                'count' => Number::format($failedRowsCount),
                'rows' => str('row')->plural($failedRowsCount),
            ]);
        }

        return $body;
    }
}
