<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Icons\Heroicon;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class ActivityLogTimelineAction
{
    public static function make(?string $name = null): Action
    {
        return Action::make($name ?? 'activityTimeline')
            ->label('Activity Timeline')
            ->color('gray')
            ->icon(Heroicon::OutlinedClock)
            ->iconPosition(IconPosition::Before)
            ->slideOver()
            ->modalWidth('4xl')
            ->stickyModalHeader()
            ->stickyModalFooter()
            ->modalHeading('Activity Timeline')
            ->modalDescription('View the complete history of changes for this record')
            ->modalSubmitAction(false)
            ->modalCancelActionLabel('Close')
            ->modalContent(function (Component $livewire): \Illuminate\Contracts\View\View {
                $record = $livewire->getRecord();

                if (! $record instanceof Model) {
                    return view('filament.components.no-record');
                }

                return view('filament.components.activity-timeline-wrapper', [
                    'record' => $record,
                ]);
            });
    }
}
