<?php

namespace App\Filament\Actions;

use Closure;
use Filament\Actions\Action;
use Filament\Support\Icons\Heroicon;
use Livewire\Component;

class CopyAction extends Action
{
    protected string|Closure $content;

    public function content(string|Closure $content): static
    {
        $this->content = $content;

        return $this;
    }

    public function getContent(): string
    {
        return $this->evaluate($this->content);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Copy')
            ->icon(Heroicon::OutlinedClipboard)
            ->outlined()
            ->color('gray')
            ->hiddenLabel()
            ->action(function (Component $livewire) {
                $content = $this->getContent();

                $livewire->js(<<<JS
                    window.navigator.clipboard.writeText("$content");
                    new FilamentNotification().title("Copied to clipboard").info().send();
                JS);
            });
    }
}
