<?php

namespace App\Filament\Seller\Widgets;

use App\Enums\OrderStatus;
use App\Models\Order;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class SellerStatsOverview extends StatsOverviewWidget
{
    protected static ?int $sort = 1;

    protected ?string $pollingInterval = null;

    protected function getStats(): array
    {
        $sellerId = Auth::id();

        // Get current month date range
        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Get all orders for this seller this month
        $ordersQuery = Order::where('seller_id', $sellerId)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Get totals in one query
        $cancelledStatus = OrderStatus::Cancelled->value;
        $monthlyStats = $ordersQuery->selectRaw('
            COUNT(*) as total_orders,
            SUM(CASE WHEN status != ? THEN total ELSE 0 END) as revenue,
            SUM(CASE WHEN status = ? THEN total ELSE 0 END) as cancelled_total,
            COUNT(CASE WHEN status = ? THEN 1 END) as cancelled_count
        ', [$cancelledStatus, $cancelledStatus, $cancelledStatus])->first();

        $totalOrders = $monthlyStats->total_orders ?? 0;
        $revenue = $monthlyStats->revenue ?? 0;
        $cancelledTotal = $monthlyStats->cancelled_total ?? 0;
        $cancelledCount = $monthlyStats->cancelled_count ?? 0;

        $cancellationRate = $totalOrders > 0 ? ($cancelledCount / $totalOrders) * 100 : 0;

        return [
            Stat::make('Revenue This Month', '$'.number_format($revenue / 100, 2))
                ->description($totalOrders.' orders total')
                ->color('success'),

            Stat::make('Cancelled This Month', '$'.number_format($cancelledTotal / 100, 2))
                ->description('Lost revenue')
                ->color('danger'),

            Stat::make('Cancellation Rate', number_format($cancellationRate, 1).'%')
                ->description('This month')
                ->color($cancellationRate > 10 ? 'warning' : 'success'),
        ];
    }
}
