<?php

namespace App\Filament\Seller\Resources\LineItems\Tables;

use App\Actions\CancelDraftOrder;
use App\Actions\ConfirmDraftOrder;
use App\Actions\GetUserEtsyShops;
use App\Enums\OrderStatus;
use App\Models\LineItem;
use Filament\Actions\Action;
use Filament\Actions\BulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

class LineItemsTable
{
    /**
     * If you wonder why the checkbox of individual record is hidden, check the ListLineItems class
     */
    public static function configure(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn (Builder $query) => $query
                    ->whereHas(
                        'order',
                        fn ($query) => $query
                            ->where('status', OrderStatus::Draft)
                            ->whereAny(['seller_id', 'backup_seller_id'], user()->id)
                    )
                    ->with(['order.customer', 'order.etsyShop'])
            )
            ->defaultGroup(
                Group::make('order.order_number')
                    ->getTitleFromRecordUsing(function (LineItem $record) {
                        return sprintf(
                            '%s from %s | Customer: %s',
                            $record->order->order_number,
                            $record->order->shopName(),
                            $record->order->customer->name,
                        );
                    })
            )
            ->selectable(true)
            ->columns([
                TextColumn::make('order.order_number')
                    ->label('TDA order number')
                    ->badge()
                    ->searchable()
                    ->url(fn ($record) => route('filament.seller.resources.orders.view', $record->order)),
                TextColumn::make('number_portal')
                    ->label('TDA item number')
                    ->badge()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                ImageColumn::make('product_image_url')
                    ->label('Image'),
                TextColumn::make('product_name')
                    ->grow(true)
                    ->extraAttributes(['style' => 'width: 322px'])
                    ->wrap()
                    ->suffix(
                        fn (LineItem $record) => $record->productUrl()
                            ? Action::make('view-product-'.$record->id)
                                ->hiddenLabel()
                                ->link()
                                ->color('gray')
                                ->icon(Heroicon::OutlinedArrowTopRightOnSquare)
                                ->url($record->productUrl())
                                ->openUrlInNewTab()
                            : null
                    ),
                TextColumn::make('attributes')
                    ->state(fn (LineItem $record) => $record->variationsLines())
                    ->bulleted()
                    ->disabledClick(),
                TextInputColumn::make('product_type')
                    ->placeholder('Product type')
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('Product type updated: '.$state)->info()->send();
                    }),
                TextInputColumn::make('size')
                    ->width(200)
                    ->placeholder('Size')
                    ->updateStateUsing(fn (LineItem $record, ?string $state) => $record->setSize($state)->save())
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('Size updated: '.($state ?: 'cleared'))->info()->send();
                    }),
                TextInputColumn::make('color')
                    ->width(200)
                    ->placeholder('Color')
                    ->updateStateUsing(fn (LineItem $record, ?string $state) => $record->setColor($state)->save())
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('Color updated: '.($state ?: 'cleared'))->info()->send();
                    }),
                TextInputColumn::make('personalization')
                    ->width(200)
                    ->placeholder('Personalization')
                    ->updateStateUsing(fn (LineItem $record, ?string $state) => $record->setPersonalization($state)->save())
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('Personalization updated: '.($state ?: 'cleared'))->info()->send();
                    }),
                TextInputColumn::make('sku')
                    ->label('SKU')
                    ->width(200)
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('SKU updated: '.($state ?: 'cleared'))->info()->send();
                    }),
                TextInputColumn::make('seller_note')
                    ->placeholder('Seller note')
                    ->afterStateUpdated(function (?string $state) {
                        Notification::make()->title('SKU updated: '.($state ?: 'cleared'))->info()->send();
                    }),
                TextColumn::make('quantity')
                    ->numeric()
                    ->alignRight(),
                TextColumn::make('price')
                    ->recordMoney()
                    ->sortable(),
                TextColumn::make('subtotal')
                    ->numeric()
                    ->recordMoney()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Filter::make('shop_id_platform')
                    ->schema([
                        Select::make('etsy_shop_id')
                            ->options(
                                Cache::remember('etsy-shops-'.user()->id, 60, fn () => GetUserEtsyShops::make()->handle(user())->pluck('username', 'id_etsy'))
                            )
                            ->searchable()
                            ->label('Etsy Shop'),
                    ])
                    ->query(function (\Illuminate\Contracts\Database\Eloquent\Builder $query, array $data) {
                        if (! blank($data['etsy_shop_id'])) {
                            $query->whereHas('order', fn ($query) => $query->where('shop_id_platform', $data['etsy_shop_id']));
                        }

                        return $query;
                    }),
            ])
            ->deferFilters(false)
            ->recordActions([
                ViewAction::make(),
            ])
            ->toolbarActions([
                BulkAction::make('bulk-confirm-orders')
                    ->label('Confirm Orders')
                    ->icon('heroicon-o-check-badge')
                    ->tooltip('
                        This action will confirm all orders from selected lines, this does not care about you select all lines of an order or not.
                        So pls complete all lines of an order before you confirm.
                    ')
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion()
                    ->action(function (Collection $records) {
                        $orders = $records->unique('order_id')->pluck('order');

                        foreach ($orders as $order) {
                            ConfirmDraftOrder::make()->handle(user(), $order);
                        }

                        Notification::make()
                            ->title('Orders confirmed successfully')
                            ->success()
                            ->send();
                    }),

                BulkAction::make('bulk-cancel-orders')
                    ->label('Cancel Orders')
                    ->icon('heroicon-o-check-badge')
                    ->color('gray')
                    ->deselectRecordsAfterCompletion()
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $orders = $records->unique('order_id')->pluck('order');

                        foreach ($orders as $order) {
                            CancelDraftOrder::make()->handle(user(), $order);
                        }

                        Notification::make()
                            ->title('Orders cancelled successfully')
                            ->success()
                            ->send();
                    }),
            ]);
    }
}
