<?php

namespace App\Filament\Seller\Resources\LineItems;

use App\Filament\Seller\Resources\LineItems\Pages\CreateLineItem;
use App\Filament\Seller\Resources\LineItems\Pages\EditLineItem;
use App\Filament\Seller\Resources\LineItems\Pages\ListLineItems;
use App\Filament\Seller\Resources\LineItems\Pages\ViewLineItem;
use App\Filament\Seller\Resources\LineItems\Schemas\LineItemForm;
use App\Filament\Seller\Resources\LineItems\Schemas\LineItemInfolist;
use App\Filament\Seller\Resources\LineItems\Tables\LineItemsTable;
use App\Filament\TranslatableResource;
use App\Models\LineItem;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class LineItemResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = LineItem::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBars2;

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationLabel(): string
    {
        return 'Draft Orders';
    }

    public static function form(Schema $schema): Schema
    {
        return LineItemForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return LineItemInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return LineItemsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListLineItems::route('/'),
            'create' => CreateLineItem::route('/create'),
            'view' => ViewLineItem::route('/{record}'),
            'edit' => EditLineItem::route('/{record}/edit'),
        ];
    }
}
