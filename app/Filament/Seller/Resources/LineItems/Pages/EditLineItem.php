<?php

namespace App\Filament\Seller\Resources\LineItems\Pages;

use App\Filament\Seller\Resources\LineItems\LineItemResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditLineItem extends EditRecord
{
    protected static string $resource = LineItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
