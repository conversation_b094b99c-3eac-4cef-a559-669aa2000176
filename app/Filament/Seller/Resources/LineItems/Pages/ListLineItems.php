<?php

namespace App\Filament\Seller\Resources\LineItems\Pages;

use App\Filament\Seller\Resources\LineItems\LineItemResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;

class ListLineItems extends ListRecords
{
    protected static string $resource = LineItemResource::class;

    protected static ?string $title = 'Draft Orders';

    public function mount(): void
    {
        parent::mount();

        // hide the checkbox on individual line items
        FilamentView::registerRenderHook(
            PanelsRenderHook::BODY_START,
            fn (): string => '<style>
                .fi-ta-record-checkbox {display: none;}
            </style>'
        );
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
