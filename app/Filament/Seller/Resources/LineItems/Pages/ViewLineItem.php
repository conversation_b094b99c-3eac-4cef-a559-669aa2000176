<?php

namespace App\Filament\Seller\Resources\LineItems\Pages;

use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Seller\Resources\LineItems\LineItemResource;
use Filament\Resources\Pages\ViewRecord;

class ViewLineItem extends ViewRecord
{
    protected static string $resource = LineItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make(),
        ];
    }
}
