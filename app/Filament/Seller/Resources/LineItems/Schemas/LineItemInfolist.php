<?php

namespace App\Filament\Seller\Resources\LineItems\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class LineItemInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('number_portal'),
                TextEntry::make('id_platform')
                    ->numeric(),
                TextEntry::make('order.id')
                    ->numeric(),
                TextEntry::make('product_name'),
                ImageEntry::make('product_image_url'),
                TextEntry::make('sku')
                    ->label('SKU'),
                TextEntry::make('quantity')
                    ->numeric(),
                TextEntry::make('price')
                    ->money(),
                TextEntry::make('currency_code'),
                TextEntry::make('subtotal')
                    ->numeric(),
                IconEntry::make('is_download')
                    ->boolean(),
                IconEntry::make('is_personalizable')
                    ->boolean(),
                TextEntry::make('etsy_listing_id')
                    ->numeric(),
                TextEntry::make('product_type'),
                TextEntry::make('production_total')
                    ->numeric(),
                TextEntry::make('packaging_total')
                    ->numeric(),
                TextEntry::make('shipping_total')
                    ->numeric(),
                TextEntry::make('supplier.name')
                    ->numeric(),
                TextEntry::make('supplier_order_number'),
                TextEntry::make('idea_file_url'),
                TextEntry::make('shipment.id')
                    ->numeric(),
                TextEntry::make('status'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
