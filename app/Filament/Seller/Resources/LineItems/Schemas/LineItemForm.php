<?php

namespace App\Filament\Seller\Resources\LineItems\Schemas;

use App\Enums\LineItemStatus;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class LineItemForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('number_portal'),
                TextInput::make('id_platform')
                    ->numeric(),
                Select::make('order_id')
                    ->relationship('order', 'id')
                    ->required(),
                TextInput::make('product_name')
                    ->required(),
                FileUpload::make('product_image_url')
                    ->image(),
                TextInput::make('sku')
                    ->label('SKU'),
                TextInput::make('quantity')
                    ->required()
                    ->numeric()
                    ->default(1),
                TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->default(0)
                    ->prefix('$'),
                TextInput::make('currency_code'),
                TextInput::make('subtotal')
                    ->required()
                    ->numeric()
                    ->default(0),
                Toggle::make('is_download')
                    ->required(),
                Toggle::make('is_personalizable')
                    ->required(),
                TextInput::make('etsy_listing_id')
                    ->numeric(),
                TextInput::make('variations'),
                TextInput::make('product_type'),
                TextInput::make('meta_data'),
                Textarea::make('private_note')
                    ->columnSpanFull(),
                TextInput::make('production_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('packaging_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('shipping_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                Select::make('supplier_id')
                    ->relationship('supplier', 'name'),
                TextInput::make('supplier_order_number'),
                TextInput::make('idea_file_url'),
                Select::make('shipment_id')
                    ->relationship('shipment', 'id'),
                Select::make('status')
                    ->options(LineItemStatus::class)
                    ->default('new')
                    ->required(),
            ]);
    }
}
