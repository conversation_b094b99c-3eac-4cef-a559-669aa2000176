<?php

namespace App\Filament\Seller\Resources\PersonalAccessTokens\Tables;

use Filament\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class PersonalAccessTokensTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('created_at')
                    ->dateTime(),
                TextColumn::make('last_used_at')
                    ->dateTime(),
                TextColumn::make('expires_at')
                    ->dateTime(),
            ])
            ->minimal()
            ->defaultSort('id', 'desc')
            ->recordActions([
                DeleteAction::make(),
            ])
            ->toolbarActions([
            ]);
    }
}
