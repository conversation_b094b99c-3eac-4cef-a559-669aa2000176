<?php

namespace App\Filament\Seller\Resources\PersonalAccessTokens;

use App\Filament\Seller\Resources\PersonalAccessTokens\Pages\ListPersonalAccessTokens;
use App\Filament\Seller\Resources\PersonalAccessTokens\Schemas\PersonalAccessTokenForm;
use App\Filament\Seller\Resources\PersonalAccessTokens\Tables\PersonalAccessTokensTable;
use App\Filament\TranslatableResource;
use App\Models\User;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Sanctum\PersonalAccessToken;
use UnitEnum;

class PersonalAccessTokenResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = PersonalAccessToken::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedKey;

    protected static string|UnitEnum|null $navigationGroup = 'Settings';

    protected static ?string $modelLabel = 'Access Token';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('tokenable_type', User::class)
            ->where('tokenable_id', user()->id);
    }

    public static function form(Schema $schema): Schema
    {
        return PersonalAccessTokenForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return PersonalAccessTokensTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPersonalAccessTokens::route('/'),
        ];
    }
}
