<?php

namespace App\Filament\Seller\Resources\PersonalAccessTokens\Pages;

use App\Actions\CreateAccessToken;
use App\Filament\Seller\Resources\PersonalAccessTokens\PersonalAccessTokenResource;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Actions;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;

class ListPersonalAccessTokens extends ListRecords
{
    protected static string $resource = PersonalAccessTokenResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create-pac')
                ->label(__('Create Access Token'))
                ->schema([
                    TextInput::make('name')->default('Default'),

                    Actions::make([
                        Action::make('create')
                            ->action(function (array $data, Get $get, Set $set) {
                                $token = CreateAccessToken::make()->handle(user(), $get('name'));
                                $set('token', $token);
                            }),
                    ]),

                    TextInput::make('token')
                        ->readOnly()
                        ->helperText(__('After creating the token, copy it immediately. You will not be able to see it again.')),
                ])
                ->modalSubmitAction(false)
                ->modalCancelAction(false),
        ];
    }
}
