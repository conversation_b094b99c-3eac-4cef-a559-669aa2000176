<?php

namespace App\Filament\Seller\Resources\Orders\Tables;

use App\Actions\CheckOrderCancelEligibility;
use App\Actions\CreateOrderCancelRequest;
use App\Actions\GetUserEtsyShops;
use App\Enums\OrderStatus;
use App\Models\Order;
use Filament\Actions\Action;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\View;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->contentGrid(['sm' => 1])
            ->modifyQueryUsing(function (Builder $query) {
                return $query->whereAny(['seller_id', 'backup_seller_id'], user()->id)
                    ->with(['lineItems.shipment.carrier', 'etsyShop', 'lineItems']);
            })
            ->recordUrl(null)
            ->columns([
                Split::make([
                    TextColumn::make('order_date')
                        ->description('Order Date', 'above')
                        ->dateTime(),
                    TextColumn::make('etsy_shop_name')
                        ->state(fn (Order $record) => $record->shopName())
                        ->description('Etsy Shop', 'above'),
                    TextColumn::make('number_portal')
                        ->copyable()
                        ->description('Order TDA number', 'above')
                        ->badge()
                        ->color('gray')
                        ->searchable(),
                    TextColumn::make('order_number')
                        ->copyable()
                        ->description('Order number', 'above')
                        ->badge()
                        ->color('gray')
                        ->searchable(),
                    TextColumn::make('status')
                        ->description('Status', 'above')
                        ->badge(),
                    TextColumn::make('total')
                        ->description('Total', 'above')
                        ->recordMoney(),
                ]),

                TextColumn::make('shippingAddress.fullAddress')
                    ->label('Shipping Address')
                    ->description('Shipping address', 'above'),
                View::make('seller.orders-table-lines')
                    ->collapsible(false)
                    ->collapsed(false),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Filter::make('shop_id_platform')
                    ->schema([
                        Select::make('etsy_shop_id')
                            ->options(
                                Cache::remember('etsy-shops-'.user()->id, 60, fn () => GetUserEtsyShops::make()->handle(user())->pluck('username', 'id_etsy'))
                            )
                            ->searchable()
                            ->label('Etsy Shop'),
                    ])
                    ->query(function (\Illuminate\Contracts\Database\Eloquent\Builder $query, array $data) {
                        if (! blank($data['etsy_shop_id'])) {
                            $query->where('shop_id_platform', $data['etsy_shop_id']);
                        }

                        return $query;
                    }),
            ])
            ->deferFilters(false)
            ->recordActions([
                ViewAction::make(),
                Action::make('request_cancel')
                    ->label('Request Cancel')
                    ->icon(Heroicon::OutlinedXCircle)
                    ->color('warning')
                    ->visible(fn (Order $record) => in_array($record->status, [OrderStatus::New, OrderStatus::Processing]) && ! $record->hasPendingCancelRequest())
                    ->modalHeading(fn (Order $record) => "Request Cancel - Order {$record->order_number}")
                    ->schema([
                        Textarea::make('reason')
                            ->label('Cancellation Reason')
                            ->required()
                            ->minLength(10)
                            ->placeholder('Please provide a detailed reason for cancelling this order...')
                            ->rows(4)
                            ->helperText('Minimum 10 characters required'),
                    ])
                    ->action(function (Order $record, array $data) {
                        try {
                            // Check eligibility
                            $eligibility = CheckOrderCancelEligibility::make()->handle($record);
                            if (! $eligibility['eligible']) {
                                throw new \Exception($eligibility['reason']);
                            }

                            // Create the cancel request
                            CreateOrderCancelRequest::make()->handle(
                                $record,
                                user(),
                                $data['reason']
                            );

                            Notification::make()
                                ->title('Cancel request submitted successfully')
                                ->body('Your cancel request has been submitted and is pending review by the fulfillment team.')
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error submitting cancel request')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->toolbarActions([]);
    }
}
