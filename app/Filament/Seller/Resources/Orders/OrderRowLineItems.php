<?php

namespace App\Filament\Seller\Resources\Orders;

use App\Models\Order;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Flex;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Contracts\HasSchemas;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Number;
use Livewire\Component;

class OrderRowLineItems extends Component implements HasSchemas
{
    use InteractsWithSchemas;

    public ?Order $order = null;

    public function mount(Order $order) {}

    public function infolist(Schema $schema): Schema
    {
        $state = [];
        foreach ($this->order->lineItems as $lineItem) {
            $state[] = [
                'product_name' => $lineItem->product_name,
                'status' => $lineItem->status,
                'product_image_url' => $lineItem->product_image_url,
                'quantity' => $lineItem->quantity,
                'price' => Number::currency($lineItem->price / 100, $lineItem->currency_code),
                'number_portal' => $lineItem->number_portal,
                'sku' => $lineItem->sku,
                'tracking_number' => $lineItem->shipment?->tracking_number,
                'carrier' => $lineItem->shipment?->carrier?->name,
            ];
        }

        return $schema
            ->constantState([
                'line-items'.$this->order->id => $state,
            ])
            ->schema([
                RepeatableEntry::make('line-items'.$this->order->id)
                    ->columnSpanFull()
                    ->hiddenLabel()
                    ->gap(0)
                    ->schema([
                        Flex::make([
                            ImageEntry::make('product_image_url')
                                ->hiddenLabel()
                                ->imageSize(69)
                                ->grow(false),
                            Group::make([
                                TextEntry::make('product_name')
                                    ->label('Name')
                                    ->inlineLabel()
                                    ->weight(FontWeight::Medium),
                                TextEntry::make('status')
                                    ->inlineLabel()
                                    ->badge(),
                                TextEntry::make('number_portal')
                                    ->badge()
                                    ->color('gray')
                                    ->label('Item TDA number')
                                    ->inlineLabel(),
                                TextEntry::make('sku')
                                    ->label('SKU')
                                    ->inlineLabel(),
                                TextEntry::make('quantity')
                                    ->label('Quantity')
                                    ->inlineLabel(),
                                TextEntry::make('price')
                                    ->label('Price')
                                    ->inlineLabel(),
                                TextEntry::make('tracking_number')
                                    ->copyable()
                                    ->badge()
                                    ->inlineLabel(),
                                TextEntry::make('carrier')
                                    ->inlineLabel(),
                            ])
                                ->columns(2)
                                ->grow()
                                ->gap(0),
                        ]),
                    ]),
            ]);
    }

    public function render(): string
    {
        return <<<'BLADE'
<div>
    {{ $this->infolist }}
</div>
BLADE;

    }
}
