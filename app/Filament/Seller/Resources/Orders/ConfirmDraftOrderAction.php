<?php

namespace App\Filament\Seller\Resources\Orders;

use App\Actions\ConfirmDraftOrder;
use App\Models\Order;
use Filament\Actions\Action;
use Filament\Support\Icons\Heroicon;

class ConfirmDraftOrderAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Confirm Order')
            ->icon(Heroicon::OutlinedCheckCircle)
            ->requiresConfirmation()
            ->action(function (Order $record) {
                ConfirmDraftOrder::make()->handle(user(), $record);
            });
    }
}
