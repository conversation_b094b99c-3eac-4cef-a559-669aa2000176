<?php

namespace App\Filament\Seller\Resources\Orders\Pages;

use App\Enums\OrderStatus;
use App\Filament\Seller\Resources\Orders\OrderResource;
use App\Models\Order;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs\Tab;
use Illuminate\Contracts\Database\Eloquent\Builder;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All'),
        ];

        foreach (
            [
                OrderStatus::New,
                OrderStatus::Processing,
                OrderStatus::PartialShipped,
                OrderStatus::Shipped,
                OrderStatus::Cancelled,
                OrderStatus::Draft,
            ] as $case
        ) {
            $tabs[$case->value] = Tab::make($case->getLabel())
                ->query(fn (Builder $query) => $query->where('status', $case))
                ->badgeColor($case->getColor())
                ->badge(
                    in_array($case, [
                        OrderStatus::New,
                        OrderStatus::Processing,
                        OrderStatus::PartialShipped,
                        OrderStatus::Draft,
                    ]) ? Order::query()->whereAny(['seller_id', 'backup_seller_id'], user()->id)
                        ->where('status', $case)->count() : null
                );
        }

        return $tabs;
    }
}
