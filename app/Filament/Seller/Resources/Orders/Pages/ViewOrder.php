<?php

namespace App\Filament\Seller\Resources\Orders\Pages;

use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Seller\Resources\Orders\OrderResource;
use Filament\Resources\Pages\ViewRecord;

class ViewOrder extends ViewRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make(),
        ];
    }
}
