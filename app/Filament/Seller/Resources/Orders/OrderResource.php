<?php

namespace App\Filament\Seller\Resources\Orders;

use App\Filament\Seller\Resources\Orders\Pages\EditOrder;
use App\Filament\Seller\Resources\Orders\Pages\ListOrders;
use App\Filament\Seller\Resources\Orders\Pages\ViewOrder;
use App\Filament\Seller\Resources\Orders\RelationManagers\LineItemsRelationManager;
use App\Filament\Seller\Resources\Orders\RelationManagers\ShippingAddressRelationManager;
use App\Filament\Seller\Resources\Orders\Schemas\OrderForm;
use App\Filament\Seller\Resources\Orders\Schemas\OrderInfolist;
use App\Filament\Seller\Resources\Orders\Tables\OrdersTable;
use App\Filament\TranslatableResource;
use App\Models\Order;
use BackedEnum;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class OrderResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = Order::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedShoppingCart;

    protected static string|UnitEnum|null $navigationGroup = 'Settings';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereAny(['seller_id', 'backup_seller_id'], user()->id)
            ->with(['cancelRequests']);
    }

    public static function infolist(Schema $schema): Schema
    {
        return OrderInfolist::configure($schema);
    }

    public static function form(Schema $schema): Schema
    {
        return OrderForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return OrdersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            RelationGroup::make('', [
                ShippingAddressRelationManager::make(),
                LineItemsRelationManager::make(),
            ]),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrders::route('/'),
            'view' => ViewOrder::route('/{record}'),
            // 'edit' => EditOrder::route('/{record}/edit'),
        ];
    }
}
