<?php

namespace App\Filament\Seller\Resources\Orders;

use App\Actions\CancelOrder;
use Filament\Actions\Action;
use Filament\Support\Icons\Heroicon;

class CancelOrderAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Cancel Order')
            ->icon(Heroicon::OutlinedXCircle)
            ->color('danger')
            ->requiresConfirmation()
            ->action(function ($record) {
                CancelOrder::make()->handle($record);
            });
    }
}
