<?php

namespace App\Filament\Seller\Resources\Orders\Schemas;

use App\Enums\OrderStatus;
use App\Enums\Platform;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('seller_id')
                    ->relationship('seller', 'name'),
                TextInput::make('backup_seller_id')
                    ->numeric(),
                TextInput::make('orders_import_request_id')
                    ->numeric(),
                TextInput::make('order_number')
                    ->required(),
                DateTimePicker::make('order_date'),
                Select::make('platform')
                    ->options(Platform::class),
                TextInput::make('id_platform')
                    ->numeric(),
                TextInput::make('shop_id_platform')
                    ->numeric(),
                Select::make('status')
                    ->options(OrderStatus::class)
                    ->default('draft')
                    ->required(),
                Toggle::make('is_gift')
                    ->required(),
                Toggle::make('is_gift_wrapped')
                    ->required(),
                Textarea::make('gift_message')
                    ->columnSpanFull(),
                TextInput::make('currency_code'),
                TextInput::make('total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('subtotal')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('shipping_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('tax_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('discount_total')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('cost_breakdown'),
                DatePicker::make('date_paid'),
                TextInput::make('payment_method'),
                DateTimePicker::make('expected_ship_date'),
                DateTimePicker::make('seller_confirmed_at'),
                TextInput::make('meta_data'),
            ]);
    }
}
