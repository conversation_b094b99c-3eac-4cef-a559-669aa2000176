<?php

namespace App\Filament\Seller\Resources\Orders\Schemas;

use App\Models\Order;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class OrderInfolist
{
    public static function configure(Schema $infolist): Schema
    {
        return $infolist
            ->schema([
                Section::make()
                    ->heading('Order Information')
                    ->columnSpanFull()
                    ->columns(2)
                    ->schema([
                        TextEntry::make('number_portal')
                            ->label('Order Number TDA')
                            ->copyable()
                            ->badge(),
                        TextEntry::make('order_number')
                            ->label('Order Number')
                            ->copyable()
                            ->badge(),
                        TextEntry::make('shop_id_platform')
                            ->label('Shop ID'),
                        TextEntry::make('order_date')
                            ->dateTime(),
                        TextEntry::make('status')
                            ->badge(),
                        TextEntry::make('platform'),
                        TextEntry::make('id_platform'),
                        IconEntry::make('is_gift')
                            ->boolean(),
                        IconEntry::make('is_gift_wrapped')
                            ->boolean(),
                        TextEntry::make('gift_message'),
                        TextEntry::make('subtotal')
                            ->recordMoney(),
                        TextEntry::make('shipping_total')
                            ->recordMoney(),
                        TextEntry::make('total')
                            ->recordMoney(),
                        TextEntry::make('expected_ship_date')
                            ->dateTime(),
                        TextEntry::make('created_at')
                            ->label('Imported at')
                            ->dateTime(),
                    ]),

                Section::make()
                    ->heading('Tax or VAT information')
                    ->columnSpanFull()
                    ->columns(2)
                    ->schema([
                        TextEntry::make('getTaxLabel')
                            ->label('Tax or VAT label')
                            ->state(fn (Order $record) => $record->getTaxLabel()),
                        TextEntry::make('getTaxMessage')
                            ->label('Tax or VAT message')
                            ->state(fn (Order $record) => $record->getTaxMessage()),
                    ]),
            ]);
    }
}
