<?php

namespace App\Filament\Seller\Resources\Orders\RelationManagers;

use App\Models\LineItem;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->schema([
                TextInput::make('product_name')
                    ->required(),
                TextInput::make('sku')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Items')
            ->columns([
                ImageColumn::make('product_image_url')
                    ->label('Image'),
                TextColumn::make('product_name')
                    ->grow(true)
                    ->extraAttributes(['style' => 'width: 322px'])
                    ->wrap()
                    ->suffix(
                        fn (LineItem $record) => $record->productUrl()
                            ? Action::make('view-product-'.$record->id)
                                ->hiddenLabel()
                                ->link()
                                ->color('gray')
                                ->icon(Heroicon::OutlinedArrowTopRightOnSquare)
                                ->url($record->productUrl())
                                ->openUrlInNewTab()
                            : null
                    ),
                TextColumn::make('sku')
                    ->label('SKU'),
                TextColumn::make('quantity')
                    ->numeric()
                    ->alignRight(),
                TextColumn::make('price')
                    ->recordMoney(),
                TextColumn::make('subtotal')
                    ->recordMoney(),
                TextColumn::make('product_type'),
                TextColumn::make('color'),
                TextColumn::make('size'),
                TextColumn::make('personalization'),
                IconColumn::make('is_personalizable')
                    ->boolean(),
                TextColumn::make('shipment.tracking_number')
                    ->copyable()
                    ->badge()
                    ->label('Tracking Number'),
                TextColumn::make('shipment.carrier.name')
                    ->label('Carrier'),
                TextColumn::make('attributes')
                    ->state(fn (LineItem $record) => $record->variationsLines())
                    ->bulleted(),
                TextColumn::make('seller_note')
                    ->wrap(),
            ])
            ->minimal()
            ->recordActions([
                // EditAction::make(),
            ]);
    }
}
