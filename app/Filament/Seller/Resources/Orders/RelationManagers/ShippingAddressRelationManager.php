<?php

namespace App\Filament\Seller\Resources\Orders\RelationManagers;

use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ShippingAddressRelationManager extends RelationManager
{
    protected static string $relationship = 'shippingAddress';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('line_one')
                    ->label('Address Line 1'),
                TextColumn::make('line_two')
                    ->label('Address Line 2'),
                TextColumn::make('city'),
                TextColumn::make('state'),
                TextColumn::make('zip')
                    ->label('ZIP Code'),
                TextColumn::make('country'),
                TextColumn::make('phone'),
                TextColumn::make('email'),
                IconColumn::make('is_usps_verified')
                    ->boolean()
                    ->label('USPS Verified'),
            ])
            ->minimal();
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                TextInput::make('name')
                    ->required(),
                TextInput::make('line_one')
                    ->label('Address Line 1')
                    ->required(),
                TextInput::make('line_two')
                    ->label('Address Line 2'),
                TextInput::make('city')
                    ->required(),
                TextInput::make('state'),
                TextInput::make('zip')
                    ->label('ZIP Code')
                    ->required(),
                TextInput::make('country')
                    ->required(),
                TextInput::make('phone'),
                TextInput::make('email')
                    ->email(),
            ]);
    }
}
