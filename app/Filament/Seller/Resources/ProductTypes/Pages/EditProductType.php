<?php

namespace App\Filament\Seller\Resources\ProductTypes\Pages;

use App\Filament\Seller\Resources\ProductTypes\ProductTypeResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditProductType extends EditRecord
{
    protected static string $resource = ProductTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
