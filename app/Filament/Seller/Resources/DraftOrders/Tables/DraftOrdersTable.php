<?php

namespace App\Filament\Seller\Resources\DraftOrders\Tables;

use App\Actions\ConfirmDraftOrder;
use App\Models\Order;
use Filament\Actions\BulkAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\View;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;

class DraftOrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->contentGrid(['sm' => 1])
            ->modifyQueryUsing(function ($query) {
                return $query->with(['lineItems.shipment.carrier', 'etsyShop', 'lineItems']);
            })
            ->recordUrl(null)
            ->columns([
                Split::make([
                    TextColumn::make('order_date')
                        ->description('Order Date', 'above')
                        ->dateTime(),
                    TextColumn::make('etsy_shop_name')
                        ->state(fn (Order $record) => $record->shopName())
                        ->description('Etsy Shop', 'above'),
                    TextColumn::make('number_portal')
                        ->description('Order TDA number', 'above')
                        ->badge()
                        ->color('gray')
                        ->searchable()
                        ->copyable(),
                    TextColumn::make('order_number')
                        ->description('Order number', 'above')
                        ->badge()
                        ->copyable()
                        ->color('gray')
                        ->searchable(),
                    TextColumn::make('status')
                        ->description('Status', 'above')
                        ->badge(),
                    TextColumn::make('total')
                        ->description('Total', 'above')
                        ->recordMoney(),
                ]),
                View::make('seller.draft-orders-table-customer-note'),

                View::make('seller.draft-orders-table-lines'),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make()
                    ->url(fn (Order $record) => route('filament.seller.resources.orders.view', ['record' => $record]))
                    ->openUrlInNewTab(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkAction::make('bulk-confirm-orders')
                    ->label('Confirm Orders')
                    ->icon('heroicon-o-check-badge')
                    ->tooltip('So pls remember to complete all lines of an order before you confirm.')
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion()
                    ->action(function (Collection $records) {
                        // validate first, if any order has line items
                        // we throw notification and stop the action
                        /** @var Order $record */
                        foreach ($records as $record) {
                            if ($record->hasLineItemWithoutProductType()) {
                                Notification::make()
                                    ->title('Cannot confirm order: '.$record->number_portal)
                                    ->body('Please ensure all line items have a product type before confirming.')
                                    ->danger()
                                    ->send();

                                return;
                            }
                        }

                        foreach ($records as $order) {
                            ConfirmDraftOrder::make()->handle(user(), $order);
                        }

                        Notification::make()
                            ->title('Orders confirmed successfully')
                            ->success()
                            ->send();
                    }),
            ]);
    }
}
