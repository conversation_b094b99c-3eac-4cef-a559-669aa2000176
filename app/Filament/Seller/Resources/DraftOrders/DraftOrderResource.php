<?php

namespace App\Filament\Seller\Resources\DraftOrders;

use App\Enums\OrderStatus;
use App\Filament\Seller\Resources\DraftOrders\Pages\ListDraftOrders;
use App\Filament\Seller\Resources\DraftOrders\Schemas\DraftOrderForm;
use App\Filament\Seller\Resources\DraftOrders\Schemas\DraftOrderInfolist;
use App\Filament\Seller\Resources\DraftOrders\Tables\DraftOrdersTable;
use App\Models\Order;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class DraftOrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedShoppingCart;

    protected static ?string $modelLabel = 'Draft Order';

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('status', OrderStatus::Draft)
            ->whereAny(['seller_id', 'backup_seller_id'], user()->id);
    }

    public static function form(Schema $schema): Schema
    {
        return DraftOrderForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return DraftOrderInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return DraftOrdersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListDraftOrders::route('/'),
        ];
    }
}
