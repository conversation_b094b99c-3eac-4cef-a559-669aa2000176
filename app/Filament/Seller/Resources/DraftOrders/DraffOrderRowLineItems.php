<?php

namespace App\Filament\Seller\Resources\DraftOrders;

use App\Models\Order;
use Livewire\Component;

class DraffOrderRowLineItems extends Component
{
    public ?Order $order = null;

    public function mount(Order $order): void
    {
        $this->order = $order;
    }

    public function render(): string
    {
        return <<<'BLADE'
<div>
    @foreach($this->order->lineItems as $lineItem)
    @endforeach
</div>
BLADE;
    }
}
