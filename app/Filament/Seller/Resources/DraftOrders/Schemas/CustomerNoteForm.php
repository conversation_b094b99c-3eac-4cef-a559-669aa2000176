<?php

namespace App\Filament\Seller\Resources\DraftOrders\Schemas;

use App\Models\Order;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Contracts\HasSchemas;
use Filament\Schemas\Schema;
use Livewire\Component;

class CustomerNoteForm extends Component implements HasSchemas
{
    use InteractsWithSchemas;

    public Order $order;

    public ?array $data = [];

    public function mount(Order $order): void
    {
        $this->order = $order;

        $this->data = [
            'customer_note' => $order->customer_note,
        ];

        $this->form->fill($this->data);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->statePath('data')
            ->schema([
                TextInput::make('customer_note')
                    ->label('Customer Note')
                    ->placeholder('Enter customer note')
                    ->lazy()
                    ->columnSpanFull()
                    ->afterStateUpdated(function (?string $state) {
                        $this->order->update(['customer_note' => $state]);
                        $this->showUpdateNotification('Customer Note', $state);
                    }),
            ]);
    }

    private function showUpdateNotification(string $field, ?string $value): void
    {
        $message = $value
            ? "{$field} updated: {$value}"
            : "{$field} cleared";

        Notification::make()
            ->title($message)
            ->success()
            ->send();
    }

    public function render(): string
    {
        return <<<'BLADE'
<div class="py-2">
    {{ $this->form }}
</div>
BLADE;
    }
}
