<?php

namespace App\Filament\Seller\Resources\DraftOrders\Schemas;

use App\Models\LineItem;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Fieldset;
use Filament\Schemas\Components\Flex;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Contracts\HasSchemas;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Icons\Heroicon;
use Livewire\Component;

class DraftOrderLineItemForm extends Component implements HasActions, HasSchemas
{
    use InteractsWithActions;
    use InteractsWithSchemas;

    public LineItem $lineItem;

    public ?array $data = null;

    public function mount(LineItem $lineItem): void
    {
        $this->lineItem = $lineItem;

        $this->data = [
            'product_type' => $lineItem->product_type,
            'size' => $lineItem->size,
            'color' => $lineItem->color,
            'personalization' => $lineItem->personalization,
            'product_sku' => $lineItem->sku,
            'seller_note' => $lineItem->seller_note,
            'product_attributes' => $lineItem->attributesString(),
        ];

        $this->form->fill($this->data);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->statePath('data')
            ->extraAttributes(['style' => 'margin-top: 1rem'])
            ->schema([
                Fieldset::make()
                    ->columns(1)
                    ->columnSpanFull()
                    ->schema([
                        Flex::make([
                            ImageEntry::make('product_image_url')
                                ->imageSize(69)
                                ->hiddenLabel()
                                ->state(fn () => $this->lineItem->product_image_url)
                                ->grow(false),
                            Grid::make(2)
                                ->extraAttributes([
                                    'class' => '[&>.fi-sc-has-gap]:gap-3',
                                ])
                                ->gap()
                                ->schema([
                                    TextEntry::make('product_name')
                                        ->hiddenLabel()
                                        ->state(fn () => $this->lineItem->product_name)
                                        ->weight(FontWeight::Medium)
                                        ->columnSpan(2)
                                        ->suffix(
                                            fn () => $this->lineItem->productUrl()
                                                ? Action::make('view-product-'.$this->lineItem->id)
                                                    ->hiddenLabel()
                                                    ->link()
                                                    ->color('gray')
                                                    ->icon(Heroicon::OutlinedArrowTopRightOnSquare)
                                                    ->url($this->lineItem->productUrl())
                                                    ->openUrlInNewTab()
                                                : null
                                        ),
                                    TextEntry::make('quantity')
                                        ->state(fn () => $this->lineItem->quantity)
                                        ->inlineLabel(),
                                    TextEntry::make('price')
                                        ->state(fn () => $this->lineItem->price)
                                        ->inlineLabel()
                                        ->columnSpan(1)
                                        ->money($this->lineItem->currency_code, divideBy: 100),
                                    TextInput::make('product_type')
                                        ->label('Product Type')
                                        ->required()
                                        ->inlineLabel()
                                        ->placeholder('Enter product type')
                                        ->lazy()
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->update(['product_type' => $state]);
                                            $this->showUpdateNotification('Product Type', $state);
                                        }),

                                    TextInput::make('size')
                                        ->label('Size')
                                        ->inlineLabel()
                                        ->placeholder('Enter size')
                                        ->lazy()
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->setSize($state)->save();
                                            $this->showUpdateNotification('Size', $state);
                                        }),

                                    TextInput::make('color')
                                        ->label('Color')
                                        ->inlineLabel()
                                        ->placeholder('Enter color')
                                        ->lazy()
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->setColor($state)->save();
                                            $this->showUpdateNotification('Color', $state);
                                        }),

                                    TextInput::make('product_sku')
                                        ->label('SKU')
                                        ->inlineLabel()
                                        ->placeholder('Enter SKU')
                                        ->lazy()
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->update(['sku' => $state]);
                                            $this->showUpdateNotification('SKU', $state);
                                        }),

                                    Textarea::make('personalization')
                                        ->label('Personalization')
                                        ->placeholder('Enter personalization')
                                        ->lazy()
                                        ->autosize()
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->setPersonalization($state)->save();
                                            $this->showUpdateNotification('Personalization', $state);
                                        }),

                                    Textarea::make('seller_note')
                                        ->label('Seller Note')
                                        ->placeholder('Enter seller note')
                                        ->live(onBlur: true)
                                        ->autosize() // tự động co giãn chiều cao
                                        ->afterStateUpdated(function (?string $state) {
                                            $this->lineItem->update(['seller_note' => $state]);
                                            $this->showUpdateNotification('Seller Note', $state);
                                        }),

                                    TextEntry::make('product_attributes')
                                        ->state(fn () => $this->lineItem->attributesString())
                                        ->columnSpanFull()
                                        ->html(),
                                ]),
                        ]),
                    ]),
            ]);
    }

    private function showUpdateNotification(string $field, ?string $value): void
    {
        $message = $value
            ? "{$field} updated: {$value}"
            : "{$field} cleared";

        Notification::make()
            ->title($message)
            ->success()
            ->send();
    }

    public function render(): string
    {
        return <<<'BLADE'
<div>
    {{ $this->form }}
</div>
BLADE;
    }
}
