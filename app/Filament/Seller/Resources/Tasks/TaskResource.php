<?php

namespace App\Filament\Seller\Resources\Tasks;

use App\Enums\TaskStatus;
use Illuminate\Database\Eloquent\Builder;

class TaskResource extends \App\Filament\Backoffice\Resources\Tasks\TaskResource
{
    public static function getEloquentQuery(): Builder
    {
        if (user()->isManager()) {
            return parent::getEloquentQuery();
        }

        return parent::getEloquentQuery()->where('owner_id', user()->id);
    }

    public static function getNavigationBadge(): ?string
    {
        if (user()->isSeller()) {
            return static::getEloquentQuery()->where('status', TaskStatus::Submitted)->count() ?: null;
        }

        return null;
    }
}
