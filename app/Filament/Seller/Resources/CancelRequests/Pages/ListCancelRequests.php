<?php

namespace App\Filament\Seller\Resources\CancelRequests\Pages;

use App\Filament\Seller\Resources\CancelRequests\Actions\CreateCancelRequestAction;
use App\Filament\Seller\Resources\CancelRequests\CancelRequestResource;
use Filament\Resources\Pages\ListRecords;

class ListCancelRequests extends ListRecords
{
    protected static string $resource = CancelRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateCancelRequestAction::make(),
        ];
    }
}
