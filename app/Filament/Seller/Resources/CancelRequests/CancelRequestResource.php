<?php

namespace App\Filament\Seller\Resources\CancelRequests;

use App\Enums\OrderCancelRequestStatus;
use App\Filament\Backoffice\Resources\OrderCancelRequests\Schemas\OrderCancelRequestInfolist;
use App\Filament\Seller\Resources\CancelRequests\Pages\ListCancelRequests;
use App\Filament\Seller\Resources\CancelRequests\Pages\ViewCancelRequest;
use App\Filament\TranslatableResource;
use App\Models\OrderCancelRequest;
use BackedEnum;
use Filament\Actions\DeleteAction;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Colors\Color;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class CancelRequestResource extends Resource
{
    use TranslatableResource;

    protected static ?string $model = OrderCancelRequest::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedHandRaised;

    protected static ?string $navigationLabel = 'Order Cancel Requests';

    protected static string|UnitEnum|null $navigationGroup = 'Workspace';

    protected static ?int $navigationSort = 2;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('seller_id', auth()->id());
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->pending()->count() ?: null;
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return Color::Amber;
    }

    public static function infolist(Schema $schema): Schema
    {
        return OrderCancelRequestInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('created_at')
                    ->label('Request Date')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('order.number_portal')
                    ->label('Order TDA number')
                    ->badge()
                    ->color('gray')
                    ->copyable()
                    ->searchable(),
                TextColumn::make('order.order_number')
                    ->label('Order Number')
                    ->badge()
                    ->color('gray')
                    ->copyable()
                    ->searchable(),
                TextColumn::make('reason')
                    ->label('Reason')
                    ->copyable()
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        return strlen($state) > 50 ? $state : null;
                    }),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('processedBy.name')
                    ->label('Processed By'),
                TextColumn::make('processed_at')
                    ->label('Processing Date')
                    ->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(OrderCancelRequestStatus::class),
            ])
            ->recordActions([
                DeleteAction::make()->visible(fn (OrderCancelRequest $record) => $record->isPending()),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCancelRequests::route('/'),
            'view' => ViewCancelRequest::route('/{record}'),
        ];
    }
}
