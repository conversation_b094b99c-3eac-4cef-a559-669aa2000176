<?php

namespace App\Filament\Seller\Resources\CancelRequests\Actions;

use App\Actions\CheckOrderCancelEligibility;
use App\Actions\CreateOrderCancelRequest;
use App\Enums\OrderStatus;
use App\Models\Order;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;

class CreateCancelRequestAction
{
    public static function make(): Action
    {
        return Action::make('create_cancel_request')
            ->label('Create New Cancel Request')
            ->icon(Heroicon::OutlinedPlus)
            ->schema([
                Select::make('order_id')
                    ->label('Select Order')
                    ->required()
                    ->searchable()
                    ->autofocus()
                    ->placeholder('Please enter TDA order number...')
                    ->getSearchResultsUsing(function (string $search) {
                        return Order::query()
                            ->whereAny(['seller_id', 'backup_seller_id'], user()->id)
                            ->whereAny(['number_portal', 'order_number'], $search)
                            ->whereNotIn('status', [OrderStatus::Cancelled, OrderStatus::Shipped])
                            ->limit(10)
                            ->get()
                            ->mapWithKeys(function ($order) {
                                return [$order->id => "{$order->number_portal} - {$order->order_number} ({$order->status->getLabel()})"];
                            })
                            ->toArray();
                    })
                    ->getOptionLabelUsing(function ($value) {
                        $order = Order::find($value);
                        if (! $order) {
                            return $value;
                        }

                        return "{$order->number_portal} - {$order->order_number} ({$order->status->getLabel()})";
                    })
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state) {
                            $order = Order::find($state);
                            if ($order) {
                                $eligibility = CheckOrderCancelEligibility::make()->handle($order);
                                if (! $eligibility['eligible']) {
                                    $set('order_id', null);
                                    Notification::make()
                                        ->title('Order cannot be cancelled')
                                        ->body($eligibility['reason'])
                                        ->danger()
                                        ->send();
                                }
                            }
                        }
                    }),

                Textarea::make('reason')
                    ->label('Cancellation Reason')
                    ->placeholder('Please provide a detailed reason for cancelling this order...')
                    ->required()
                    ->minLength(10)
                    ->maxLength(1000)
                    ->rows(4)
                    ->helperText('Please provide at least 10 characters explaining why you want to cancel this order.'),
            ])
            ->action(function (array $data) {
                try {
                    $order = Order::find($data['order_id']);

                    if (! $order) {
                        throw new \Exception('Order not found.');
                    }

                    // Double-check eligibility
                    $eligibility = CheckOrderCancelEligibility::make()->handle($order);
                    if (! $eligibility['eligible']) {
                        throw new \Exception($eligibility['reason']);
                    }

                    CreateOrderCancelRequest::make()->handle(
                        $order,
                        auth()->user(),
                        $data['reason']
                    );

                    Notification::make()
                        ->title('Cancel request submitted successfully')
                        ->body('Your cancel request has been submitted and is pending review by the fulfillment team.')
                        ->success()
                        ->send();

                } catch (\Exception $e) {
                    Notification::make()
                        ->title('Error submitting cancel request')
                        ->body($e->getMessage())
                        ->danger()
                        ->send();
                }
            });
    }
}
