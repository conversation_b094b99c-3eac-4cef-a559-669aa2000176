<?php

namespace App\Filament\Designer\Widgets;

use App\Enums\DesignStatus;
use App\Models\Design;
use Filament\Support\Icons\Heroicon;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class DesignStatusWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $userId = Auth::id();

        return [];

        return [
            Stat::make('Todo Designs', Design::where('user_id', $userId)->where('status', DesignStatus::Todo)->count())
                ->descriptionIcon(Heroicon::OutlinedClock)
                ->color('warning'),

            Stat::make('Need Revision Designs', Design::where('user_id', $userId)->where('status', DesignStatus::NeedRevision)->count())
                ->descriptionIcon(Heroicon::OutlinedExclamationTriangle)
                ->color('danger'),

            Stat::make('Processing Designs', Design::where('user_id', $userId)->where('status', DesignStatus::Processing)->count())
                ->descriptionIcon(Heroicon::OutlinedBeaker)
                ->color('info'),
        ];
    }
}
