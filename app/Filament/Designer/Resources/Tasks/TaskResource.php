<?php

namespace App\Filament\Designer\Resources\Tasks;

use App\Filament\Designer\Resources\Tasks\Pages\CreateTask;
use App\Filament\Designer\Resources\Tasks\Pages\EditTask;
use App\Filament\Designer\Resources\Tasks\Pages\ListTasks;
use App\Filament\Designer\Resources\Tasks\Schemas\TaskForm;
use App\Filament\Designer\Resources\Tasks\Tables\TasksTable;
use App\Models\Task;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use UnitEnum;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedSquare3Stack3d;

    protected static ?string $recordTitleAttribute = 'title';

    protected static string | UnitEnum | null $navigationGroup = 'Workspace';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('assignee_id', user()->id)
            ->with([
                'designs.idea.media',
                'designs.productType'
            ]);
    }

    public static function form(Schema $schema): Schema
    {
        return TaskForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return TasksTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTasks::route('/'),
            'create' => CreateTask::route('/create'),
            'edit' => EditTask::route('/{record}/edit'),
        ];
    }
}
