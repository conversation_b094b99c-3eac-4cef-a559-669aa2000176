<?php

namespace App\Filament\Designer\Resources\Tasks\Schemas;

use App\Models\Design;
use App\Models\Task;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Fieldset;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\View;
use Filament\Schemas\Schema;
use Illuminate\Database\Eloquent\Builder;

class TaskForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make(3)
                    ->columnSpanFull()
                    ->schema([
                        // Task information section
                        Group::make()
                            ->columnSpan(2)
                            ->columns(2)
                            ->schema([
                                Section::make()
                                    ->columns(2)
                                    ->heading('Task information')
                                    ->schema([
                                        TextEntry::make('title'),
                                        TextEntry::make('status')
                                            ->badge(),
                                        TextEntry::make('description')
                                            ->columnSpanFull(),
                                    ]),

                                Repeater::make('designs')
                                    ->relationship(
                                        'designs',
                                        modifyQueryUsing: function (Builder $query) {
                                            return $query->with(['idea.media', 'productType', 'media']);
                                        })
                                    ->columnSpanFull()
                                    ->itemLabel(function (array $state) {
                                        return '#' . $state['id'];
                                    })
                                    ->columns(2)
                                    ->deletable(false)
                                    ->addable(false)
                                    ->collapsible()
                                    ->schema([
                                        TextEntry::make('productType.name')
                                            ->label('Product Type'),
                                        TextEntry::make('idea.name')
                                            ->label('Idea Name'),
                                        SpatieMediaLibraryImageEntry::make('idea.featured_image')
                                            ->collection('featured_image')
                                            ->label('Idea image'),

                                        Section::make('Designer works')
                                            ->description('Designer files will be uploaded here')
                                            ->columnSpanFull()
                                            ->schema([
                                                SpatieMediaLibraryFileUpload::make('featured_image')
                                                    ->label('Featured image')
                                                    ->collection('featured_image')
                                                    ->panelLayout('grid')
                                                    ->columnSpanFull()
                                                    ->image(),

                                                SpatieMediaLibraryFileUpload::make('gallery_images')
                                                    ->label('Gallery images')
                                                    ->collection('gallery_images')
                                                    ->multiple()
                                                    ->reorderable()
                                                    ->downloadable()
                                                    ->columnSpanFull()
                                                    ->panelLayout('grid'),

                                                Textarea::make('source_file_urls')
                                                    ->columnSpanFull()
                                            ])
                                    ])
                            ]),

                        // Comments section
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                View::make('conversation')
                                    ->columnSpanFull()
                                    ->view('filament.components.conversation-wrapper')
                                    ->viewData(function (Task $record) {
                                        return [
                                            'record' => $record,
                                        ];
                                    }),
                            ]),
                    ])
            ]);
    }
}
