<?php

namespace App\Filament\Designer\Resources\Tasks\Pages;

use App\Actions\MarkTaskAsProcessing;
use App\Actions\SubmitTask;
use App\Filament\Designer\Resources\Tasks\TaskResource;
use App\Models\Task;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditTask extends EditRecord
{
    protected static string $resource = TaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Mark task as processing
            Action::make('mark-as-processing')
                ->visible(fn(Task $record) => $record->isTodo())
                ->action(function (Task $task) {
                    MarkTaskAsProcessing::make()->handle($task);

                    Notification::make()
                        ->title('Task marked as processing')
                        ->body('The task is now being processed.')
                        ->success()
                        ->send();
                }),

            // Submit the task
            Action::make('submit-task')
                ->visible(fn(Task $record) => $record->isProcessing() || $record->isNeedRevision())
                ->action(function (Task $task) {
                    SubmitTask::make()->handle($task);

                    Notification::make()
                        ->title('Task submitted successfully')
                        ->body('Your task has been submitted for review.')
                        ->success()
                        ->send();
                }),

            DeleteAction::make(),
        ];
    }
}
