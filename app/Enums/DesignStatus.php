<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum DesignStatus: string implements HasColor
{
    case Todo = 'todo';
    case Processing = 'processing';
    case NeedRevision = 'need_revision';
    case Submitted = 'submitted';
    case Approved = 'approved';

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Todo => 'gray',
            self::Processing => 'primary',
            self::NeedRevision => 'danger',
            self::Submitted => 'warning',
            self::Approved => 'success',
        };
    }
}
