<?php

namespace App\Enums;

use Filament\Support\Colors\Color;
use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

enum UserRole: string implements HasColor, HasLabel
{
    case Seller = 'seller';
    case Designer = 'designer';
    case Operator = 'operator';
    case Manager = 'manager';

    public function getLabel(): string|Htmlable|null
    {
        return Str::headline($this->value);
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Seller, self::Designer => Color::Amber,
            self::Operator => Color::Indigo,
            self::Manager => Color::Rose,
        };
    }
}
