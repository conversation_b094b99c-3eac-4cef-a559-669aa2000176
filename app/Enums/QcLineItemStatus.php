<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

enum QcLineItemStatus: string implements HasColor, HasLabel
{
    case Ready = 'ready';
    case Missing = 'missing';

    public function getLabel(): string|Htmlable|null
    {
        return Str::headline($this->value);
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Ready => 'success',
            self::Missing => 'danger',
        };
    }
}
