<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

enum QcOrderCheckStatus: string implements HasColor, HasLabel
{
    case Processing = 'processing';
    case Completed = 'completed';

    public function getLabel(): string|Htmlable|null
    {
        return Str::headline($this->value);
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Processing => 'info',
            self::Completed => 'success',
        };
    }
}
