<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum LineItemStatus: string implements HasColor
{
    case New = 'new';
    case InProduction = 'in-production';
    case Packing = 'packing';
    case Packed = 'packed';
    case Shipped = 'shipped';
    case Cancelled = 'cancelled';

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::New => 'gray',
            self::InProduction => 'warning',
            self::Packing => 'info',
            self::Packed => 'primary',
            self::Shipped => 'success',
            self::Cancelled => 'danger',
        };
    }
}
