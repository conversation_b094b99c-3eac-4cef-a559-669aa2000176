<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

enum OrderStatus: string implements HasColor, HasLabel
{
    case Draft = 'draft';
    case New = 'new';
    case Processing = 'processing';
    case PartialShipped = 'partial-shipped';
    case Shipped = 'shipped';
    case Cancelled = 'cancelled';

    public function getLabel(): string|Htmlable|null
    {
        return Str::headline($this->value);
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Draft => 'gray',
            self::New => 'warning',
            self::Processing => 'info',
            self::PartialShipped => 'info',
            self::Shipped => 'success',
            self::Cancelled => 'danger',
            default => 'primary'
        };
    }
}
