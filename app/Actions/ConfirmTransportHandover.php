<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\ShipmentStatus;
use App\Enums\TransportHandoverStatus;
use App\Models\Shipment;
use App\Models\TransportHandover;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ConfirmTransportHandover
{
    use AsAction;

    public function handle(TransportHandover $transportHandover): void
    {
        DB::transaction(function () use ($transportHandover) {
            // Update the transport handover status to confirmed
            $transportHandover->update([
                'status' => TransportHandoverStatus::Confirmed,
                'confirmed_at' => now(),
            ]);

            // Parse tracking numbers from the stored text
            $trackingNumbers = $transportHandover->getParsedTrackingNumbers();

            // Get shipments with the tracking numbers
            $shipments = Shipment::whereIn('tracking_number', $trackingNumbers)
                ->where('packer', 'TDA')
                ->with(['lineItems.order'])
                ->get();

            // Collect all affected orders for status update
            $affectedOrders = collect();

            // Update shipments to PickedUp status and set third party logistics
            foreach ($shipments as $shipment) {
                $shipment->update([
                    'status' => ShipmentStatus::PickedUp,
                    'shipped_at' => now(),
                    'third_party_logistic_id' => $transportHandover->third_party_logistic_id,
                ]);

                // Update all line items in this shipment to Shipped status
                $shipment->lineItems->each->update([
                    'status' => LineItemStatus::Shipped,
                ]);

                // Collect unique orders for status update
                foreach ($shipment->lineItems as $lineItem) {
                    if ($lineItem->order && ! $affectedOrders->contains('id', $lineItem->order->id)) {
                        $affectedOrders->push($lineItem->order);
                    }
                }
            }

            // Update shipping status for all affected orders
            foreach ($affectedOrders as $order) {
                UpdateOrderShippingStatus::make()->handle($order);
            }
        });
    }
}
