<?php

namespace App\Actions;

use App\Enums\QcOrderCheckStatus;
use App\Models\Order;
use App\Models\QcSession;
use Lorisleiva\Actions\Concerns\AsAction;

class RemoveOrderFromQc
{
    use AsAction;

    public function handle(QcSession $qcSession, Order $order): void
    {
        $qcSession->orderChecks()
            ->where('order_id', $order->id)
            ->where('status', QcOrderCheckStatus::Processing)
            ->sole()
            ->delete();
    }
}
