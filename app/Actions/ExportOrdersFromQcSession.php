<?php

namespace App\Actions;

use App\Models\QcSession;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;

class ExportOrdersFromQcSession
{
    use AsAction;

    public function handle(QcSession $qcSession, bool $returnData = false): string|array
    {
        // Get all line items from QC order checks in this session with relationships
        /*         $qcSession->load([
                    'orderChecks.lineItemChecks.lineItem.order.shippingAddress',
                ]); */

        $qcSession->load([
            'orderChecks' => function ($query) {
                $query->orderBy('id');
            },
            'orderChecks.lineItemChecks' => function ($query) {
                $query->orderBy('id');
            },
            'orderChecks.lineItemChecks.lineItem.order.shippingAddress',
        ]);

        $lineItems = collect();

        foreach ($qcSession->orderChecks as $orderCheck) {
            foreach ($orderCheck->lineItemChecks as $lineItemCheck) {
                // Only include line items that have "Ready" QC status
                if ($lineItemCheck->isReady()) {
                    $lineItems->push($lineItemCheck->lineItem);
                }
            }
        }

        // Generate CSV data
        $csvData = $this->generateCsvData($lineItems);

        // If returnData is true, return the CSV data for testing
        if ($returnData) {
            return $csvData;
        }

        // Create filename with timestamp
        $filename = 'qc_export_'.$qcSession->id.'_'.now()->format('Y_m_d_H_i_s').'.csv';
        $filePath = 'exports/'.$filename;

        // Generate CSV content
        $csvContent = $this->generateCsvContent($csvData);

        // Save file to storage
        Storage::disk('public')->put($filePath, $csvContent);

        // Return the file path
        return $filePath;
    }

    protected function generateCsvData($lineItems): array
    {
        $csvData = [];

        // Add header row
        $csvData[] = [
            'Order TDA Number',
            'Order Number',
            'Line item code',
            'Shipping Name',
            'Shipping Phone',
            'Shipping Email',
            'Shipping Address 1',
            'Shipping Address 2',
            'Shipping City',
            'Shipping State',
            'Shipping Zip',
            'Shipping Country',
            'Notes (Platform)',
            'Tax/VAT Label',
            'Tax/VAT Message',
            'Gift Message',
            'Size',
            'Type of Product',
            'Quantity',
            'Weight (g)',
            'Height (cm)',
            'Length (cm)',
            'Width (cm)',
            'Item Name',
            'Item SKU',
            'Item HsCode',
            'Item Quantity',
            'Item Price',
            'Service (US Standard,US Premium,Outside US)',
            'Buy with Insurance',
            'IOSS number',
            'EORI number',
            'VAT number',
            'Chinese name',
            'Unit Weight',
        ];

        // Add data rows
        foreach ($lineItems as $lineItem) {
            $order = $lineItem->order;
            $shippingAddress = $order->shippingAddress;

            $csvData[] = [
                $order->number_portal ?? '',                    // Order TDA Number
                $order->order_number ?? '',                     // Order Number
                $lineItem->number_portal ?? '',                 // Line item code
                $shippingAddress->name ?? '',                   // Shipping Name
                $shippingAddress->phone ?? '',                  // Shipping Phone
                $shippingAddress->email ?? '',                  // Shipping Email
                $shippingAddress->line_one ?? '',               // Shipping Address 1
                $shippingAddress->line_two ?? '',               // Shipping Address 2
                $shippingAddress->city ?? '',                   // Shipping City
                $shippingAddress->state ?? '',                  // Shipping State
                $shippingAddress->zip ?? '',                    // Shipping Zip
                $shippingAddress->country ?? '',                // Shipping Country
                $lineItem->compositeNotes(),                    // Notes (Platform)
                $order->getTaxLabel() ?? '',                    // Tax/VAT Label
                $order->getTaxMessage() ?? '',                  // Tax/VAT Message
                $order->gift_message ?? '',                     // Gift Message
                $lineItem->size ?? '',                          // Size
                $lineItem->product_type ?? '',                  // Type of Product
                $lineItem->quantity ?? '',                      // Quantity
                '',                                             // Weight (g) - blank for manual filling
                '',                                             // Height (cm) - blank for manual filling
                '',                                             // Length (cm) - blank for manual filling
                '',                                             // Width (cm) - blank for manual filling
                '',                                             // Item Name - blank for manual filling
                '',                                             // Item SKU - blank for manual filling
                '',                                             // Item HsCode - blank for manual filling
                '',                                             // Item Quantity - blank for manual filling
                '',                                             // Item Price - blank for manual filling
                '',                                             // Service - blank for manual filling
                '',                                             // Buy with Insurance - blank for manual filling
                '',                                             // IOSS number - blank for manual filling
                '',                                             // EORI number - blank for manual filling
                '',                                             // VAT number - blank for manual filling
                '',                                             // Chinese name - blank for manual filling
                '',                                              // Unit Weight - blank for manual filling
            ];
        }

        return $csvData;
    }

    protected function generateCsvContent(array $csvData): string
    {
        $handle = fopen('php://temp', 'r+');

        // Add BOM for UTF-8
        fwrite($handle, "\xEF\xBB\xBF");

        // Write all CSV rows
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $content = stream_get_contents($handle);
        fclose($handle);

        return $content;
    }
}
