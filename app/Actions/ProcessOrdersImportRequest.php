<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\Platform;
use App\Models\Customer;
use App\Models\OrdersImportRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessOrdersImportRequest
{
    use AsAction;

    public function handle(OrdersImportRequest $importRequest): void
    {
        if (! $importRequest->isFromEtsy()) {
            throw new \InvalidArgumentException('Only Etsy import requests can be processed.');
        }

        $data = $importRequest->raw_data;
        $seller = $importRequest->user;

        $orders = [];

        try {
            DB::beginTransaction();

            foreach ($data['orders_search']['orders'] as $rawOrder) {
                // skip if the order is already imported
                if (
                    $seller
                        ->importedOrders()
                        ->where('id_platform', $rawOrder['order_id'])
                        ->where('shop_id_platform', $rawOrder['business_id'])
                        ->exists()
                ) {
                    continue;
                }

                // Handle customer information first (since orders belong to customers)
                $rawBuyer = Arr::first($data['orders_search']['buyers'], function ($i) use ($rawOrder) {
                    return $i['buyer_id'] === $rawOrder['buyer_id'];
                });

                $customer = Customer::updateOrCreate([
                    'platform' => Platform::Etsy,
                    'id_platform' => $rawBuyer['buyer_id'],
                ], [
                    'name' => $rawBuyer['name'] ?? $rawBuyer['username'],
                    'email' => $rawBuyer['email'],
                    'username' => $rawBuyer['username'] ?? null,
                    'avatar_url' => $rawBuyer['avatar_url'],
                ]);

                $costBreakdown = $rawOrder['payment']['cost_breakdown'];

                $order = $seller->importedOrders()->create([
                    'orders_import_request_id' => $importRequest->id,
                    'customer_id' => $customer->id,
                    'platform' => Platform::Etsy->value,
                    'order_number' => $rawOrder['order_id'],
                    'order_date' => Carbon::parse($rawOrder['order_date']),
                    'id_platform' => $rawOrder['order_id'],
                    'shop_id_platform' => $rawOrder['business_id'],
                    'status' => $rawOrder['is_canceled'] ? OrderStatus::Cancelled : OrderStatus::Draft,
                    'is_gift' => $rawOrder['is_gift'],
                    'is_gift_wrapped' => $rawOrder['is_gift_wrapped'],
                    'gift_message' => $rawOrder['gift_message'] ?? null,
                    'currency_code' => $costBreakdown['total_cost']['currency_code'],
                    'total' => $costBreakdown['total_cost']['value'],
                    'subtotal' => $costBreakdown['items_cost']['value'],
                    'shipping_total' => $costBreakdown['shipping_cost']['value'],
                    'tax_total' => $costBreakdown['tax_cost']['value'],
                    'discount_total' => $costBreakdown['discount']['value'],
                    'cost_breakdown' => $costBreakdown,

                    'date_paid' => Carbon::parse($rawOrder['payment']['payment_date']),
                    'payment_method' => $rawOrder['payment']['payment_method'],
                    'expected_ship_date' => Carbon::parse($rawOrder['fulfillment']['expected_ship_date']),

                    'customer_note' => $rawOrder['notes']['note_from_buyer'],
                    'has_gift_teaser' => $rawOrder['has_gift_teaser'],
                ]);

                // handle line items
                foreach ($rawOrder['transactions'] as $rawTransaction) {
                    $rawProduct = $rawTransaction['product'];
                    $order->lineItems()->updateOrCreate([
                        'id_platform' => $rawTransaction['transaction_id'],
                        'sku' => $rawProduct['product_identifier'],
                    ], [
                        'etsy_listing_id' => $rawTransaction['listing_id'],
                        'product_name' => $rawProduct['title'],
                        'product_image_url' => str_replace('75x75', '1000x1000', $rawProduct['image_url_75x75']),
                        'sku' => $rawProduct['product_identifier'],
                        'quantity' => $rawTransaction['quantity'],
                        'price' => $rawTransaction['cost']['value'],
                        'currency_code' => $rawTransaction['cost']['currency_code'],
                        'variations' => array_map(function ($v) {
                            return [
                                'label' => $v['property'],
                                'value' => $v['value'],
                                'order' => $v['order'],
                            ];
                        }, $rawTransaction['variations']),
                        'subtotal' => $rawTransaction['quantity'] * $rawTransaction['cost']['value'],
                        'is_download' => $rawTransaction['is_download'],
                        'is_personalizable' => $rawTransaction['is_personalizable'],
                        'product_type' => $this->parseProductType($rawProduct['product_identifier']),
                        'status' => LineItemStatus::New,
                    ]);
                }

                // Handle shipping information
                $rawShipping = $rawOrder['fulfillment']['to_address'];
                $order->shippingAddress()->updateOrCreate([
                    'name' => $rawShipping['name'],
                ], [
                    'line_one' => $rawShipping['first_line'],
                    'line_two' => $rawShipping['second_line'],
                    'city' => $rawShipping['city'],
                    'state' => $rawShipping['state'],
                    'zip' => $rawShipping['zip'],
                    'country' => $rawShipping['country'],
                    'phone' => $rawShipping['phone'] ?? null,
                    'is_usps_verified' => $rawShipping['is_usps_verified'],
                ]);

                // Handle billing information
                // Order from Etsy does not have a separate billing address, so we use the buyer's information
                $order->billingAddress()->updateOrCreate([
                    'id_platform' => $rawBuyer['buyer_id'],
                ], [
                    'name' => $rawBuyer['name'],
                    'email' => $rawBuyer['email'],
                ]);

                // Handle coupon lines
                foreach ($rawOrder['payment']['sellermarketing_coupons'] as $rawCoupon) {
                    $order->couponLines()->updateOrCreate([
                        'code' => $rawCoupon['code'],
                        'type' => $rawCoupon['type'],
                    ], [
                        'percentage' => $rawCoupon['percentage'] ?? 0,
                        'end_date' => $rawCoupon['end_date'],
                    ]);
                }

                $orders[] = $order;
            }

            $importRequest->markAsProcessed();

            DB::commit();

            foreach ($orders as $order) {
                GenerateInternalOrderNumber::make()->handle($order);
            }
        } catch (\Throwable $e) {
            DB::rollBack();

            throw $e; // Re-throw the exception to handle it further up the stack
        }
    }

    /**
     * Parse product type from SKU based on configured product codes
     */
    protected function parseProductType(?string $sku): ?string
    {
        if (empty($sku)) {
            return null;
        }

        $productCodes = config('genesis.product_codes', []);

        foreach ($productCodes as $code => $productName) {
            if (str_starts_with(strtoupper($sku), strtoupper($code))) {
                return $productName;
            }
        }

        return null;
    }
}
