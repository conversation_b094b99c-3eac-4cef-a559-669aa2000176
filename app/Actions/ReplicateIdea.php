<?php

namespace App\Actions;

use App\Enums\DesignStatus;
use App\Models\Design;
use App\Models\Idea;
use Lorisleiva\Actions\Concerns\AsAction;

class ReplicateIdea
{
    use AsAction;

    public function handle(Idea $idea): Idea
    {
        $idea->load('designs.productType', 'tags');

        $replica = $idea->replicate(['uid']);
        $replica->name = $idea->name.' (Copy)';
        $replica->save();

        // Replicate tags
        if ($idea->tags->isNotEmpty()) {
            $replica->tags()->attach($idea->tags->pluck('id'));
        }

        // Attach existing product types to the new idea with reset designs
        foreach ($idea->designs as $design) {
            // Create new design record with status 'todo' and no assigned designer
            Design::create([
                'idea_id' => $replica->id,
                'product_type_id' => $design->product_type_id, // Use existing product type
                'status' => DesignStatus::Todo,
                'designer_id' => null,
                'design_by_company' => $design->design_by_company,
                'source_file_urls' => null,
                'score' => $design->score,
            ]);
        }

        // Refresh the replica to load the new relationships
        $replica->refresh();
        $replica->load('designs.productType');

        return $replica;
    }
}
