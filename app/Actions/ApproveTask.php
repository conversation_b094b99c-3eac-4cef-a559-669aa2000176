<?php

namespace App\Actions;

use App\Enums\TaskStatus;
use App\Models\Task;
use Lorisleiva\Actions\Concerns\AsAction;

class ApproveTask
{
    use AsAction;

    public function handle(Task $task, ?int $score = null, ?string $feedback = null)
    {
        $task->update([
            'status'      => TaskStatus::Approved,
            'approved_at' => now(),
            'score'       => $score,
            'feedback'    => $feedback,
        ]);
    }
}
