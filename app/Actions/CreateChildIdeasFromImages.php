<?php

namespace App\Actions;

use App\Enums\DesignStatus;
use App\Enums\IdeaType;
use App\Models\Design;
use App\Models\Idea;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateChildIdeasFromImages
{
    use AsAction;

    public function handle(Idea $idea, array $images): void
    {
        $idea->load('designs.productType', 'tags');
        $tagsIds = $idea->tags->pluck('id')->toArray();

        $counter = 1;

        foreach ($images as $imagePath) {
            /** @var Idea $subIdea */
            $subIdea = Idea::create([
                'name'        => $idea->name . ' - Variant ' . $counter,
                'description' => $idea->description,
                'parent_id'   => $idea->id,
                'user_id'     => $idea->user_id,
                'type'        => IdeaType::Simple,
            ]);

            // Add the uploaded image as featured image
            $subIdea->addMediaFromDisk($imagePath, 'public')
                ->toMediaCollection('featured_image');

            // Clone tags from parent idea
            if ($idea->tags->isNotEmpty()) {
                $subIdea->tags()->attach($tagsIds);
            }

            // Clone designs from parent idea
            foreach ($idea->designs as $originalDesign) {
                Design::create([
                    'status'          => DesignStatus::Todo,
                    'product_type_id' => $originalDesign->product_type_id,
                    'idea_id'         => $subIdea->id,
                ]);
            }

            $counter++;
        }
    }
}
