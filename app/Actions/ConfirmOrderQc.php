<?php

namespace App\Actions;

use App\Enums\QcOrderCheckStatus;
use App\Models\QcOrderCheck;
use Lorisleiva\Actions\Concerns\AsAction;

class ConfirmOrderQc
{
    use AsAction;

    public function handle(QcOrderCheck $orderCheck, array $linesState): QcOrderCheck
    {
        foreach ($linesState as $lineState) {
            $orderCheck->lineItemChecks()->updateOrCreate([
                'line_item_id' => $lineState['line_item_id'],
            ], [
                'status' => $lineState['status'],
                'checked_at' => now(),
            ]);
        }

        $orderCheck->update([
            'status' => QcOrderCheckStatus::Completed->value,
            'confirmed_at' => now(),
        ]);

        return $orderCheck;
    }
}
