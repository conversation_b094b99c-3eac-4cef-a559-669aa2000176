<?php

namespace App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;

class SplitTrackingNumbers
{
    use AsAction;

    /**
     * Split tracking numbers string into array of individual tracking numbers
     *
     * Handles multiple formats:
     * - Newline separated: "ABC123\nDEF456"
     * - Comma separated: "ABC123,DEF456"
     * - Mixed: "ABC123\nDEF456,GHI789"
     * - With empty lines and extra whitespace
     *
     * @param  string  $trackingNumbers  Raw tracking numbers input
     * @return array Clean array of unique tracking numbers
     */
    public function handle(string $trackingNumbers): array
    {
        // Split by newlines and commas
        $numbers = preg_split('/[\r\n,]+/', $trackingNumbers);

        // Trim whitespace and filter out empty values
        $numbers = array_filter(array_map('trim', $numbers), function ($code) {
            return ! empty($code);
        });

        // Return unique values with reset array keys
        return array_values(array_unique($numbers));
    }
}
