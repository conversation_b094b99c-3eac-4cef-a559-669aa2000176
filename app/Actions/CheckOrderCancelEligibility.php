<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use Lorisleiva\Actions\Concerns\AsAction;

class CheckOrderCancelEligibility
{
    use AsAction;

    public function handle(Order $order): array
    {
        // Check if order is already cancelled or shipped
        if ($order->status === OrderStatus::Cancelled) {
            return [
                'eligible' => false,
                'reason' => 'Order is already cancelled.',
            ];
        }

        if ($order->status === OrderStatus::Shipped) {
            return [
                'eligible' => false,
                'reason' => 'Order has already been shipped and cannot be cancelled.',
            ];
        }

        // Check if order already has a pending cancel request
        if ($order->hasPendingCancelRequest()) {
            return [
                'eligible' => false,
                'reason' => 'Order already has a pending cancel request.',
            ];
        }

        // New orders can always be cancelled
        if ($order->status === OrderStatus::New) {
            return [
                'eligible' => true,
                'reason' => 'Order can be cancelled.',
            ];
        }

        // Draft orders can always be cancelled
        if ($order->status === OrderStatus::Draft) {
            return [
                'eligible' => true,
                'reason' => 'Draft order can be cancelled.',
            ];
        }

        // Processing orders - check based on line item status and shipments
        if ($order->status === OrderStatus::Processing) {
            // Check if any line items are already shipped
            $shippedLineItems = $order->lineItems()->where('status', LineItemStatus::Shipped)->count();
            if ($shippedLineItems > 0) {
                return [
                    'eligible' => false,
                    'reason' => 'Some line items have already been shipped.',
                ];
            }

            // Check if any line items have tracking numbers (handed over to carrier)
            $lineItemsWithTracking = $order->lineItems()
                ->whereHas('shipment', function ($query) {
                    $query->whereNotNull('tracking_number');
                })
                ->count();

            if ($lineItemsWithTracking > 0) {
                return [
                    'eligible' => true,
                    'reason' => 'Order can be cancelled but may incur costs due to tracking already purchased.',
                    'warning' => true,
                ];
            }

            return [
                'eligible' => true,
                'reason' => 'Processing order can be cancelled.',
            ];
        }

        // Partially shipped orders - can request cancellation for remaining items
        if ($order->status === OrderStatus::PartialShipped) {
            return [
                'eligible' => true,
                'reason' => 'Remaining unshipped items can be cancelled.',
                'warning' => true,
            ];
        }

        return [
            'eligible' => false,
            'reason' => 'Order cannot be cancelled in its current state.',
        ];
    }
}
