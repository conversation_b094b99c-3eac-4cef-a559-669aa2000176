<?php

namespace App\Actions;

use App\Models\Order;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class GenerateInternalOrderNumber
{
    use AsAction;

    public const MAGIC_NUMBER = 100322; // This is a magic number used to generate the order number

    public function handle(Order $order)
    {
        $orderNumber = $this->generateOrderNumber($order);

        $order->update([
            'number_portal' => $orderNumber,
        ]);

        $order->lineItems->each(function ($lineItem, $index) use ($orderNumber) {
            $lineItem->update([
                'number_portal' => sprintf('%s-%02d', $orderNumber, $index + 1),
            ]);
        });
    }

    private function generateOrderNumber(Order $order): string
    {
        $platformCode = $order->platform->code();
        $sequentialNumber = static::MAGIC_NUMBER + $order->id;

        return sprintf('%s%d', $platformCode, $sequentialNumber);
    }
}
