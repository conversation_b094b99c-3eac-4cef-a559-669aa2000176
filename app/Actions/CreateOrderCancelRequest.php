<?php

namespace App\Actions;

use App\Enums\OrderCancelRequestStatus;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateOrderCancelRequest
{
    use AsAction;

    public function handle(Order $order, User $seller, string $reason): OrderCancelRequest
    {
        // Validate that the user is a seller
        if (! $seller->isSeller()) {
            throw ValidationException::withMessages([
                'user' => 'Only sellers can create cancel requests.',
            ]);
        }

        // Validate that the seller owns this order
        if (! in_array($seller->id, [$order->seller_id, $order->backup_seller_id])) {
            throw ValidationException::withMessages([
                'order' => 'You can only request cancellation for your own orders.',
            ]);
        }

        // Check if order is eligible for cancellation
        $eligibility = CheckOrderCancelEligibility::make()->handle($order);
        if (! $eligibility['eligible']) {
            throw ValidationException::withMessages([
                'order' => $eligibility['reason'],
            ]);
        }

        // Validate reason
        if (empty(trim($reason))) {
            throw ValidationException::withMessages([
                'reason' => 'Cancellation reason is required.',
            ]);
        }

        if (strlen(trim($reason)) < 10) {
            throw ValidationException::withMessages([
                'reason' => 'Cancellation reason must be at least 10 characters long.',
            ]);
        }

        // Create the cancel request
        $cancelRequest = OrderCancelRequest::create([
            'order_id' => $order->id,
            'seller_id' => $seller->id,
            'reason' => trim($reason),
            'status' => OrderCancelRequestStatus::Pending,
        ]);

        if ($order->isDraft() && $seller->isInchargeOfOrder($order)) {
            ProcessOrderCancelRequest::make()->handle($cancelRequest, $seller, true);
        }

        return $cancelRequest;
    }
}
