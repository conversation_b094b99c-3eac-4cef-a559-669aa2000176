<?php

namespace App\Actions;

use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Lorisleiva\Actions\Concerns\AsAction;

class CancelDraftOrder
{
    use AsAction;

    public function handle(User $user, Order $order): void
    {
        if ($user->cannot('cancelDraftOrder', $order)) {
            throw new AuthorizationException('You are not authorized to cancel this order.');
        }

        CancelOrder::make()->handle($order);
    }
}
