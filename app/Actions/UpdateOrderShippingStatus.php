<?php

namespace App\Actions;

use App\Enums\OrderStatus;
use App\Models\Order;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateOrderShippingStatus
{
    use AsAction;

    /**
     * Update the order status based on the shipping status of its line items
     */
    public function handle(Order $order): void
    {
        if ($order->isFullyShipped()) {
            $order->update(['status' => OrderStatus::Shipped]);
        } elseif ($order->isPartiallyShipped()) {
            $order->update(['status' => OrderStatus::PartialShipped]);
        }
    }
}
