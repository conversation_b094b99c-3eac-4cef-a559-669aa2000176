<?php

namespace App\Actions;

use App\Models\ProductType;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class ReplicateProductType
{
    use AsAction;

    public function handle(ProductType $productType): ProductType
    {
        $productType->load('options.values');

        $replica = $productType->replicate();
        $replica->name = $productType->name.' (Copy)';
        $replica->save();

        // Replicate options and values
        foreach ($productType->options as $option) {
            $replicaOption = $option->replicate();
            $replicaOption->product_type_id = $replica->id;
            $replicaOption->save();

            foreach ($option->values as $value) {
                $replicaValue = $value->replicate();
                $replicaValue->product_type_option_id = $replicaOption->id;
                $replicaValue->save();
            }
        }

        // Refresh the replica to load the new relationships
        $replica->refresh();
        $replica->load('options.values');

        return $replica;
    }
}
