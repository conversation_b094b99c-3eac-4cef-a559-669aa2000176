<?php

namespace App\Actions;

use App\Models\ProductTypeOption;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateProductTypeOption
{
    use AsAction;

    public function handle(ProductTypeOption $option, string $optionName, array $values = [], bool $isRequired = false): ProductTypeOption
    {
        $option->update([
            'name' => $optionName,
            'is_required' => $isRequired,
        ]);

        $existingValues = $option->values()->get();
        $existingValuesByValue = $existingValues->keyBy('value');

        $valuesToKeep = [];

        foreach ($values as $index => $value) {
            if (empty(trim($value))) {
                continue;
            }

            $value = (string) $value;

            if ($existingValuesByValue->has($value)) {
                $existingValue = $existingValuesByValue->get($value);
                if ($existingValue->sort_order !== $index) {
                    $existingValue->update(['sort_order' => $index]);
                }
                $valuesToKeep[] = $existingValue->id;
            } else {
                $existingValue = $option->values()->where('value', $value)->first();

                if ($existingValue) {
                    if ($existingValue->sort_order !== $index) {
                        $existingValue->update(['sort_order' => $index]);
                    }
                    $valuesToKeep[] = $existingValue->id;
                } else {
                    $newValue = $option->values()->create([
                        'value' => $value,
                        'sort_order' => $index,
                    ]);
                    $valuesToKeep[] = $newValue->id;
                }
            }
        }

        $option->values()->whereNotIn('id', $valuesToKeep)->delete();

        return $option->fresh(['values']);
    }
}
