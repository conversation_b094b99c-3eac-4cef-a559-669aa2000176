<?php

namespace App\Actions;

use App\Models\QcOrderCheck;
use Lorisleiva\Actions\Concerns\AsAction;

class CheckOrderCompletion
{
    use AsAction;

    public function handle(QcOrderCheck $orderCheck): bool
    {
        // Check if all line items have been checked
        if (! $orderCheck->hasAllLineItemsChecked()) {
            return false;
        }

        // If all line items are checked, we can potentially auto-confirm
        // For now, we'll just return true to indicate the order is ready for confirmation
        // In the future, we could add auto-confirmation logic here

        return true;
    }
}
