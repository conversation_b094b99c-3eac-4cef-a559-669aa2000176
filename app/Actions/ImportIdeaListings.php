<?php

namespace App\Actions;

use App\Models\Design;
use App\Models\Idea;
use App\Models\ListingUrl;
use App\Models\ProductType;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ImportIdeaListings
{
    use AsAction;

    public function validate(string $filePath): array
    {
        $rows = ParseCsvFile::make()->handle($filePath);

        // Check required columns
        if (empty($rows)) {
            throw new \Exception('CSV file is empty or invalid.');
        }

        $firstRecord = $rows[0];

        if (! isset($firstRecord['link'])) {
            throw new \Exception('CSV file must contain a "link" column.');
        }

        if (! isset($firstRecord['uid'])) {
            throw new \Exception('CSV file must contain a "UID" column.');
        }

        if (! isset($firstRecord['product_types'])) {
            throw new \Exception('CSV file must contain a "Product types" column.');
        }

        $links = [];

        // Get all product types in one query for efficiency and normalize for case-insensitive comparison
        $allProductTypes = ProductType::pluck('name', 'id')->toArray();
        $normalizedProductTypes = [];
        foreach ($allProductTypes as $id => $name) {
            $normalizedProductTypes[strtolower(trim($name))] = ['id' => $id, 'name' => $name];
        }

        foreach ($rows as $index => $row) {
            $this->validateRow($row, $index + 2, $links, $normalizedProductTypes);
        }

        return $rows;
    }

    protected function validateRow(array $row, int $rowNumber, array &$links, array $normalizedProductTypes): void
    {
        $link = trim($row['link'] ?? '');
        $uid = trim($row['uid'] ?? '');
        $productTypesString = trim($row['product_types'] ?? '');

        // Validate link
        if (empty($link)) {
            throw new \Exception("Row {$rowNumber}: Link is required");
        }

        if (! filter_var($link, FILTER_VALIDATE_URL)) {
            throw new \Exception("Row {$rowNumber}: Invalid URL format '{$link}'");
        }

        // Check for duplicate links
        if (in_array($link, $links)) {
            throw new \Exception("Row {$rowNumber}: Duplicate link '{$link}'");
        }
        $links[] = $link;

        // Validate UID
        if (empty($uid)) {
            throw new \Exception("Row {$rowNumber}: UID is required");
        }

        $idea = Idea::where('uid', $uid)->first();
        if (! $idea) {
            throw new \Exception("Row {$rowNumber}: Idea with UID '{$uid}' not found");
        }

        // Validate product types
        if (! empty($productTypesString)) {
            $productTypeNames = array_filter(
                array_map('trim', explode(',', $productTypesString))
            );

            foreach ($productTypeNames as $productTypeName) {
                // Normalize product type name for case-insensitive comparison
                $normalizedProductTypeName = strtolower(trim($productTypeName));

                // Check if product type exists in our normalized list
                if (! isset($normalizedProductTypes[$normalizedProductTypeName])) {
                    throw new \Exception("Row {$rowNumber}: Product type '{$productTypeName}' not found");
                }

                $productTypeId = $normalizedProductTypes[$normalizedProductTypeName]['id'];

                // Check if the idea has this product type
                $ideaHasProductType = $idea->productTypes()
                    ->where('product_types.id', $productTypeId)
                    ->exists();

                if (! $ideaHasProductType) {
                    throw new \Exception("Row {$rowNumber}: Idea '{$uid}' does not have product type '{$productTypeName}'");
                }
            }
        }
    }

    public function handle(string $filePath): void
    {
        $csvData = ParseCsvFile::make()->handle($filePath);

        try {
            DB::beginTransaction();

            foreach ($csvData as $index => $row) {
                $link = strtolower(trim($row['link'] ?? ''));
                $uid = trim($row['uid'] ?? '');
                $productTypesString = trim($row['product_types'] ?? '');

                // Find the idea by UID
                $idea = Idea::where('uid', $uid)->first();

                // Parse product types
                $productTypeNames = array_filter(
                    array_map('trim', explode(',', $productTypesString))
                );

                if (empty($productTypeNames)) {
                    // Create listing URL without design if no product types specified
                    $this->createOrUpdateListingUrl($link, $idea, null);
                } else {
                    // Create listing URLs for each product type
                    foreach ($productTypeNames as $productTypeName) {
                        $productType = ProductType::query()->where('name', $productTypeName)->first();

                        // Find the design for this idea and product type
                        $design = Design::where('idea_id', $idea->id)
                            ->where('product_type_id', $productType->id)
                            ->first();

                        $this->createOrUpdateListingUrl($link, $idea, $design);
                    }
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function createOrUpdateListingUrl(string $url, Idea $idea, ?Design $design): ListingUrl
    {
        // Check if listing URL already exists
        $existingListingUrl = ListingUrl::where('link', $url)
            ->where('idea_id', $idea->id)
            ->when($design, fn ($query) => $query->where('design_id', $design->id))
            ->first();

        if ($existingListingUrl) {
            return $existingListingUrl;
        }

        return ListingUrl::create([
            'link' => $url,
            'idea_id' => $idea->id,
            'design_id' => $design?->id,
        ]);
    }
}
