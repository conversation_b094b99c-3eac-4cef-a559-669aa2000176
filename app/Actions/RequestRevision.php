<?php

namespace App\Actions;

use App\Enums\DesignStatus;
use App\Models\Design;
use Lorisleiva\Actions\Concerns\AsAction;

class RequestRevision
{
    use AsAction;

    public function handle(Design $design, string $comment, int $userId): void
    {
        $design->update([
            'status' => DesignStatus::NeedRevision,
            'revision_requested_at' => now(),
        ]);

        $formattedComment = '[Request Revision] '.$comment;
        AddComment::run($design, $formattedComment, $userId);
    }
}
