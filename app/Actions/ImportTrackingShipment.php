<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Enums\ShipmentStatus;
use App\Models\Carrier;
use App\Models\LineItem;
use App\Models\Shipment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ImportTrackingShipment
{
    use AsAction;

    protected function getCarriers(): Collection
    {
        return Carrier::all()->keyBy(fn ($c) => strtolower(trim($c->name)));
    }

    public function validate(string $file)
    {
        $rows = ParseCsvFile::make()->handle($file);

        foreach ($rows as $index => $row) {
            $this->validateRow($row, $index + 1);
        }

        return $rows;
    }

    protected function validateRow(array $row, int $index): void
    {
        // Validate Order TDA Number
        if (blank($row['order_tda_number'])) {
            throw new \InvalidArgumentException("Row {$index} is missing 'Order TDA Number'.");
        }

        // Validate Line Item Code
        if (blank($row['line_item_code']) || LineItem::where('number_portal', $row['line_item_code'])->doesntExist()) {
            throw new \InvalidArgumentException("Row {$index} is missing or invalid 'Line Item Code'.");
        }

        // Validate that line item belongs to the order
        $lineItem = LineItem::query()
            ->where('number_portal', $row['line_item_code'])
            ->whereHas(
                'order',
                fn ($query) => $query
                    ->whereIn('status', [OrderStatus::Processing, OrderStatus::PartialShipped, OrderStatus::Shipped])
                    ->where('number_portal', $row['order_tda_number'])
            )
            ->first();
        if (! $lineItem) {
            throw new \InvalidArgumentException("Row {$index}: Line item {$row['line_item_code']} does not belong to the specified order.");
        }

        if (! $lineItem->isShipped() && ! $lineItem->isPacked()) {
            if ($row['packer'] === 'TDA' && ! $lineItem->isPacking()) {
                throw new \InvalidArgumentException("Row {$index}: Line item {$row['line_item_code']} with packer TDA is not in Packing status.");
            }

            if ($row['packer'] === 'SUP' && ! $lineItem->isInProduction()) {
                throw new \InvalidArgumentException("Row {$index}: Line item {$row['line_item_code']} with packer SUP is not in In Production status.");
            }
        }

        // Validate Tracking Code
        if (blank($row['tracking_code'])) {
            throw new \InvalidArgumentException("Row {$index} is missing 'Tracking Code'.");
        }

        // Validate Carrier
        if (blank($row['carrier'])) {
            throw new \InvalidArgumentException("Row {$index} is missing 'Carrier'.");
        }

        $carriers = $this->getCarriers();
        $carrierName = strtolower(trim($row['carrier']));
        if (! $carriers->has($carrierName)) {
            throw new \InvalidArgumentException("Row {$index} is missing or invalid 'Carrier'.");
        }

        // Validate Packer
        if (blank($row['packer'])) {
            throw new \InvalidArgumentException("Row {$index} is missing 'Packer'.");
        }

        if (! in_array(strtoupper($row['packer']), ['TDA', 'SUP'])) {
            throw new \InvalidArgumentException("Row {$index} has invalid 'Packer'. Must be 'TDA' or 'SUP'.");
        }

        // Validate numeric fields if provided
        if (! empty($row['weight_(g)']) && (! is_numeric($row['weight_(g)']) || (float) $row['weight_(g)'] < 0)) {
            throw new \InvalidArgumentException("Row {$index} has invalid 'Weight (g)'.");
        }

        if (! empty($row['height_(cm)']) && (! is_numeric($row['height_(cm)']) || (float) $row['height_(cm)'] < 0)) {
            throw new \InvalidArgumentException("Row {$index} has invalid 'Height (cm)'.");
        }

        if (! empty($row['length_(cm)']) && (! is_numeric($row['length_(cm)']) || (float) $row['length_(cm)'] < 0)) {
            throw new \InvalidArgumentException("Row {$index} has invalid 'Length (cm)'.");
        }

        if (! empty($row['width_(cm)']) && (! is_numeric($row['width_(cm)']) || (float) $row['width_(cm)'] < 0)) {
            throw new \InvalidArgumentException("Row {$index} has invalid 'Width (cm)'.");
        }

        if (! empty($row['shipping_fee'])) {
            $shippingFee = str_replace(',', '.', $row['shipping_fee']);
            if (! is_numeric($shippingFee) || (float) $shippingFee < 0) {
                throw new \InvalidArgumentException("Row {$index} has invalid 'Shipping fee'.");
            }
        }

        // Validate production cost
        if (! empty($row['production_cost'])) {
            $productionCost = str_replace(',', '.', $row['production_cost']);
            if (! is_numeric($productionCost) || (float) $productionCost < 0) {
                throw new \InvalidArgumentException("Row {$index} has invalid 'Production cost'.");
            }
        }

        // Validate packaging fee
        if (! empty($row['packaging_fee'])) {
            $packagingFee = str_replace(',', '.', $row['packaging_fee']);
            if (! is_numeric($packagingFee) || (float) $packagingFee < 0) {
                throw new \InvalidArgumentException("Row {$index} has invalid 'Packaging fee'.");
            }
        }
    }

    public function handle(string $filePath): void
    {
        $data = ParseCsvFile::make()->handle($filePath);
        $carriers = $this->getCarriers();

        try {
            DB::beginTransaction();

            // Group data by tracking code and carrier (multiple orders can share same shipment)
            $groupedData = collect($data)->groupBy(function ($row) {
                return $row['tracking_code'].'|'.$row['carrier'];
            });

            foreach ($groupedData as $group) {
                $firstRow = $group->first();

                // Normalize carrier from file
                $carrierName = strtolower(trim($firstRow['carrier']));
                $carrier = $carriers->get($carrierName);

                $packer = strtoupper($firstRow['packer']);

                // Determine shipment status and shipped_at based on packer
                $shipmentStatus = $packer === 'SUP' ? ShipmentStatus::PickedUp : ShipmentStatus::Pending;
                $shippedAt = $packer === 'SUP' ? now() : null;

                // Find or create shipment (respecting unique constraint on carrier_id + tracking_number)
                $shipment = Shipment::updateOrCreate([
                    'tracking_number' => $firstRow['tracking_code'],
                    'carrier_id' => $carrier->id,
                ], [
                    'status' => $shipmentStatus,
                    'weight' => $this->parseIntegerValue($firstRow['weight_(g)']),
                    'height' => $this->parseIntegerValue($firstRow['height_(cm)']),
                    'length' => $this->parseIntegerValue($firstRow['length_(cm)']),
                    'width' => $this->parseIntegerValue($firstRow['width_(cm)']),
                    'shipping_total' => $this->parseShippingFee($firstRow['shipping_fee']) * 100,
                    'currency_code' => 'USD',
                    'packer' => $packer,
                ]);

                if ($shipment->isPickedUp() && $shipment->shipped_at === null) {
                    $shipment->update([
                        'shipped_at' => $shippedAt,
                    ]);
                }

                // Update line items and collect affected orders
                $affectedOrders = collect();
                $lineItemCodes = $group->pluck('line_item_code');
                $lineItems = LineItem::with('order')->whereIn('number_portal', $lineItemCodes)->get()->keyBy('number_portal');

                foreach ($group as $row) {
                    $lineItem = $lineItems->get($row['line_item_code']);

                    // Determine line item status based on packer
                    $lineItemStatus = $packer === 'SUP'
                        ? ($lineItem->isInProduction() ? LineItemStatus::Shipped : $lineItem->status)
                        : ($lineItem->isPacking() ? LineItemStatus::Packed : $lineItem->status);

                    $updateData = [
                        'shipment_id' => $shipment->id,
                        'status' => $lineItemStatus,
                    ];

                    // Add production cost and packaging fee (set to 0 if empty)
                    if (isset($row['production_cost'])) {
                        $updateData['production_total'] = ! empty($row['production_cost'])
                            ? $this->parseMoneyValue($row['production_cost']) * 100
                            : 0;
                    }

                    if (isset($row['packaging_fee'])) {
                        $updateData['packaging_total'] = ! empty($row['packaging_fee'])
                            ? $this->parseMoneyValue($row['packaging_fee']) * 100
                            : 0;
                    }

                    $lineItem->update($updateData);

                    // Collect unique orders for status update
                    if ($lineItem->order && ! $affectedOrders->contains('id', $lineItem->order->id)) {
                        $affectedOrders->push($lineItem->order);
                    }
                }

                // Update shipping status for all affected orders
                foreach ($affectedOrders as $order) {
                    UpdateOrderShippingStatus::make()->handle($order);
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function parseIntegerValue(?string $value): ?int
    {
        if (blank($value)) {
            return null;
        }

        return (int) str_replace(',', '.', $value);
    }

    private function parseShippingFee(?string $value): float
    {
        if (blank($value)) {
            return 0;
        }

        // Replace Vietnamese comma with dot for decimal separator
        return (float) str_replace(',', '.', $value);
    }

    private function parseMoneyValue(?string $value): float
    {
        if (blank($value)) {
            return 0;
        }

        // Replace Vietnamese comma with dot for decimal separator
        return (float) str_replace(',', '.', $value);
    }
}
