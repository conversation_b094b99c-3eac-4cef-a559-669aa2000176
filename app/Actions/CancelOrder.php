<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use Lorisleiva\Actions\Concerns\AsAction;

class CancelOrder
{
    use AsAction;

    public function handle(Order $order): void
    {
        $order->update(['status' => OrderStatus::Cancelled]);

        $order->lineItems->each(function ($lineItem) {
            $lineItem->update(['status' => LineItemStatus::Cancelled]);
        });
    }
}
