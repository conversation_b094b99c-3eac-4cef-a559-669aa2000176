<?php

namespace App\Actions;

use App\Models\Idea;
use App\Models\ProductType;
use App\Models\ProductTypeOptionValue;
use App\Models\ProductVariant;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateProductVariants
{
    use AsAction;

    public function handle(Idea $idea, ProductType $productType, array $optionsData, array $pricingData = []): Collection
    {
        $variants = collect();

        // Generate all combinations of option values
        $combinations = $this->generateCombinations($optionsData, $productType);

        foreach ($combinations as $optionValueIds) {
            // Check if variant with these option values already exists
            $existingVariant = $this->findExistingVariant($idea, $productType, $optionValueIds);

            if ($existingVariant) {
                continue; // Skip if variant already exists
            }

            // Create new product variant
            $variant = ProductVariant::create([
                'idea_id' => $idea->id,
                'product_type_id' => $productType->id,
            ]);

            // Attach option values to the variant
            $variant->optionValues()->attach($optionValueIds);

            $variants->push($variant);
        }

        return $variants;
    }

    protected function generateCombinations(array $optionsData, ProductType $productType): array
    {
        $combinations = [[]];

        foreach ($optionsData as $option) {
            // Extract option name and values from the option data structure
            $optionName = $option['name'];
            $values = $option['values'] ?? [];

            // Skip if no values provided for this option
            if (empty($values)) {
                continue;
            }

            // Find the ProductTypeOption for this name
            $productTypeOption = $productType->options()->where('name', $optionName)->first();
            if (! $productTypeOption) {
                continue; // Skip if option doesn't exist
            }

            $newCombinations = [];

            foreach ($combinations as $combination) {
                foreach ($values as $value) {
                    // Find the ProductTypeOptionValue for this value
                    $optionValue = $productTypeOption->values()->where('value', $value)->first();
                    if ($optionValue) {
                        $newCombination = $combination;
                        $newCombination[] = $optionValue->id;
                        $newCombinations[] = $newCombination;
                    }
                }
            }

            $combinations = $newCombinations;
        }

        return $combinations;
    }

    protected function findExistingVariant(Idea $idea, ProductType $productType, array $optionValueIds): ?ProductVariant
    {
        // Find variants with the same idea and product type
        $candidates = ProductVariant::where('idea_id', $idea->id)
            ->where('product_type_id', $productType->id)
            ->with('optionValues')
            ->get();

        // Check if any candidate has exactly the same option values
        foreach ($candidates as $candidate) {
            $candidateOptionValueIds = $candidate->optionValues->pluck('id')->sort()->values()->toArray();
            $targetOptionValueIds = collect($optionValueIds)->sort()->values()->toArray();

            if ($candidateOptionValueIds === $targetOptionValueIds) {
                return $candidate;
            }
        }

        return null;
    }
}
