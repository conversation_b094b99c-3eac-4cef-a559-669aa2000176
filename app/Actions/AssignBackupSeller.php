<?php

namespace App\Actions;

use App\Models\Order;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;

class AssignBackupSeller
{
    use AsAction;

    public function handle(Order $order, User $backupSeller): void
    {
        if (! $backupSeller->isSeller()) {
            throw new \InvalidArgumentException('The provided user is not a seller.');
        }

        $order->update([
            'backup_seller_id' => $backupSeller->id,
        ]);
    }
}
