<?php

namespace App\Actions;

use App\Models\QcSession;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;

class ExportPackingSlipFromQcSession
{
    use AsAction;

    public function handle(QcSession $qcSession, bool $returnData = false): string|array
    {
        // Get all line items from QC order checks in this session with relationships
        /*         $qcSession->load([
                    'orderChecks.lineItemChecks.lineItem.order.shippingAddress',
                ]); */
        $qcSession->load([
            'orderChecks' => function ($query) {
                $query->orderBy('id');
            },
            'orderChecks.lineItemChecks' => function ($query) {
                $query->orderBy('id');
            },
            'orderChecks.lineItemChecks.lineItem.order.shippingAddress',
        ]);

        $lineItems = collect();

        foreach ($qcSession->orderChecks as $orderCheck) {
            foreach ($orderCheck->lineItemChecks as $lineItemCheck) {
                // Only include line items that have "Ready" QC status
                if ($lineItemCheck->isReady()) {
                    $lineItems->push($lineItemCheck->lineItem);
                }
            }
        }

        // Generate CSV data
        $csvData = $this->generateCsvData($lineItems);

        // If returnData is true, return the CSV data for testing
        if ($returnData) {
            return $csvData;
        }

        // Create filename with timestamp
        $filename = 'packing_slip_'.$qcSession->id.'_'.now()->format('Y_m_d_H_i_s').'.csv';
        $filePath = 'exports/'.$filename;

        // Generate CSV content
        $csvContent = $this->generateCsvContent($csvData);

        // Save file to storage
        Storage::disk('public')->put($filePath, $csvContent);

        // Return the file path
        return $filePath;
    }

    protected function generateCsvData($lineItems): array
    {
        $csvData = [];

        // Add header row with current date
        $currentDate = now()->format('d/m/Y');
        $csvData[] = [
            "PHIẾU ĐÓNG GÓI NGÀY {$currentDate}",
            '', '', '', '', '', '', '', '', '', '', '', '',
        ];

        // Add column headers
        $csvData[] = [
            'Order TDA Number',
            'Order Number',
            'Line Item Code',
            'Shipping Name',
            'Shipping Country',
            'Quantity',
            'Notes',
            'Seller Note',
            'Type of Product',
            'Product Attributes',
            'Size',
            'Color',
            'Personalization',
            'Image URL',
            'Image',
            'Has Gift Info',
            'Gift Message',
            'Packed',
        ];

        // Add data rows
        foreach ($lineItems as $lineItem) {
            $order = $lineItem->order;
            $shippingAddress = $order->shippingAddress;

            // ---- Gift logic ----
            $hasGiftTeaser = ((int) ($order->has_gift_teaser ?? 0) === 1);
            $isGiftWrap = ((int) ($order->is_gift_wrap ?? 0) === 1);
            $giftMessage = trim((string) ($order->gift_message ?? ''));
            $hasGiftInfo = $hasGiftTeaser || $isGiftWrap || ($giftMessage !== '');

            $csvData[] = [
                $order->number_portal ?? '',                    // Order TDA Number
                $order->order_number ?? '',                     // Order Number
                $lineItem->number_portal ?? '',                 // Line Item Code
                $shippingAddress->name ?? '',                   // Shipping Name
                $shippingAddress->country ?? '',                // Shipping Country
                $lineItem->quantity ?? '',                      // Quantity
                $lineItem->compositeNotes(),                    // Notes
                $lineItem->seller_note ?? '',                   // Seller Note
                $lineItem->product_type ?? '',                  // Type of Product
                $lineItem->attributesString(),                  // Product Attributes
                $lineItem->size ?? '',                          // Size
                $lineItem->color ?? '',                         // Color
                $lineItem->personalization ?? '',               // Personalization
                $lineItem->product_image_url ?? '',             // Image URL
                '',                                             // Image placeholder
                $hasGiftInfo ? 'TRUE' : '',                     // Has Gift Info
                $hasGiftInfo ? $giftMessage : '',               // Gift Message (rỗng nếu không thỏa)
                '',                                              // Packed - empty for manual completion
            ];
        }

        return $csvData;
    }

    protected function generateCsvContent(array $csvData): string
    {
        $handle = fopen('php://temp', 'r+');

        // Add BOM for UTF-8
        fwrite($handle, "\xEF\xBB\xBF");

        // Write all CSV rows
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $content = stream_get_contents($handle);
        fclose($handle);

        return $content;
    }
}
