<?php

namespace App\Actions;

use App\Enums\QcOrderCheckStatus;
use App\Enums\QcSessionStatus;
use App\Models\QcSession;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class FinishQcSession
{
    use AsAction;

    public function handle(QcSession $qcSession): QcSession
    {
        // Validate session is in progress
        if (! $qcSession->isProcessing()) {
            throw ValidationException::withMessages([
                'qc_session' => 'QC session must be in progress to finish.',
            ]);
        }

        // Check if there are any incomplete order checks
        $incompleteOrderChecks = $qcSession->orderChecks()
            ->where('status', QcOrderCheckStatus::Processing->value)
            ->count();

        if ($incompleteOrderChecks > 0) {
            throw ValidationException::withMessages([
                'qc_session' => "There are {$incompleteOrderChecks} incomplete order checks. Please complete all orders before finishing the session.",
            ]);
        }

        // Update session status
        $qcSession->update([
            'status' => QcSessionStatus::Completed->value,
            'completed_at' => now(),
        ]);

        // Update line items to Packing status
        UpdateLineItemsToPacking::make()->handle($qcSession);

        return $qcSession;
    }
}
