<?php

namespace App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;

class GetTaxLabelAndMessage
{
    use AsAction;

    public function handle(array $costBreakdown): array
    {
        $vatConfig = config('etsy.vat_config');

        [$vatOrTaxLabel, $vatOrTaxMessage] = match (true) {
            $costBreakdown['should_show_vat_euro_notice'] ?? false => [
                $vatConfig['vat_collected'],
                str_replace('{{euro_amount}}', $costBreakdown['euro_consignment_cost']['formatted_value'], $vatConfig['vat_euro_ioss_message']),
            ],
            $costBreakdown['should_show_vat_uk_notice'] ?? false => [
                $vatConfig['vat_collected'],
                str_replace('{{gbp_amount}}', $costBreakdown['tax_rule_consignment_cost']['formatted_value'], $vatConfig['vat_uk_message']),
            ],
            $costBreakdown['should_show_gst_sg_notice'] ?? false => [
                $vatConfig['etsy_singapore_gst_collected_badge'],
                $vatConfig['etsy_singapore_gst_number_message'],
            ],
            $costBreakdown['should_show_canada_tax_paid_notice'] ?? false => [
                $vatConfig['province_tax_collected'],
                str_replace('{{amount}}', $costBreakdown['total_canadian_provincial_tax_cost']['formatted_value'], $vatConfig['vat_canada_tax_paid_notice']),
            ],
            $costBreakdown['should_show_canada_zero_tax_rated_notice'] ?? false => [
                $vatConfig['province_tax_zero_rated_item'],
                $vatConfig['vat_canada_zero_tax_rated_notice'],
            ],
            $costBreakdown['should_show_norway_voec_notice'] ?? false => [
                $vatConfig['etsy_norway_voec_collected_badge'],
                $vatConfig['etsy_norway_voec_number_message'],
            ],
            $costBreakdown['should_show_swiss_vat_notice'] ?? false => [
                $vatConfig['etsy_switzerland_vat_number_message'],
                $vatConfig['etsy_switzerland_vat_number_message'],
            ],
            default => ['', ''],
        };

        return [$vatOrTaxLabel, strip_tags($vatOrTaxMessage)];
    }
}
