<?php

namespace App\Actions;

use App\Enums\QcSessionStatus;
use App\Models\QcSession;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class StartQcSession
{
    use AsAction;

    public function handle(User $user): QcSession
    {
        // Check if user already has an active QC session
        if ($user->hasActiveQcSession()) {
            throw ValidationException::withMessages([
                'user' => 'User already has an active QC session. Please finish or cancel it before starting a new one.',
            ]);
        }

        return $user->qcSessions()->create([
            'status' => QcSessionStatus::Processing,
            'started_at' => now(),
        ]);
    }
}
