<?php

namespace App\Actions;

use App\Enums\QcOrderCheckStatus;
use App\Enums\QcSessionStatus;
use App\Models\Order;
use App\Models\QcOrderCheck;
use App\Models\QcSession;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class AddOrderToQc
{
    use AsAction;

    public function handle(QcSession $qcSession, Order $order): QcOrderCheck
    {
        // Validate order can be QC'd
        if (! $order->canBeQualityControlled()) {
            throw ValidationException::withMessages([
                'order' => 'Order cannot be quality controlled. It must be in processing status and not already being QC\'d.',
            ]);
        }

        // Update session status to in progress if it's new
        if ($qcSession->isNew()) {
            $qcSession->update([
                'status' => QcSessionStatus::Processing->value,
                'started_at' => now(),
            ]);
        }

        /** @var QcOrderCheck $orderCheck */
        $orderCheck = $qcSession->orderChecks()->create([
            'order_id' => $order->id,
            'status' => QcOrderCheckStatus::Processing,
        ]);

        return $orderCheck;
    }
}
