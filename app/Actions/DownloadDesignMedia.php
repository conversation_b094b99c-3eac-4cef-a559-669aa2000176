<?php

namespace App\Actions;

use App\Models\Design;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use ZipArchive;

class DownloadDesignMedia
{
    use AsAction;

    public function handle(Design $design): string
    {
        $mediaItems = $design->media()->get();

        if ($mediaItems->isEmpty()) {
            throw new \InvalidArgumentException('No media files found for this design.');
        }

        // Create a unique folder name for this design's media
        $folderName = 'design-media-downloads/' . Str::slug('design-' . $design->id) . '-' . now()->format('Y-m-d-H-i-s');

        // Create the temporary folder
        if (!Storage::disk('public')->makeDirectory($folderName)) {
            throw new \RuntimeException('Failed to create temporary folder for media download.');
        }

        try {
            // Download each media file to the temporary folder
            foreach ($mediaItems as $index => $media) {
                $fileName = $media->file_name;

                // Ensure unique filename if there are duplicates
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                $fileName = sprintf('%s-%s-%s.%s', $media->collection_name, $index, $media->name, $extension);

                try {
                    // Get the media content - works for both local and cloud storage
                    $mediaContent = file_get_contents($media->getUrl());

                    // Save the media file to our temporary folder
                    Storage::disk('public')->put(
                        $folderName . '/' . $fileName,
                        $mediaContent
                    );
                } catch (\Exception $e) {
                    // If we can't get the media content, skip this file
                    continue;
                }
            }

            // Create ZIP file
            $zipFileName = Str::slug('design-' . $design->id) . '-media-' . now()->format('Y-m-d-H-i-s') . '.zip';
            $zipFilePath = storage_path('app/public/design-media-downloads/' . $zipFileName);

            $zip = new ZipArchive();
            if ($zip->open($zipFilePath, ZipArchive::CREATE) !== TRUE) {
                throw new \RuntimeException('Failed to create ZIP file.');
            }

            // Add all files from the temporary folder to the ZIP
            $files = Storage::disk('public')->files($folderName);
            foreach ($files as $file) {
                $fullPath = Storage::disk('public')->path($file);
                $relativePath = basename($file);
                $zip->addFile($fullPath, $relativePath);
            }

            $zip->close();

            Storage::disk('public')->deleteDirectory($folderName);

            return Storage::disk('public')->url('design-media-downloads/' . $zipFileName);

        } catch (\Exception $e) {
            // Clean up temporary folder in case of error
            if (Storage::disk('public')->exists($folderName)) {
                Storage::disk('public')->deleteDirectory($folderName);
            }

            throw $e;
        }
    }

    /**
     * Clean up old media download files (older than 24 hours)
     */
    public function cleanupOldDownloads(): void
    {
        $downloadPath = 'design-media-downloads';

        if (!Storage::disk('public')->exists($downloadPath)) {
            return;
        }

        $files = Storage::disk('public')->files($downloadPath);
        $cutoffTime = now()->subDay();

        foreach ($files as $file) {
            $lastModified = Storage::disk('public')->lastModified($file);

            if ($lastModified < $cutoffTime->timestamp) {
                Storage::disk('public')->delete($file);
            }
        }
    }
}
