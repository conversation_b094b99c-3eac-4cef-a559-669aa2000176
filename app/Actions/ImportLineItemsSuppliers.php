<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Enums\OrderStatus;
use App\Models\LineItem;
use App\Models\Order;
use App\Models\Supplier;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ImportLineItemsSuppliers
{
    use AsAction;

    public function validate(string $file)
    {
        $rows = ParseCsvFile::make()->handle($file);

        foreach ($rows as $index => $row) {
            $this->validateRow($row, $index + 1);
        }

        return $rows;
    }

    protected function validateRow(array $row, int $index): void
    {
        if (blank($row['order_tda_number']) || Order::where('number_portal', $row['order_tda_number'])->doesntExist()) {
            throw new \InvalidArgumentException("Row {$index} is missing or invalid 'order_tda_number'.");
        }

        if (blank($row['line_item_code']) || LineItem::where('number_portal', $row['line_item_code'])->doesntExist()) {
            throw new \InvalidArgumentException("Row {$index} is missing or invalid 'line_item_code'.");
        }

        if (
            LineItem::query()
                ->where('number_portal', $row['line_item_code'])
                ->whereHas('order', fn ($query) => $query->where('number_portal', $row['order_tda_number']))
                ->doesntExist()
        ) {
            throw new \InvalidArgumentException('Some of `order_number_platform`, `order_tda_number`, `line_item_code` is invalid');
        }

        if (blank($row['product_name'])) {
            throw new \InvalidArgumentException("Row {$index} is missing 'product_name'.");
        }

        if (blank($row['supplier']) || Supplier::whereRaw('LOWER(code) = ?', [strtolower($row['supplier'])])->doesntExist()) {
            throw new \InvalidArgumentException("Row {$index} is missing supplier or supplier does not exist.");
        }

        if (blank($row['supplier_order_number'])) {
            // throw new \InvalidArgumentException("Row {$index} is missing 'supplier order number'.");
        }

        if (blank($row['link_design'])) {
            throw new \InvalidArgumentException("Row {$index} is missing or invalid 'link_design'.");
        }
    }

    public function handle(string $filePath): void
    {
        $data = ParseCsvFile::make()->handle($filePath);
        $suppliers = Supplier::all()->keyBy(fn ($supplier) => strtolower(trim($supplier->code)));

        try {
            DB::beginTransaction();

            foreach ($data as $row) {
                $line = LineItem::where('number_portal', $row['line_item_code'])->first();

                $line->update([
                    'supplier_id' => $suppliers->get(strtolower(trim($row['supplier'])))->id,
                    'supplier_order_number' => $row['supplier_order_number'] ?? null,
                    'design_file_url' => $row['link_design'],
                    'status' => $line->isNew() ? LineItemStatus::InProduction : $line->status,
                ]);

                if ($line->order->isNew()) {
                    $line->order->update(['status' => OrderStatus::Processing]);
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
