<?php

namespace App\Actions;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Lorisleiva\Actions\Concerns\AsAction;

class ConfirmDraftOrder
{
    use AsAction;

    public function handle(User $user, Order $order): void
    {
        if ($user->cannot('confirmDraftOrder', $order)) {
            throw new AuthorizationException('You are not authorized to confirm this order.');
        }

        $order->update([
            'status' => OrderStatus::New,
            'seller_confirmed_at' => now(),
        ]);
    }
}
