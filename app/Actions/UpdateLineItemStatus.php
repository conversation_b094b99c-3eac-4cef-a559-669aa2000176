<?php

namespace App\Actions;

use App\Enums\QcLineItemStatus;
use App\Models\QcLineItemCheck;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateLineItemStatus
{
    use AsAction;

    public function handle(QcLineItemCheck $lineItemCheck, QcLineItemStatus $status): QcLineItemCheck
    {
        // Verify the order check is still in progress
        if (! $lineItemCheck->qcOrderCheck->isProcessing()) {
            throw ValidationException::withMessages([
                'line_item_check' => 'Cannot update line item status for completed order.',
            ]);
        }

        // Update the line item check status
        $lineItemCheck->update([
            'status' => $status->value,
            'checked_at' => now(),
        ]);

        return $lineItemCheck;
    }
}
