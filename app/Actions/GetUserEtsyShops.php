<?php

namespace App\Actions;

use App\Enums\Platform;
use App\Models\EtsyShop;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetUserEtsyShops
{
    use AsAction;

    public function handle(User $user): Collection
    {
        $shopIds = Order::query()
            ->whereAny(['seller_id', 'backup_seller_id'], $user->id)
            ->where('platform', Platform::Etsy)
            ->distinct('shop_id_platform')
            ->pluck('shop_id_platform');

        return EtsyShop::whereIn('id_etsy', $shopIds)->get();
    }
}
