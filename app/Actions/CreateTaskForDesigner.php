<?php

namespace App\Actions;

use App\Enums\TaskStatus;
use App\Models\Design;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateTaskForDesigner
{
    use AsAction;

    public function handle(User $designer, Design|Collection|array $designs, User $owner, string $title, ?string $comment = null): Task
    {
        // create new task for this design
        // assign designer to this task
        $task = Task::query()->create([
            'title'       => $title,
            'description' => $comment,
            'assignee_id' => $designer->id,
            'owner_id'    => $owner->id,
            'status'      => TaskStatus::Todo,
        ]);

        $task->designs()->attach($designs);

        return $task;
    }
}
