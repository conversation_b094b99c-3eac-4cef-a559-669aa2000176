<?php

namespace App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;

class ParseCsvFile
{
    use AsAction;

    public function handle(string $filePath): array
    {
        if (! file_exists($filePath) || ! is_readable($filePath)) {
            throw new \InvalidArgumentException("File does not exist or is not readable: {$filePath}");
        }

        $handle = fopen($filePath, 'r');
        if (! $handle) {
            throw new \RuntimeException("Unable to open file: {$filePath}");
        }

        // Read and remove BOM if present at the beginning of the file
        $firstBytes = fread($handle, 3);
        if ($firstBytes !== "\xEF\xBB\xBF") {
            // No BOM found, rewind to beginning
            rewind($handle);
        }

        $headers = fgetcsv($handle);
        if ($headers === false) {
            fclose($handle);
            throw new \RuntimeException("Unable to read CSV headers from file: {$filePath}");
        }

        // Convert headers to lowercase and replace spaces with underscores
        $headers = $this->normalizeHeaders($headers);

        $data = [];
        while (($row = fgetcsv($handle)) !== false) {
            $data[] = array_combine($headers, $row);
        }

        fclose($handle);

        return $data;
    }

    private function normalizeHeaders(array $headers): array
    {
        return array_map(function ($header) {
            // Remove BOM (Byte Order Mark) from the header if present
            $header = $this->removeBom($header);

            return str_replace(' ', '_', strtolower(trim($header)));
        }, $headers);
    }

    private function removeBom(string $text): string
    {
        // Remove UTF-8 BOM (\u{FEFF} or bytes EF BB BF)
        if (str_starts_with($text, "\xEF\xBB\xBF")) {
            return substr($text, 3);
        }

        // Remove UTF-16 BE BOM (\u{FEFF} as UTF-16 BE)
        if (str_starts_with($text, "\xFE\xFF")) {
            return substr($text, 2);
        }

        // Remove UTF-16 LE BOM (\u{FFFE} as UTF-16 LE)
        if (str_starts_with($text, "\xFF\xFE")) {
            return substr($text, 2);
        }

        return $text;
    }
}
