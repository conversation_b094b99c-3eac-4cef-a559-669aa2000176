<?php

namespace App\Actions;

use App\Enums\LineItemStatus;
use App\Models\QcSession;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateLineItemsToPacking
{
    use AsAction;

    public function handle(QcSession $qcSession): void
    {
        // Load relationships to avoid lazy loading violations
        $qcSession->load([
            'orderChecks.lineItemChecks.lineItem',
        ]);

        // Get line item checks with their statuses from this QC session
        foreach ($qcSession->orderChecks as $orderCheck) {
            foreach ($orderCheck->lineItemChecks as $lineItemCheck) {
                // Only update to Packing status if QC status is Ready
                if ($lineItemCheck->isReady()) {
                    $lineItem = $lineItemCheck->lineItem;

                    // Update to Packing status only if currently in production
                    if ($lineItem->isInProduction()) {
                        $lineItem->update(['status' => LineItemStatus::Packing]);
                    }
                }
                // Do nothing if status is 'missing' - line item keeps its current status
            }
        }
    }
}
