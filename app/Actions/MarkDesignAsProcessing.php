<?php

namespace App\Actions;

use App\Enums\DesignStatus;
use App\Models\Design;
use Lorisleiva\Actions\Concerns\AsAction;

class MarkDesignAsProcessing
{
    use AsAction;

    public function handle(Design $design): void
    {
        if (! $design->isTodo()) {
            throw new \InvalidArgumentException('Design must be in Todo status to be marked as processing.');
        }

        $design->update([
            'status' => DesignStatus::Processing,
            'processing_at' => now(),
        ]);
    }
}
