<?php

namespace App\Actions;

use App\Enums\OrderCancelRequestStatus;
use App\Models\OrderCancelRequest;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessOrderCancelRequest
{
    use AsAction;

    public function handle(OrderCancelRequest $cancelRequest, User $processor, bool $approve, ?string $feedbackNote = null): OrderCancelRequest
    {
        // Validate that the user can process cancel requests (Operator or Manager)
        if (! $processor->isOperator() && ! $processor->isManager() && ($processor->isSeller() && ! $processor->isInchargeOfOrder($cancelRequest->order))) {
            throw ValidationException::withMessages([
                'user' => 'Only operators and managers can process this cancel requests.',
            ]);
        }

        // Validate that the request is still pending
        if (! $cancelRequest->isPending()) {
            throw ValidationException::withMessages([
                'request' => 'This cancel request has already been processed.',
            ]);
        }

        // If rejecting, feedback note is required
        if (! $approve && empty(trim($feedbackNote))) {
            throw ValidationException::withMessages([
                'feedback_note' => 'Feedback note is required when rejecting a cancel request.',
            ]);
        }

        // Process the request
        $status = $approve ? OrderCancelRequestStatus::Approved : OrderCancelRequestStatus::Rejected;

        $cancelRequest->update([
            'status' => $status,
            'processed_by' => $processor->id,
            'processed_at' => now(),
            'feedback_note' => $feedbackNote,
        ]);

        // If approved, update the order status to cancel
        if ($approve) {
            CancelOrder::make()->handle($cancelRequest->order);
        }

        return $cancelRequest;
    }
}
