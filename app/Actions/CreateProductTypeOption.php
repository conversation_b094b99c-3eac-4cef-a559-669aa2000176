<?php

namespace App\Actions;

use App\Models\ProductType;
use App\Models\ProductTypeOption;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateProductTypeOption
{
    use AsAction;

    public function handle(ProductType $productType, string $optionName, array $values = [], bool $isRequired = false): ProductTypeOption
    {
        // Calculate the next sort order
        $nextSortOrder = $productType->options()->max('sort_order') + 1;

        // Create the product type option
        $option = $productType->options()->create([
            'name' => $optionName,
            'is_required' => $isRequired,
            'sort_order' => $nextSortOrder,
        ]);

        // Create the option values in separate table
        foreach ($values as $index => $value) {
            $option->values()->create([
                'value' => $value,
                'sort_order' => $index,
            ]);
        }

        return $option->fresh(['values']);
    }
}
