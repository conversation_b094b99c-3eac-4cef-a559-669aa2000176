<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderImportRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'platform' => $this->platform,
            'status' => $this->status,
            'orders_count' => count($this->raw_data['orders_search']['orders'] ?? []),
            'created_at' => $this->created_at,
            'processed_at' => $this->processed_at,
            'error_message' => $this->error_message,
        ];
    }
}
