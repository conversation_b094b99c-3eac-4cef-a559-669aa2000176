<?php

namespace App\Http\Controllers\Api;

use App\Actions\ProcessOrdersImportRequest;
use App\Enums\Platform;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ImportEtsyOrdersController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        // validate the body of the request must not be empty
        if (blank($request->input())) {
            return response()->json([
                'error' => 'The request body must not be empty.',
            ], 422);
        }

        $importRequest = auth('sanctum')->user()->ordersImportRequest()->create([
            'platform' => Platform::Etsy->value,
            'raw_data' => $request->input(),
            'hash' => md5($request->getContent()),
        ]);

        // Calculate existing order numbers before processing
        $data = $request->input();
        $seller = auth('sanctum')->user();
        $existingOrderNumbers = [];

        if (isset($data['orders_search']['orders'])) {
            foreach ($data['orders_search']['orders'] as $rawOrder) {
                $existingOrder = $seller
                    ->importedOrders()
                    ->where('id_platform', $rawOrder['order_id'])
                    ->where('shop_id_platform', $rawOrder['business_id'])
                    ->first();

                if ($existingOrder) {
                    $existingOrderNumbers[] = $existingOrder->order_number;
                }
            }
        }

        try {
            ProcessOrdersImportRequest::make()->handle($importRequest);
        } catch (\Throwable $e) {
            report($e);

            return response()->json([
                'error' => $e->getMessage(),
            ], 400);
        }

        return response()->json([
            'orders_import_request' => ['id' => $importRequest->id],
            'orders_imported_count' => $importRequest->orders()->count(),
            'imported_order_numbers' => $importRequest->orders()->pluck('order_number')->all(),
            'existing_order_numbers' => $existingOrderNumbers,
        ]);
    }
}
