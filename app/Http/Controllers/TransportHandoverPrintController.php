<?php

namespace App\Http\Controllers;

use App\Models\TransportHandover;

class TransportHandoverPrintController extends Controller
{
    public function show(TransportHandover $transportHandover)
    {
        $this->authorize('view', $transportHandover);

        // Load the 3PL relationship
        $transportHandover->load('thirdPartyLogistic');

        // Parse tracking numbers from the stored text
        $trackingNumbers = $transportHandover->getParsedTrackingNumbers();

        // Get shipments with their line items and orders
        $shipments = \App\Models\Shipment::whereIn('tracking_number', $trackingNumbers)
            ->where('packer', 'TDA')
            ->with(['lineItems.order'])
            ->get();

        // If no shipments found, create empty collection to avoid errors in view
        if ($shipments->isEmpty()) {
            $shipments = collect();
        }

        // Attach shipments to the transport handover for the view
        $transportHandover->setRelation('shipments', $shipments);

        return view('transport-handover.print', [
            'transportHandover' => $transportHandover,
        ]);
    }
}
