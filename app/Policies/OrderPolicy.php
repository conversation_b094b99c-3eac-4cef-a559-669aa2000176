<?php

namespace App\Policies;

use App\Models\Order;
use App\Models\User;

class OrderPolicy
{
    public function view(User $user, Order $order): bool
    {
        if ($user->isOperator() || $user->isManager()) {
            return true;
        }

        return $user->isSeller() && ($user->id === $order->seller_id || $user->id === $order->backup_seller_id);
    }

    public function update(User $user, Order $order): bool
    {
        if ($user->isSeller()) {
            return false;
        }

        return true;
    }

    public function delete(User $user, Order $order): bool
    {
        if (! $order->isDraft()) {
            return false;
        }

        if ($user->id === $order->seller_id || $user->id === $order->backup_seller_id) {
            return true;
        }

        return $user->isManager();
    }

    public function confirmDraftOrder(User $user, Order $order): bool
    {
        return $user->isSeller()
            && $order->isDraft()
            && ($user->id === $order->seller_id || $user->id === $order->backup_seller_id);
    }

    public function cancelDraftOrder(User $user, Order $order): bool
    {
        if ($user->isManager()) {
            return $order->isDraft();
        }

        return $user->isSeller()
            && $order->isDraft()
            && ($user->id === $order->seller_id || $user->id === $order->backup_seller_id);
    }

    public function assgignBackupSeller(User $user, Order $order): bool
    {
        if ($user->isManager() && $order->isDraft()) {
            return true;
        }

        return false;
    }
}
