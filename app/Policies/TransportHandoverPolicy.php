<?php

namespace App\Policies;

use App\Models\TransportHandover;
use App\Models\User;

class TransportHandoverPolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        //
    }

    public function view(User $user, TransportHandover $transportHandover)
    {
        return $user->isOperator() || $user->isManager();
    }

    public function edit(User $user, TransportHandover $transportHandover)
    {
        return $transportHandover->isDraft();
    }
}
