<?php

namespace App\Policies;

use App\Models\Comment;
use App\Models\User;

class CommentPolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        //
    }

    public function delete(User $user, Comment $comment): bool
    {
        // return true;
        return $user->id === $comment->user_id
            && $comment->created_at->diffInMinutes(now(), absolute: true) <= 1;
    }
}
