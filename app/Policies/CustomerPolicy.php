<?php

namespace App\Policies;

use App\Models\User;

class CustomerPolicy
{
    public function before(User $user, $ability)
    {
        // Allow all abilities for managers
        if ($user->isManager()) {
            return true;
        }

    }

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function update(User $user): bool
    {
        return false;
    }
}
