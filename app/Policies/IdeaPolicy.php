<?php

namespace App\Policies;

use App\Models\Idea;
use App\Models\User;

class IdeaPolicy
{
    public function before(User $user, $ability)
    {
        if ($user->isManager()) {
            return true;
        }
    }

    public function view(User $user, Idea $idea): bool
    {
        return $user->id === $idea->user_id;
    }

    public function update(User $user, Idea $idea): bool
    {
        return $user->id === $idea->user_id;
    }

    public function delete(User $user, Idea $idea): bool
    {
        return $user->id === $idea->user_id;
    }

    public function replicate(User $user, Idea $idea): bool
    {
        return $idea->isSimple();
    }
}
