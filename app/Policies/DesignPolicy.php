<?php

namespace App\Policies;

use App\Models\Design;
use App\Models\User;

class DesignPolicy
{
    public function submit(User $user, Design $design): bool
    {
        return true;
        return ($design->isProcessing() || $design->isNeedRevision()) && $design->user_id === $user->id;
    }

    public function approve(User $user, Design $design): bool
    {
        return true;
        return $design->isSubmitted() && ($design->idea->user_id === $user->id || $user->isManager());
    }

    public function requestRevision(User $user, Design $design): bool
    {
        return true;
        return $design->isSubmitted() && ($design->idea->user_id === $user->id || $user->isManager());
    }

    public function delete(User $user, Design $design): bool
    {
        return true;
        return $design->isTodo() && ($design->idea?->user_id === $user->id || $user->isManager());
    }
}
