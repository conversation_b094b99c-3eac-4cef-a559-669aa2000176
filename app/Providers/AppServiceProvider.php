<?php

namespace App\Providers;

use App\Filament\Forms\TagsInput;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Facades\FilamentTimezone;
use Filament\Support\Facades\FilamentView;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use SocialiteProviders\LaravelPassport\Provider;
use SocialiteProviders\Manager\SocialiteWasCalled;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        $this->configureUrls();

        $this->configureModels();

        $this->configureCommands();

        $this->configureFilament();

        $this->configureTdaPassport();
    }

    protected function configureCommands(): void
    {
        DB::prohibitDestructiveCommands($this->app->isProduction());
    }

    protected function configureUrls(): void
    {
        URL::forceHttps(app()->isProduction());
    }

    protected function configureModels(): void
    {
        Model::unguard();

        Model::shouldBeStrict(! app()->isProduction());
    }

    protected function configureFilament(): void
    {
        $this->customFilamentStyles();

        Table::configureUsing(function (Table $table): void {
            $table
                ->defaultPaginationPageOption(25)
                ->defaultDateTimeDisplayFormat('Y-m-d H:i:s')
                ->defaultDateDisplayFormat('Y-m-d')
                ->defaultNumberLocale('en')
                ->deferFilters(false);
        });

        Table::macro('minimal', fn () => $this->searchable(false)->paginated(false)->filters([]));

        TextColumn::macro('recordMoney', function (): TextColumn {
            return $this->money(
                currency: fn (Model $record) => $record->currency_code ?? null,
                divideBy: fn (Model $record) => strtolower($record->currency_code) === 'vnd' ? 1 : 100,
            )->alignRight()->weight('medium');
        });

        Schema::configureUsing(function (Schema $schema): void {
            $schema
                ->defaultDateTimeDisplayFormat('Y-m-d H:i:s')
                ->defaultDateDisplayFormat('Y-m-d')
                ->defaultNumberLocale('en');
        });

        TextEntry::macro('recordMoney', function (): TextEntry {
            return $this->money(
                currency: fn (Model $record) => $record->currency_code ?? null,
                divideBy: fn (Model $record) => strtolower($record->currency_code) === 'vnd' ? 1 : 100,
            )->weight('medium');
        });

        Section::configureUsing(fn (Section $section) => $section->columnSpanFull());

        TagsInput::configureUsing(function (TagsInput $tagsInput): TagsInput {
            return $tagsInput
                ->splitKeys([',']);
        });

        SpatieMediaLibraryFileUpload::configureUsing(function (SpatieMediaLibraryFileUpload $fileUpload) {
            return $fileUpload->disk(config('media-library.disk_name'));
        });

        FilamentTimezone::set(config('app.user_timezone'));
    }

    protected function customFilamentStyles(): void
    {
        FilamentView::registerRenderHook(
            PanelsRenderHook::HEAD_END,
            fn (): string => <<<'CSS'
                <style>
                html {
                    font-size: 14px
                }
                aside {
                    background: #fff
                }
                .bg-color-danger {
                    background: var(--danger-500)
                }
                </style>
                CSS
        );
    }

    protected function configureTdaPassport(): void
    {
        Event::listen(
            function (SocialiteWasCalled $event) {
                $event->extendSocialite(
                    'tda_passport',
                    Provider::class
                );
            }
        );
    }
}
