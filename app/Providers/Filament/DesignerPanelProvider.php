<?php

namespace App\Providers\Filament;

use App\Filament\Designer\Widgets\DesignStatusWidget;
use App\Livewire\Login;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets\AccountWidget;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class DesignerPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('designer')
            ->path('designer')
            ->spa()
            ->spaUrlExceptions([
                '*/login*',
            ])
            ->darkMode(false)
            ->favicon('/favicon.svg')
            ->brandName('Designer Dashboard')
            ->colors([
                'primary' => Color::Teal,
            ])
            ->login(Login::class)
            ->discoverResources(in: app_path('Filament/Designer/Resources'), for: 'App\Filament\Designer\Resources')
            ->discoverPages(in: app_path('Filament/Designer/Pages'), for: 'App\Filament\Designer\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Designer/Widgets'), for: 'App\Filament\Designer\Widgets')
            ->widgets([
                AccountWidget::class,
                DesignStatusWidget::class,
            ])
            ->readOnlyRelationManagersOnResourceViewPagesByDefault(false)
            ->sidebarCollapsibleOnDesktop()
            ->font('Inter')
            ->maxContentWidth('full')
            ->breadcrumbs(false)
            ->viteTheme('resources/css/filament/backoffice/theme.css')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
