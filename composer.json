{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/livewire-starter-kit", "type": "project", "description": "The official Laravel starter kit for Livewire.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "bangnokia/laravel-bunny-storage": "*", "filament/filament": "^4.0.0", "filament/spatie-laravel-media-library-plugin": "^4.0", "laravel/framework": "v12.19.3", "laravel/nightwatch": "^1.10", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.21", "laravel/tinker": "^2.10.1", "lorisleiva/laravel-actions": "^2.9", "phiki/phiki": "^1.1", "socialiteproviders/laravelpassport": "^4.3", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-medialibrary": "^11.13", "ext-zip": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/boost": "^1.0", "laravel/pail": "^1.2.2", "laravel/pint": "^1.23", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/logging.sqlite') || touch('database/logging.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" --names=server,queue,logs"], "test": ["@php artisan config:clear --ansi", "@php artisan test --parallel"], "pint": ["./vendor/bin/pint --parallel"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}, "platform": {"php": "8.3"}}, "minimum-stability": "beta", "prefer-stable": true}